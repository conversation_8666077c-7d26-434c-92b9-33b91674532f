<?php

namespace Apimio\Gallery\Models;

use Apimio\Gallery\Models\File;
use Apimio\Gallery\Rules\FolderNameRule;
use App\User;
use Font<PERSON>ib\Table\Type\name;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpKernel\HttpCache\Store;
use function PHPUnit\Framework\isEmpty;

class Folder extends Model
{
    protected $guarded=[];
    use HasFactory;

    private $data, $user, $folder;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        static::creating(function ($model) {
            if(!isset($model->organization_id)) {
                if (Auth::check()) {
                    $model->organization_id = Auth::user()->organization_id;
                }
            }
        });

        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', Auth::user()->organization_id);
            }
        });

    }


    /**
     * Scope a query to only include default folders.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIsDefault($query) {
        return $query->where('is_default',1);
    }

    private function rules()
    {
        return [
            'folder_name' => ['required','max:255', new FolderNameRule($this->data) ]
        ];
    }

    public function validation()
    {
        $validator = Validator::make($this->data, $this->rules());
        return $validator;
    }

    public function get_folder_with_files($id): object
    {
        $folder = $this->with('files')->findOrFail($id);
        return $folder;
    }

    public function set_data(array $data)
    {
        $this->data = $data;
        return $this;
    }

    public function set_user($user) {
        $this->user = $user;
        return $this;
    }

    public function get_folder($id) {
        $this->folder = $this->findOrFail($id);
        return $this;
    }

    /**
     * Get data attribute.
     *
     * @return $this
     */
    public function get_user() {
        $orgId   =   $this->organization_id;
        return  User::whereHas('organizations', function ($query) use ($orgId) {
            $query->where('organization_id', $orgId);
        })->first();
    }

    /**
     * @param $error_callback
     * @param $success_callback
     * @return mixed
     */
    public function store($error_callback, $success_callback)
    {
        $validation = $this->validation();

        if ($validation->fails()) {
            return $error_callback($validation->errors());
        }
        try {
            if (isset($this->data["id"])) {
                $obj = $this->find($this->data["id"]);
            } else {
                $obj = $this;
            }


            $obj->name = $this->data['folder_name'];
            $obj->folder_id = isset($this->data['folder_id']) ? $this->data['folder_id'] : null;
            $obj->save();
            $this->share($obj->id);
            return $success_callback($obj);
        }
        catch (\Exception $e) {
            return $error_callback(["main" => 'Something went wrong, please try again.']);
        }
    }

    public function share($folderId){
        try{
            $enc_id = encrypt($folderId);
            $link = "guests/" . $enc_id;
            Folder::where('id', $folderId)->update([
                'link' => $link,
                'updated_at' => now()]);
        }
        catch(Exception $e){
            return redirect()->back()->withErrors("share link cannot be generated");
        }
    }

    public function delete_by_id($id)
    {
        $folder = Folder::findorfail($id);
        $folder->delete();
        return redirect()->back()->withSuccess('Folder deleted');
    }

    public function getParents($id,$hierarchy){
        $folder = Folder::findorfail($id);
        $hierarchy[]= [$folder->name,$folder->id];
        if($folder->folder_id){
            $hierarchy = $this->getParents($folder->folder_id,$hierarchy);
        }
        return $hierarchy;
    }

    public function format_folders($id = null,$search = null) {
        $folders = array();
        $parent_array = array();
        $temp_array = array();

        $all_folders = Folder::query();
        if (isset($search)) {
            $all_folders = $all_folders->where('name','like','%'. $search . '%');
        }
        if (isset($id)) {
            //getting child folders
            $all_folders = $all_folders->where('id',$id)->whereNull('folder_id')->get();
            if (count($all_folders) == 0) {
                //getting nested child folders
                $all_folders = Folder::where('id',$id)->whereNotNull('folder_id')->get();
            }
        } else {
            $all_folders = $all_folders->whereNull('folder_id')->get();
        }

        foreach($all_folders as $folder) {
            $child_folders = Folder::where('folder_id',$folder->id)->get();
            if (count($child_folders) > 0) {
                $temp_array[] = $this->getChildFolders($folder);
            }

            if(Folder::where('folder_id',$folder->id)->count()  == 0){
                $parent_array[] = [
                    'id'=>$folder->id,
                    'title'=>$folder->name,
                ];
            }
            $folders =   array_merge($temp_array,$parent_array);
        }
        return $folders;
    }

    public function getChildFolders($folder) {
        $temp_array_child = array();
        $child_folders = Folder::query()
            ->where('folder_id',$folder->id)
            ->get();

        if (!empty($child_folders)){
            foreach ($child_folders as $child){
                $temp_array_child['subs'][] = $this->getChildFolders($child);
            }
        }

        $parent_array = [
            'id'=>$folder->id,
            'title'=>$folder->name,
        ];

        return array_merge($temp_array_child,$parent_array);
    }

    public function create_default_folder($org)
    {
        /*Saving default folder*/
        $folder = new Folder();
        $folder->name =  $org->name;
        $folder->organization_id = $org->id;
        $folder->is_default = 1;
        $folder->save();
        //saving folder link
        $this->share($folder->id);
    }

    public function create_social_media_folder($org,$names)
    {
        foreach ($names as $name) {
            /*Saving social media folders*/
            $folder = new Folder();
            $folder->name =  $name;
            $folder->organization_id = $org->id;
            $folder->is_default = 1;
            $folder->type = 'social_media';
            $folder->save();
            //saving folder link
            $this->share($folder->id);
        }

    }

    public function delete_folder()
    {
        $files = $this->folder->files()->get();
        $file_obj = new File();
        foreach ($files as $file) {
            $file_obj->get_file($file->id)->delete_file();
        }
        $this->folder->delete();
    }

    /*
  *
  *  RELATIONSHIPS
  *
  * */

    public function files() {
        return $this->belongsToMany(File::class);
    }



}
