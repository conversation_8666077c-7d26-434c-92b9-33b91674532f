<?php

namespace Apimio\Gallery\components;

use Illuminate\View\Component;

class AssetCard extends Component
{
    public $files;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($files)
    {
        $this->files = $files;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('gallery::components.asset-card');
    }
}
