<?php

namespace Apimio\MappingConnectorPackage\components;

use Illuminate\Support\Facades\Auth;
use Illuminate\View\Component;

class MappingAddAttribute extends Component
{
    public  $session_data;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->session_data = $data;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('mapping::components.mapping-add-attribute');
    }
}
