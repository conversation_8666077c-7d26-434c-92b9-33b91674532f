<?php

namespace Apimio\Gallery\components;

use Apimio\Gallery\Models\Folder;
use Illuminate\View\Component;

class ImageUploader extends Component
{
    public $folderId;
    public $productId;
    public $folders_list;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($folderId = null, $productId = null)
    {

        $this->folderId = $folderId;
        $this->productId = $productId;
        $this->folders_list = (new Folder())->format_folders($this->folderId);
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('gallery::components.image-uploader');
    }
}
