<?php

namespace Apimio\MappingConnectorPackage\components\alerts;

use Illuminate\View\Component;

class TemplateDelete extends Component
{
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('mapping::components.alerts.template-delete');
    }
}
