<?php


namespace Apimio\MappingConnectorPackage\Http\Controllers;

use Apimio\MappingConnectorPackage\classes\MappingConversion;
use Apimio\MappingConnectorPackage\models\Template;
use Apimio\MappingConnectorPackage\models\Vlookup;
use App\Classes\Facades\UnitFacade;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Product\ProductController;
use App\Imports\ProductsImport;
use App\Models\Product\Category;
use App\Models\Product\Version;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\View;
use Maatwebsite\Excel\Facades\Excel;


class MappingFieldController extends Controller
{
    protected array $data;


    public function set_data($data)
    {
        $this->data = $data;
    }


    /**
     * @param $input_array_data
     * @return string
     */
    function create_json_file($products, $set_data = [])
    {
        $fetch_products = $this->fetch_apimio_products($products, true, ['data_required' => $set_data]);
        $encode_data = json_encode($fetch_products);
        $fileName = time() . '_datafile.json';
        Storage::disk('s3')->put('mapping_fields/upload/json/' . $fileName, $encode_data);
        return 'mapping_fields/upload/json/' . $fileName;
    }


    public function mapping_view(array $input_array_data, array $output_array, array $data_required)
    {
        $mapping = new MappingConversion();
        $template_attributes = array();
        $mapping_type = $data_required['template_method_type'] ?? null;

        $templates = collect();
        $data = [];

        //record saving in json file temporarily
        $data['file_path'] = null;
        if (isset($data_required['products'])) {
            $data['file_path'] = $this->create_json_file($data_required['products'], $data_required);
        }


        if (isset($data_required['sync']) && $data_required['sync'] == false) {
            if ($data_required['template_method_type'] != 'import') {
                $data = array_merge($data, [
                    'input_array' => $input_array_data,
                    'output_array' => $output_array,
                    'data_required' => $data_required,
                    'template_attributes' => $template_attributes,
                ]);

                $data = $mapping->temp_mapping_view($data);

                //template for exist channels

                $templates = $templates->merge(Template::default_template(
                    $data_required,
                    function ($error) {
                        return false;
                    },
                    function ($success) {
                        return $success;
                    }
                ));

                Session::put('data', $data);
            } else {
                $data = Session::get('data');
                //                $templates->push(Template::apimio_default_template(
                //                    $data_required,function ($error){
                //                    return false;
                //                },
                //                function ($success){
                //                        return $success;
                //                }));
            }

            if (isset($data['data_required']['organization_id']) && $data['data_required']['template_method_type']) {
                $own_templates = Template::where('organization_id', $data['data_required']['organization_id'])->where('type', $data['data_required']['template_method_type'])->get();
                foreach ($own_templates as $tem) {
                    $templates->push($tem);
                }

                if ($templates->count() > 0) {
                    if (isset($data_required['template_method_type']) && $data_required['template_method_type'] == 'shopify') {
                        $request = new Request();
                        $request->setMethod('POST');
                        if (!empty($templates) && count($templates) > 1) {
                            $template_id = $templates->last()->id ?? null;
                            $request->request->add(['template' => $template_id]);
                        } else {
                            $request->request->add(['template' => "shopify-default"]);
                        }
                        return $this->mapping_data($request);
                    }
                    return View::make('mapping::index', compact('templates', 'mapping_type'));
                    // return View::make('mapping::mappingreact', compact('templates'));
                } else {
                    return view('mapping::mappingreact', compact('data', 'mapping_type'));
                }
            }
        } else {
            $template = Template::find($data_required['template_id'] ?? null);
            if (empty($template)) {
                $template = Template::default_template(
                    $data_required,
                    function ($error) {
                        return false;
                    },
                    function ($success) {
                        return $success;
                    }
                );
                if (isset($template[0])) {
                    $template = $template[0];
                }
            }
            if ($data_required['template_method_type'] === 'shopify') {
                $template_attributes = $this->fetch_template_in_array($template ?? null);
                $data['nodes'] = $template_attributes['template'] ?? [];
                $data['organization_id'] = $data_required['organization_id'] ?? null;
                $data['template_method_type'] = $data_required['template_method_type'];
            } else if ($data_required['template_method_type'] === 'export') {
                $data = $data_required['request_data'] ?? null;
                unset($data_required['request_data']);
                $data['request_data'] = $data_required;
                $data['export_type'] = $data_required['export_type'] ?? null;
                $data['products'] = $input_array_data;
            }
            $request = new Request($data);

            return $this->mapping_convert($request);
        }
    }


    /**
     * fetch template selected data
     *
     * @param Request $request
     *
     */
    function mapping_data(Request $request)
    {
        $data = session()->get('data');
        if (!isset($request->create_template)) {
            if (isset($request->template) && in_array($request->template, ["apimio-default", "shopify-default", "magento-default"])) {

                $data['data_required']['selected_template'] = $request->template ?? null;

                $template_obj = Template::default_template(
                    $data['data_required'],
                    function ($error) {
                        return false;
                    },
                    function ($success) {
                        return $success;
                    }
                );
            } else {
                $template_obj = Template::find($request->template);
            }
        }

        if (!empty($template_obj)) {
            $template_attributes = $this->fetch_template_in_array($template_obj);
        } else {
            $template_attributes = array();
        }

        $data['template_attributes'] = $template_attributes;

        return view('mapping::mappingreact', compact('data'));
    }


    /**
     * delete template
     *
     * @param Request $request
     *
     */
    function delete_template(Request $request)
    {
        $template_obj = Template::find($request->template_delete);
        if ($template_obj) {
            $template_obj->delete();
            $msg = "Template delete successfully.";
        } else {
            $msg = "Something went wrong. please try again later.";
            return response()->json(['msg' => $msg], 500);
        }

        return response()->json(['msg' => $msg], 200);
    }


    /**
     * To save or update the template
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|object
     */
    function save_template(Request $request)
    {

        $default_template_name_FLAG = false;

        $default_temp_names = [
            'Shopify Default',
            'Magento Default',
        ];


        $template_obj = new Template();
        $template_obj_count = $template_obj->where('organization_id', $request->organization_id)
            ->where(function ($query) use ($request) {

                if (is_array($request->version)) {
                    foreach ($request->version as $version) {
                        $query->orWhereJsonContains('version_id', $version);
                    }
                } else {
                    $query->whereJsonContains('version_id', $request->version);
                }
            })
            ->where(function ($query) use ($request) {
                if (is_array($request->catalog)) {
                    foreach ($request->catalog as $catalog) {
                        $query->orWhereJsonContains('channel_id', $catalog);
                    }
                } else {
                    $query->whereJsonContains('channel_id', $request->catalog);
                }
            })
            ->where('name', $request->temp_name)
            ->where('type', $request->template_method_type)
            ->where('export_type', $request->export_type);




        if (isset($request->temp_id)) {
            $temp_ids = is_array($request->temp_id) ? $request->temp_id : [$request->temp_id];
            $template_obj_count = $template_obj_count->whereNotIn('id', $temp_ids);
        }

        $template_obj_count = $template_obj_count->get();



        if (in_array($request->temp_name, $default_temp_names) && $request->template_method_type == 'export') {
            $default_template_name_FLAG = true;
        }

        if (count($template_obj_count) > 0 || (isset($request->temp_status) && $request->temp_status == "on" && $default_template_name_FLAG)) {
            // Check if this is an Inertia request
            if ($request->header('X-Inertia')) {
                return back()->withErrors(['template_error' => 'Template name already exist in this organization']);
            }
            return response()->json([
                'status' => 'error',
                'data' => "",
                'message' => "Template name already exist in this organization",
            ], 500);
        }

        if(isset($request->temp_status) && $request->temp_status == "on"){
            $template_obj = $template_obj->set_data($request->all())->store(function ($tem_obj) use ($request) {
                $msg = "";
                if (!isset($request->temp_id)) {
                    $msg = "Template saved successfully.";
                } else {
                    $msg = "Template update successfully.";
                }
                return response()->json([
                    'status' => 'success',
                    'data' => $tem_obj->toArray(),
                    'message' => $msg,
                ], 200);
            }, function ($error) {
                return response()->json([
                    'status' => 'error',
                    'data' => "",
                    'message' => $error,
                ], 500);
            });

            return $template_obj;
        }
    }


    /**
     * apply template mapping
     *
     * @param Request $request
     *
     */
    function apply_template($id)
    {
        $create_mapping_template_request = array();
        $data = session()->get('data');
        $create_mapping_template_request['organization_id'] = $data['data_required']['organization_id'];
        $create_mapping_template_request['template_method_type'] = $data['data_required']['template_method_type'];
        $create_mapping_template_request['import_csv'] = "Start Importing";
        $create_mapping_template_request['file_path'] = $data['file_path'];
        $template = Template::find($id);
        if ($template) {
            $create_mapping_template_request['temp_name'] = $template->name ?? null;
            $create_mapping_template_request['temp_id'] = $id ?? null;
            $create_mapping_template_request['version'] = $template->version_id ?? null;
            $create_mapping_template_request['status'] = $template->product_status ?? null;
            $create_mapping_template_request['catalog'] = $template->channel_id ?? null;
            $create_mapping_template_request['nodes'] = json_decode($template->payload, true) ?? null;
        }

        $custom_request = new Request();
        $custom_request->setMethod('POST');
        $custom_request->request->add($create_mapping_template_request);
        $product_controller = new ProductController();
        return $product_controller->import_csv($custom_request);
    }


    /**
     * @param $template_obj
     * @return array
     */
    function fetch_template_in_array($template_obj)
    {
        $template_attributes = array();
        if ($template_obj != null) {
            $template = json_decode($template_obj->payload, true);
            $template_attributes = [
                'type' => isset($template_obj->type) ? $template_obj->type : null,
                'export_type' => isset($template_obj->export_type) ? $template_obj->export_type : null,
                'version_id' => isset($template_obj->version_id) ? $template_obj->version_id : null,
                'channel_id' => isset($template_obj->channel_id) ? $template_obj->channel_id : null,
                'name' => isset($template_obj->name) ? $template_obj->name : null,
                'id' => isset($template_obj->id) ? $template_obj->id : null,
                'template' => isset($template) ? $template : null
            ];
        }


        return $template_attributes;
    }


    /**
     * fetch new row components in import step 3
     *
     * @param Request $request
     *
     */
    public function add_row(Request $request)
    {
        try {
            $request_data['input_array'] = json_decode($request->input_array, true);
            $request_data['output_array'] = json_decode($request->output_array, true);
            $request_data['row_count'] = $request->row_count ?? null;
            $row_nodes = $request->row_node ?? null;
            $request_data['formula'] = $request->formula ?? null;
            $request_data['method_type'] = $request->method_type ?? null;

            $chunk = $request->input_row_selector ?? null;
            $request_data['input_row_selector'] = $chunk;
            $request_data['family'] = '';
            $request_data['vlookups'] = Vlookup::all();


            $request_data['row_node'] = $row_nodes;

            $main_output = [];

            if (isset($chunk) && !empty($chunk)) {
                foreach ($chunk as $chunk_family => $chunk_attributes) {
                    if (is_iterable($chunk_attributes)) {
                        foreach ($chunk_attributes as $input_row_selector) {
                            $request_data['input_row_selector'] = $input_row_selector;
                            $request_data['family'] = $chunk_family;
                            $output = $this->render_add_row_components($request_data);
                            $request_data['row_count']++;
                            $main_output[] = $output;
                        }
                    }
                }
            } elseif (is_iterable($row_nodes)) {
                foreach ($row_nodes as $row_node) {
                    $request_data['row_node'] = $row_node;
                    $output = $this->render_add_row_components($request_data);
                    $request_data['row_count']++;
                    $main_output[] = $output;
                }
            } else {
                $output = $this->render_add_row_components($request_data);
                $main_output[] = $output;
            }
            return response()->json(['output' => $main_output]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()]);
        }
    }


    public function render_add_row_components($request_data)
    {
        $output = View::make("mapping::components.from-field")
            ->with('input_array', $request_data['input_array'])
            ->with('formula', (isset($request_data['row_node']['with_formula'])) ? $request_data['row_node']['with_formula'] : $request_data['formula'])
            ->with('row_count', $request_data['row_count'])
            ->with('row_node', $request_data['row_node'])
            ->with('input_row_selector', $request_data['input_row_selector'])
            ->with('family', $request_data['family'])
            ->with('method_type', $request_data['method_type'])
            ->render();


        $output .= View::make("mapping::components.formula-field")
            ->with('row_count', $request_data['row_count'])
            ->with('formulas', $this->set_formula_csv())
            ->with('row_node', $request_data['row_node'])
            ->with('method_type', $request_data['method_type'])
            ->render();


        $output .= View::make("mapping::components.to-field")
            ->with('input_array', $request_data['input_array'])
            ->with('output_array', $request_data['output_array'])
            ->with('formula', (isset($request_data['row_node']['with_formula'])) ? $request_data['row_node']['with_formula'] : $request_data['formula'])
            ->with('row_count', $request_data['row_count'])
            ->with('row_node', $request_data['row_node'])
            ->with('input_row_selector', $request_data['input_row_selector'])
            ->with('method_type', $request_data['method_type'])
            ->with('expand_positions', $this->fetch_expand_positioning())
            ->with('vlookups', $request_data['vlookups'])
            ->render();

        return $output;
    }


    /**
     * fetch new variants option components
     *
     * @param Request $request
     *
     */
    public function mapping_add_variant_option(Request $request)
    {
        try {

            $input_array = json_decode($request->input_array, true);
            $template_option = $request->template_option;
            $output = View::make("mapping::components.variants-option")
                ->with('input_array', $input_array)
                ->with('template_option', $template_option)
                ->render();

            return response()->json(['output' => $output]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()]);
        }
    }


    /**
     * fetch selected formula fields
     *
     * @param Request $request (formula)
     *
     */
    public function fetch_formula_fields(Request $request)
    {
        try {
            $input_array = json_decode($request->input_array, true);
            $output_array = json_decode($request->output_array, true);

            $formula = $request->formula;
            $row_node = $request->row_node;
            $row_count = $request->row_count;
            $vlookups = Vlookup::all();

            $output_left = "";

            $output_left = View::make("mapping::components.from-field")
                ->with('input_array', $input_array)
                ->with('formula', (isset($row_node['with_formula'])) ? $row_node['with_formula'] : $request->formula)
                ->with('row_count', $row_count)
                ->with('row_node', $row_node)
                ->with('method_type', $request->method_type)
                ->render();


            $output = View::make("mapping::components.to-field")
                ->with('input_array', $input_array)
                ->with('output_array', $output_array)
                ->with('formula', $formula)
                ->with('row_count', $row_count)
                ->with('vlookups', $vlookups)
                ->with('row_node', $row_node)
                ->with('expand_positions', $this->fetch_expand_positioning())
                ->with('method_type', $request->method_type)
                ->render();


            return response()->json(['output' => $output, 'output_left' => $output_left]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()]);
        }
    }


    function column_mapped_for_handle($array)
    {
        $sku_key_array = null;
        $mapping_conversion_obj = new MappingConversion();
        foreach ($array as $key => $input_fields) {
            if (is_iterable($input_fields)) {
                if (isset($input_fields['to']) && is_iterable($input_fields['to'])) {
                    foreach ($input_fields['to'] as $val) {
                        if (isset($val) && $val == "Default,handle") {
                            $sku_key_array = $key;
                        }
                    }
                }
            }
        }

        if ($sku_key_array === null) {
            return null;
        }
        return $mapping_conversion_obj->get_explode_attribute_name($array[$sku_key_array]['from'][0]);
    }

    function array_map_key_recursive(callable $func, array $array)
    {
        return array_map(function ($item) use ($func) {
            if (is_array($item)) {
                $item = $this->array_map_key_recursive($func, $item);
            }
            return $item;
        }, array_combine(array_map($func, array_keys($array)), $array));
    }

    function collection_map_key_recursive(callable $func, $collection)
    {
        return $collection->map(function ($item, $key) use ($func) {
            if ($item instanceof Collection) {
                return $this->collection_map_key_recursive($func, $item);
            } elseif (is_array($item)) {
                return $this->collection_map_key_recursive($func, new Collection($item));
            }
            return $item;
        })->keyBy(function ($item, $key) use ($func) {
            return $func($key);
        });
    }


    /**
     * fetch selected formula fields
     *
     * @param Request $request (formula)
     *
     */
    public function mapping_convert(Request $request)
    {
        $validator = Validator::make($request->all(), [
//            'file_path' => 'required',
            'nodes' => 'required',
            'organization_id' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()]);
        }

        $path = isset($request->file_path) ? Storage::disk('s3')->url($request->file_path) : null;

        $extension = pathinfo($path, PATHINFO_EXTENSION);

        if ($extension == 'csv' || $extension == 'xlsx') {

            $collection = (new \App\Imports\ProductsImport())->toCollection($request->file_path ?? null, 's3');
            $collection = $collection->first() ?? null;

            $collection = $this->collection_map_key_recursive('trim', $collection);

            // //          TODO: we need to add here rows validation
            //             $all_nodes = $request->nodes;

            //             // fetch mapped column name in csv
            //             $csv_handle_column_name = isset($all_nodes['data']) ? $this->column_mapped_for_handle($all_nodes['data']) : null;

            //             dd($csv_handle_column_name);

            //             // csv sku column values
            //             $all_csv_skus = [];
            //             if (isset($csv_sku_column_name)){
            //                 $all_csv_skus = array_column($collection,$csv_sku_column_name);
            //             }


            //             // fetch remaining row limit
            //             $row_limit = $request->request_data['temp']['product_limit'] ?? count($all_csv_skus);

            //             // fetch all updating skus
            //             $same_csv_sku = array_intersect($all_csv_skus,$request->all_product_skus);

            //             // fetch all unique skus
            //             $unique_inlimit_sku = array_slice(array_diff($all_csv_skus , $request->all_product_skus), 0, $row_limit, true);

            //             // 1 = update and add new skus | 2 = only update existing skus | 3 = only add new skus
            //             $import_action = $request->request_data['import_action'];
            //             if ($import_action == "1"){
            //                 $collection = array_intersect_key($collection,$unique_inlimit_sku+$same_csv_sku);
            //             }
            //             elseif ($import_action == "3"){
            //                 $collection = array_intersect_key($collection,$unique_inlimit_sku);
            //             }
            //             else{
            //                 $collection = array_intersect_key($collection,$same_csv_sku);
            //             }
        } else {

            if ($path) {
                // Read the JSON data from a file
                $jsonData = file_get_contents($path);

                // Convert the JSON data to an associative array
                $data = json_decode($jsonData, true);
            } else if (isset($request->products)) {
                $data = $request->products;
            } else {
                if (!Storage::disk('s3')->exists($request->file_path)) {
                    $validator->errors()->add('file_path', 'Your product data json file is not exist.');
                    return $validator->errors();
                }
            }

            // Convert the array to a Laravel collection
            $pre_collection = new Collection($data);

            // Apply the collection_map_key_recursive function to the collection
            $collection = $this->collection_map_key_recursive('trim', $pre_collection);

        }


        $mapping_obj = new MappingConversion();

        if (!empty($collection)) {
            $final_collection = $mapping_obj->set_data($request->all())
                ->convert_data_v2(
                    $collection,
                    // error function
                    function ($errors) {
                        return $errors;
                    },

                    // success function
                    function ($success) {
                        return $success;
                    }
                );
        } else {
            $final_collection = collect();
        }
        return $final_collection;
    }


    /**
     * @param $product
     * @return array
     */
    public function fetch_default_apimio_array($product): array
    {
        $non_family_attribute = array();
        $non_family_attribute['family_name'] = "Default";
        $non_family_attribute['attributes'] = [
            'handle' => $product->sku ?? null,
            'status' => $this->product_status_in_text($product->status ?? null),
            'brand' => $this->fetch_brand($product),
            'file' => $this->fetch_file($product),
            'categories' => $this->fetch_category($product),
            'vendor' => $this->fetch_vendor($product),
            // 'Quantity' => $this->fetch_inventory($product),
        ];

        // need to remove this code because we have remove this from product setting
//         if (isset($this->data) && isset($this->data['request_data']['template_method_type']) && $this->data['request_data']['template_method_type'] == 'shopify') {
//             $non_family_attribute['attributes']['track_quantity'] = $product->settings->track_quantity ?? true;
//             $non_family_attribute['attributes']['continue_selling'] = $product->settings->continue_selling ?? false;
//         }
        return $non_family_attribute;
    }


    /**
     * @param $product_status
     * @return string
     */
    public function product_status_in_text($product_status): string
    {
        $status = "draft";
        if ($product_status == 1) {
            $status = "active";
        }
        return $status;
    }


    /**
     * Convert apimio product to family attributes array
     *
     * @param $product
     * @param $with_variants
     * @return array
     */
    public function convert_apimio_product_to_family_attributes_array($product = null, $with_variants = null): array
    {
        $product_array = array();

        $data_required = $this->data['data_required'];

        $product_array['parent'][] = $this->fetch_default_apimio_array($product);

        if ($product->has('versions')) {
            $versions = $product->versions;
            foreach ($versions as $version) {
                if (isset($version->families)) {
                    $product_families = array();
                    $product_array['hidden'] = array();
                    $product_array['hidden_units'] = array();
                    $product_families = $this->fetch_converted_family_attributes_array($version->families);
                    $product_array['parent'] = array_merge($product_array['parent'], $product_families['families']);
                    $product_array['hidden'] = array_merge($product_array['hidden'], $product_families['hidden']);
                    $product_array['hidden_units'] = array_merge($product_array['hidden_units'], $product_families['hidden_units']);
                }
            }
        }


        // with variants
        if ($with_variants) {

            if ($product->has('variants') && $product->variants->isNotEmpty()) {
                foreach ($product->variants as $variant) {

                    $variant_quantities = $this->find_variant_inventory_with_location($variant);
                    $variant_attributes = array();
                    $variant_attributes['handle'] = $variant->product->sku ?? '';
                    $variant_attributes['sku'] = $variant->sku ?? '';
                    $variant_attributes['name'] = $variant->name ?? '';
                    $variant_attributes['price'] = $variant->price ?? '';
                    $variant_attributes['cost_price'] = $variant->cost_price ?? '';
                    $variant_attributes['compare_at_price'] = $variant->compare_at_price ?? '';
                    $variant_attributes['cost_price'] = $variant->cost_price ?? '';
                    $variant_attributes['weight'] = $variant->weight ?? '';
                    $variant_attributes['weight_unit'] = $variant->weight_unit ?? '';
                    $variant_attributes = array_merge($variant_attributes, $variant_quantities);
                    $variant_attributes['barcode'] = $variant->barcode ?? '';
                    $variant_attributes['file'] = $variant->file()->first()->link ?? "";
//                    $variant_attributes['attr_options_json'] = $variant->option ?? "";
                    $variant_attributes['attr_options_json'] = $variant->decode_option_with_attached_attributes_name() ?? "";

                    if (isset($data_required['template_method_type']) && $data_required['template_method_type'] == "shopify") {
                        $variant_attributes['track_quantity'] = $variant->settings->track_quantity ?? true;
                        $variant_attributes['continue_selling'] = $variant->settings->continue_selling ?? true;
                        $variant_attributes['store_connect_id'] = $variant->channelVariant->store_connect_id ?? null;
                        $variant_attributes['store_connect_image_id'] = $variant->channelVariant->store_connect_image_id ?? null;
                    }

                    $product_array['variants'][] = $variant_attributes;
                }
            }

            $product_array['parent'][] = [
                'family_name' => 'Variant',
                'attributes' => $product_array['variants'][0] ?? [],
            ];
        }

        return $product_array;
    }


    /**
     * Get variant quantity with location
     * @param array $variant
     * @return array
     */
    protected function find_variant_inventory_with_location($variant = []): array
    {
        $variant_quantities = [];
        if ($variant->has('inventories')) {
            $inventories = $variant->inventories;
            foreach ($inventories as $inventory) {
                $variant_quantities['inventory,' . $inventory->location_id] = $inventory->available_quantity;
            }
        }
        return $variant_quantities;
    }


    /**
     * @param $products // null or an eloquent collection
     * @param bool $with_variants // true or false
     * @param $data // data array with families and variants key
     * @return array
     */
    public function fetch_apimio_products($products = null, bool $with_variants = true, $data = []): array
    {
        $this->set_data($data);
        $final_converted_array = [
            'array_name' => 'Apimio',
        ];
        if (isset($products)) {

            if (!is_iterable($products)) {
                $products = collect([$products]);
            }
            foreach ($products as $product) {
                $final_converted_array['nodes'][] = $this->convert_apimio_product_to_family_attributes_array($product, true);
            }
        } else {
            if (!empty($data)) {
                $product_array = array();
                $product_array['hidden'] = array();
                //default array
                $product_array['parent'][] = $this->fetch_default_apimio_array($products);
                //family array
                if (isset($data['families'])) {
                    $product_families = $this->fetch_converted_family_attributes_array($data['families']);
                    $product_array['parent'] = array_merge($product_array['parent'], $product_families['families']);
                    $product_array['hidden'] = array_merge($product_array['hidden'], $product_families['hidden']);
                }

                //variant array
                if (isset($data['variants'])) {
                    $product_array['parent'][] = [
                        'family_name' => 'Variant',
                        'attributes' => $data['variants'][0] ?? [],
                    ];
                    $product_array['variants'] = $data['variants'];
                }
                $final_converted_array['nodes'][] = $product_array;
            }
        }
        return $final_converted_array;
    }


    /**
     * @param $families // contain families eloquent object with attribute relation
     * @return array // families with hidden array
     */
    public function fetch_converted_family_attributes_array($families): array
    {
        $product_families = array();
        $hidden = array();
        $data_required = $this->data['data_required'] ?? [];
        $version_ids = $data_required['versions'] ? array_keys($data_required['versions']) : null;
        $version_currencies = Version::whereIn('id', $version_ids)->where('organization_id', $data_required['organization_id'] ?? null)->get();
        $currency = null;
        if ($version_currencies) {
            $currency = $version_currencies->first()->currency;
        }
        $hidden_units = [
            'Variant' => [
                'price' => $currency,
                'compare_at_price' => $currency,
                'cost_price' => $currency,
            ],
        ];
        foreach ($families as $fam_key => $family) {
            if ($family->has('attributes')) {
                $product_attributes = array();
                $fam_attributes = $family->attributes;
                foreach ($fam_attributes as $attribute) {

                    // Initialize as array
                    $product_attributes[$attribute->handle] = $product_attributes[$attribute->handle] ?? [];

                    // for weight unit
                    if ($attribute->handle == "weight" && $family->name == "General") {
                        $product_attributes["Weight Unit"] = "";

                    }

                    if (isset($attribute->value)) {

                        if (is_iterable($attribute->value)) {
                            foreach ($attribute->value as $val) {
                                if (!is_null($val->unit)) {
                                    $hidden_units[$family->name][$attribute->handle][] = $val->unit;
                                }
                                // for weight unit
                                if ($attribute->handle == "weight" && $family->name == "General") {
                                    $product_attributes["Weight Unit"] = $val->unit;
                                }
                                if (isset($val->value)) {
                                    if (is_array($product_attributes[$attribute->handle])) {
                                        $product_attributes[$attribute->handle][] = $val->value;
                                    } else {
                                        if ($product_attributes[$attribute->handle] == "") {
                                            $product_attributes[$attribute->handle] = [$val->value];
                                        } else {
                                            $previous_attributes = $product_attributes[$attribute->handle];
                                            $product_attributes[$attribute->handle] = [$previous_attributes, $val->value];
                                        }
                                    }
                                }
                            }
                            // implode for units for multi values
                            if (isset($hidden_units[$family->name][$attribute->handle])) {
                                if (is_array($hidden_units[$family->name][$attribute->handle])) {
                                    $hidden_units[$family->name][$attribute->handle] = implode(' , ', $hidden_units[$family->name][$attribute->handle]);
                                }
                            }
                        } else {
                            // Handle scalar values
                            $product_attributes[$attribute->handle][] = $attribute->value;
                        }
                        $hidden[$family->name][$attribute->handle] = $attribute->id;
                    } else {
                        // Default value if attribute value not set
                        $product_attributes[$attribute->handle] = "";
                    }

                    if (isset($product_attributes[$attribute->handle]) && is_array($product_attributes[$attribute->handle])) {
                        $product_attributes[$attribute->handle] = implode(' , ', $product_attributes[$attribute->handle]);
                    }

                }
                $product_families[$fam_key]['family_name'] = $family->name;
                $product_families[$fam_key]['attributes'] = $product_attributes;
            }
        }

        return [
            'families' => $product_families,
            'hidden' => $hidden,
            'hidden_units' => $hidden_units
        ];

    }


    /**
     * method for Fetching apimio left array
     *
     *
     * @param $mapping_array
     * @return array
     */
    public function create_apimio_left_array($mapping_array)
    {
        $data = array();
        if (isset($mapping_array['request_data']['template_method_type']) && $mapping_array['request_data']['template_method_type'] != "import") {
            $data['variants'][] = [
                'sku' => '',
                'name' => '',
                'file' => '',
                'price' => '',
                'compare_at_price' => '',
                'quantity' => '',
                'barcode' => '',
                'weight' => '',
                'weight_unit' => '',
                // 'track_quantity' => '',
                // 'continue_selling' => '',
            ];
        }

        return $data;
    }


    /**
     * method for Fetching right array
     *
     * @param $type (shopify,apimio,magento)
     * @return array
     */
    public function fetch_right_array($type): array
    {
        $template = new Template();
        $right_array = [];
        if ($type == 'apimio') {
            $right_array = Template::apimio_mapping_array();
        } else if ($type == 'shopify') {
            $right_array = $template->shopify_mapping_array();
        }

        return $right_array;
    }


    /**
     * method for Fetching product brands name
     *
     *
     *
     */
    public function fetch_brand($product)
    {
        $brands = array();
        if (isset($product) && $product->has('brands')) {
            foreach ($product->brands as $brand) {
                if (isset($brand->name)) {
                    $brands[] = $brand->name;
                }
            }
        }
        return empty($brands) ? null : implode(", ", $brands);
    }


    /**
     * method for Fetching product categories name
     *
     *
     *
     */
    public function fetch_category($product)
    {
        $categories = array();
        $category_obj = new Category();
        if (isset($product) && $product->has('categories')) {
            foreach ($product->categories as $category) {
                $categories_with_parents = $category_obj->get_parent_categories_with_name($category->id);
                if (!empty($categories_with_parents)) {
                    $categories[] = implode(' > ', $categories_with_parents);
                }
            }
        }
        return empty($categories) ? null : implode(", ", $categories);
    }


    /**
     * method for Fetching product vendors name
     *
     *
     *
     */
    public function fetch_vendor($product)
    {
        $vendors = array();
        if (isset($product) && $product->has('inviteVendor')) {
            $all_vendors = $product->inviteVendor;
            foreach ($all_vendors as $vendor) {
                $vendors[] = $vendor->fname . " " . $vendor->lname;
            }
        }
        return empty($vendors) ? null : implode(", ", $vendors);
    }

    /**
     * method for Fetching product inventory
     *
     *
     *
     */
    public function fetch_inventory($product)
    {
        $inventories = array();

        if (isset($product) && $product->has('inventories')) {
            $all_inventories = $product->inventories;
            foreach ($all_inventories as $inventory) {
                $inventories[] = $inventory->available_quantity;
            }
        }
        return empty($inventories) ? null : implode(", ", $inventories);
    }


    /**
     * method for Fetching product versions name
     *
     *
     *
     */
    public function fetch_version($product)
    {
        $versions = array();
        if (isset($product) && $product->has('versions')) {
            foreach ($product->versions as $version) {
                if (isset($version->name)) {
                    $versions[] = $version->name;
                }
            }
        }
        return empty($versions) ? null : implode(", ", $versions);
    }


    /**
     * method for Fetching product files link
     *
     *
     *
     */
    public function fetch_file($product)
    {
        $files = array();
        if (isset($product) && $product->has('files')) {
            foreach ($product->files as $file) {
                if (isset($file->link)) {
                    $files[] = $file->link;
                }
            }
        }
        return empty($files) ? null : implode(", ", $files);
    }


    public function fetch_expand_positioning()
    {
        $formula = [
            'start' => "Start",
            'end' => "End",
        ];

        return $formula;
    }


    public function set_formula_csv()
    {
        $formula = [
            'assign' => "Assign",
            'split' => "Split",
            'merge' => "Merge (Basic)",
            'short_code' => "Merge (Advance)",
            'replace' => "Replace",
            'slug' => "Slug",
            'vlookup' => 'V-Lookup',
            'calculate' => 'Calculate',
            'expand' => 'Expand'

        ];

        return $formula;
    }

    public function mappingReact()
    {
        return view('mapping::mappingreact');
    }
}
