<?php

namespace Apimio\MappingConnectorPackage\classes;

use App\Classes\Facades\UnitFacade;

class ImportExport
{


    /**
     * method for Fetching product files link
     * @param string $temp_create which define the type of template which we want to create.
     * @param  boolean  $with_variant if we want to add variants default value then pass TRUE otherwise default value is FALSE
     *
     *
     * @return  array fetch all the default values for specific default template
     */
    public function fetch_template_default_values (string $temp_create = null, $with_variant = FALSE): array
    {
        $csv_row = array();
        // if equals to shopify
        if ($temp_create === 'shopify'){
            if ($with_variant) {
                $csv_row['Variant Inventory Tracker'] = 'shopify';
                $csv_row['Variant Inventory Policy'] = 'continue';
                $csv_row['Variant Fulfillment Service'] = 'manual';
                $csv_row['Variant Requires Shipping'] = 'TRUE';
                $csv_row['Variant Taxable'] = 'TRUE';
            }
            else{
                $csv_row['Published'] = 'TRUE';
                $csv_row['Gift Card'] = 'FALSE';
            }
        }
        // if equals to magento
        elseif ($temp_create === 'magento'){
            $csv_row['attribute_set_code'] = 'Default';
            $csv_row['product_websites'] = 'base';
            $csv_row['tax_class_name'] = 'Taxable Goods';

            if ($with_variant) {
                $csv_row['product_type'] = 'simple';
                $csv_row['visibility'] = 'Not Visible Individually';
            }
            else{
                $csv_row['product_type'] = 'configurable';
                $csv_row['visibility'] = 'Catalog, Search';
            }
        }
        return $csv_row;
    }


    /**
     * filter attributes for export csv variants file
     *
     * @param array $variant
     * @param array $parent
     * @param $type
     * @param $variants_count
     * @return array
     */
    function variant_filter_attributes(array $variant = [], array &$parent = [], $type = null, $variants_count = null): array
    {
        $shopify_remove_attr = ['Title'];
        $magento_remove_attr = ['configurable_variations','configurable_variation_labels'];
        if ($type == 'shopify'){
            $variant['Handle'] = $parent['Handle'] ?? null;
            $variant = array_diff_key($variant,array_flip($shopify_remove_attr));
            $variant = array_merge($variant,$this->fetch_template_default_values($type, TRUE));
            $variant['Variant Grams'] = UnitFacade::convert_value([
                'value' => $variant['Variant Grams'] ?? null,
                'from_unit' => $variant['Variant Weight Unit'] ?? null,
                'to_unit' => "g"
            ]);
        }
        else if($type == 'magento'){

            if (!isset($parent['configurable_variations'])){
                $parent['configurable_variations'] = array();
            }
            $parent['configurable_variations'][] = $variant['configurable_variations'] ?? '';


            if (!isset($parent['configurable_variation_labels'])){
                $parent['configurable_variation_labels'] = array();
            }

            if ($variant['configurable_variation_labels']){
                foreach ($variant['configurable_variation_labels'] as $configurable_variation_label){
                    if (!in_array($configurable_variation_label,$parent['configurable_variation_labels'])){
                        $parent['configurable_variation_labels'][] = $configurable_variation_label;
                    }
                }

            }


            $variant = array_diff_key($variant,array_flip($magento_remove_attr));
            $variant = array_merge($variant,$this->fetch_template_default_values($type, TRUE));
        }
        return $variant;
    }



    /**
     * method for getting shopify products attributes heading name for CSV export
     *
     *
     * @return array
     */
    public function get_shopify_product_attributes_name ($custom_added = []): array
    {
        $heading_array = array();
        $heading_array = [ 'Handle', 'Title', 'Body (HTML)','Vendor',
            'Standard Product Type', 'Custom Product Type',
            'Tags', 'Published', 'Option1 Name', 'Option1 Value',
            'Option2 Name', 'Option2 Value', 'Option3 Name',
            'Option3 Value', 'Variant SKU', 'Variant Grams',
            'Variant Inventory Tracker', 'Variant Inventory Qty',
            'Variant Inventory Policy', 'Variant Fulfillment Service',
            'Variant Price', 'Variant Compare At Price',
            'Variant Requires Shipping', 'Variant Taxable',
            'Variant Barcode', 'Image Src', 'Image Position',
            'Image Alt Text', 'Gift Card', 'SEO Title', 'SEO Description',
            'Variant Image', 'Variant Weight Unit', 'Variant Tax Code',
            'Cost per item', 'Status'

        ];

        if (!empty($custom_added)){
            $heading_array = array_merge($heading_array,$custom_added);
        }
        return $heading_array;
    }


    /**
     * method for getting magento products attributes heading name for CSV export
     *
     *
     * @return array
     */
    public function get_magento_product_attributes_name (): array
    {
        $heading_array = array();
        $heading_array = [
            'sku','name','description', 'attribute_set_code', 'price', 'product_type', 'product_websites',
            'weight',	'product_online',	'tax_class_name',	'visibility', 'url_key', 'additional_attributes',
            'qty', 'configurable_variations', 'configurable_variation_labels'
        ];
        return $heading_array;
    }

}
