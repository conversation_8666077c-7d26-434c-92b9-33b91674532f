(function(e,a,d,g){var b="shadonghongCaret";var c=function(){if(d.getSelection){var h=d.getSelection();if(h.rangeCount>0){return h.getRangeAt(0)}}else{if(a.selection){return a.selection.createRange()}}return null};var f=function(h){e(h).off("mouseup keyup").on("mouseup keyup",function(){e(this).data(b,c())});e(h).each(function(){if(!e(this).hasfocus){e(this).focus();var i=c();i.selectNodeContents(this);i.collapse(false);e(this).data(b,i);e(this).blur()}})};e.initCursor=f;e.fn.insertAtCursor=function(h){return this.each(function(){var r=this,k,t=0,u=0,q=("selectionStart" in r&&"selectionEnd" in r),p,i,n,s,l;if(!((r.tagName&&r.tagName.toLowerCase()==="textarea")||(r.tagName&&r.tagName.toLowerCase()==="input"&&r.type.toLowerCase()==="text"))){if(!e(r).hasfocus){e(r).focus()}if(d.getSelection&&d.getSelection().getRangeAt){n=e(r).data(b)||(f(r),e(r).data(b));n.collapse(false);l=n.createContextualFragment(h);var o=l.lastChild;n.insertNode(l);if(o){n.setEndAfter(o);n.setStartAfter(o)}var m=d.getSelection();m.removeAllRanges();m.addRange(n)}else{if(a.selection&&a.selection.createRange){a.selection.createRange().pasteHTML(h)}}}else{k=r.scrollTop;if(q){t=r.selectionStart;u=r.selectionEnd}else{r.focus();n=a.selection.createRange();n.moveStart("character",-r.value.length);t=n.text.length}if(u<t){u=t}p=(r.value).substring(0,t);i=(r.value).substring(u,r.value.length);r.value=p+h+i;t=t+h.length;if(q){r.selectionStart=t;r.selectionEnd=t}else{n=a.selection.createRange();n.moveStart("character",t);n.moveEnd("character",0);n.select()}r.scrollTop=k}})}})(jQuery,document,window);
