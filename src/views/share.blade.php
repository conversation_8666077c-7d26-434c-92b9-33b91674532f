@extends('layouts.app_new',['sidebar_display'=> false])
@section('titles','Mapping Fields')
@section('content')
    @push("header_scripts")
{{--        <link rel="stylesheet" href="{{asset('css/gallery/css/updated_gallery.css')}}" type="text/css"/>--}}
{{--        <link rel="stylesheet" href="{{asset('css/gallery/css/shared_page.css')}}" type="text/css"/>--}}
                        <link rel="stylesheet" href="{{asset('gallery_assets/scss/screen.css')}}" type="text/css"/>

    @endpush
    <style>
        .share_page_emoji {
            width: 100% !important;
        }
        #folder-main2 {
            display: none;
        }
        #hide-folders {
            display: none;
        }
        section {
            display: none;
        }
        /*.card-collection {*/
        /*    width: 100%;*/
        /*    grid-template-columns: repeat(auto-fit, minmax(281px, 1fr));*/
        /*    grid-gap: 24px;*/
        /*}*/
        #dropdownMenuLink {
            display: none;
        }
    </style>
    <div class="d-flex justify-content-between flex-column" >
        <div class="d-flex align-items-end">
            <h1 class=" pt-3">Media Library</h1>
            <span  class="pt-2" style="font-family: Poppins, sans-serif; font-weight: 400; font-size: 38px; line-height: 45px; color: #A5A5A5" >/</span>
            <a href="" class="page-title pt-2 path-head" style="  font-weight: 500; line-height: 44px !important;">Demo</a>

        </div>

            <p class="page-subheading mt-3 mb-2">Organization name : {{$org_name}}</p>

    </div>

    @if($list->count()==0 &&     ($files->total()==0) )
        <div class="empty_gallery" style="margin-top: 325px">
            <x-gallery-empty-gallery></x-gallery-empty-gallery>
        </div>
    @endif

    <section class="section2">
        {{--    Search Bar--}}
{{--        <div class="col-12 col-xl-3 col-lg-3 mt-4">--}}
{{--            <div class="d-flex flex-row">--}}
{{--                <div class="input-group">--}}
{{--                    <input type="text" class="form-control" value="{{request('q')}}" name="q"--}}
{{--                           placeholder="Search"--}}
{{--                           aria-label="Search by SKU" aria-describedby="search">--}}
{{--                    @if(request()->has('q'))--}}
{{--                        <a href="{{ \Illuminate\Support\Facades\Request::url() }}"--}}
{{--                           class="ripplelink btn search_btn_close">--}}
{{--                            <i class="fa fa-close"></i>--}}
{{--                        </a>--}}
{{--                    @endif--}}
{{--                    <div class="input-group-append">--}}
{{--                        <button class="search" type="submit" id="search">--}}
{{--                            <img src="{{asset('media/retailer-dashboard/Search-2.png')}}" alt="">--}}
{{--                        </button>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}

        {{--    Folder Component--}}

        <h2 class="folder-title mt-5 mb-4 ">Sub Folders</h2>
        @if($list->count()==0)
            <p class="Roboto mx-auto text-center " style="font-weight: 400;">
                No folders found.
            </p>
        @endif

        <div class="share-collection " href="gallery.folder" id="folder-main">
            @foreach($list->slice(0,14) as $linked_folder)
                <x-gallery-folder name="{{$linked_folder->name}}" id="{{encrypt($linked_folder->id)}}" link="{{$linked_folder->link}}" parentFolderId="{{$id}}"/>
            @endforeach

        </div>
        <div class="share-collection " href="gallery.folder" id="folder-main2">
            @foreach($list as $linked_folder)
                <x-gallery-folder name="{{$linked_folder->name}}" id="{{encrypt($linked_folder->id)}}" link="{{$linked_folder->link}}" parentFolderId="{{$id}}"/>
            @endforeach

        </div>
        @if($list->count()>14)
            <button type="button"
                    class="mt-1"
                    id="show-folders"
                    style="float: right; color: #6C757D; text-decoration-line: underline; font-weight: 700; border: hidden; background-color:transparent ">
                Show all ({{$list->count()}})
            </button>
        @endif
        <button type="button"
                class="mt-1"
                id="hide-folders"
                style="float: right; color: #6C757D; text-decoration-line: underline; font-weight: 700; border: hidden; background-color:transparent ">
            Show less
        </button>
        <x-gallery-folder-create id="download_folder"  parentFolderId="{{$id}}" header="Email Address" formUrl="{{route('file.downloadZip')}}" title="Email" btnText="Submit" message="Please enter your email and get your required folder mailed to you"  ></x-gallery-folder-create>

        <x-gallery-confirmation-modal></x-gallery-confirmation-modal>

        <div class="divider-head pb-1 mt-4">
            <div class="divider div-transparent div-arrow-down" id="divider-bar"></div>
        </div>
        <div class="d-flex justify-content-between mt-3">
            <div>
                <h2 class="folder-title mt-3">Images</h2>
                <p class="page-subheading mt-3 mb-4 total_img">Total Images: {{$folder_files->files->total()}}</p>
            </div>
            <div class="d-flex align-items-center">
                <div class="download-btn-spacing me-4">
                    <a type="button" class="btn btn-dark"  style="    padding: 11px 42px;"  data-bs-toggle="modal" data-bs-target="#download_folder">Download All</a>
                </div>

                <div class="tabs">
                    <ul class="nav nav-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active"
                               data-bs-toggle="tab"
                               href="#tabs-1"
                               role="tab"><em class="fa-solid fa-table-list"></em></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#tabs-2" role="tab"><em class="fa-solid fa-grip"></em></a>
                        </li>
                    </ul><!-- Tab panes -->
                </div>
            </div>
        </div>
        @if($files->total()==0)
            <p class="Roboto mx-auto text-center mt-5 " style="font-weight: 400; margin-top: 200px !important;">
                No files found.
            </p>
        @endif

        <div class="tab-content">
            <div class="tab-pane active" id="tabs-1" >
                <div>
                    <x-gallery-asset-table :files="$files"></x-gallery-asset-table>
                </div>

            </div>
            <div class="tab-pane" id="tabs-2" >
{{--                <div class="share-collection-card">--}}
                        <x-gallery-asset-card :files="$files"></x-gallery-asset-card>
{{--                </div>--}}
            </div>
            {!! $files->links() !!}
        </div>
        <x-gallery-shared-asset-details></x-gallery-shared-asset-details>
    </section>
@endsection
@push('footer_scripts')
    <script>
        let side_modal = document.querySelector(".sidebar-modal")
        let card_img = document.querySelector(".card-img-top")
        let folder_count= {!! json_encode(count($list)) !!};
        let table_count = document.querySelectorAll(".clickable-row")
        const  shared_height_info = document.querySelector(".shared_height")
        const  shared_size_info = document.querySelector(".shared_size")
        const  shared_width_info = document.querySelector(".shared_width")
        const  shared_type_info = document.querySelector(".shared_type")
        const shared_img_info = document.querySelector(".shared_img");
        const shared_img_name = document.querySelector(".img_name");
        const update_info = document.querySelector(".update_info")
        let card_info = document.querySelectorAll(".card_info")
        let folder_name_info = document.querySelector(".folder_name")
        let table_img_data = {!! json_encode($files) !!};
        console.log(table_img_data, "here")
        let folder_data = {!! json_encode($folder) !!};
        let share_download_link = document.querySelector(".shared_img_download")
        function open_model() {
            $("#modal_aside_left").modal('show');
        }
        $(document).ready(function () {
            $('#new_down').select2();
        });



        //Custom css for folders
        window.onload = function() {
            if (folder_count === 2) {
                $("#folder-main").css('width', '506px')
            } else if (folder_count === 3 || folder_count === 4 || folder_count === 5 || folder_count === 6) {
                $("#folder-main").css('width', '615px')

            }

        }
        //Show data in sidebar
        function load_data(data, class_name=null) {
            let index = $(`${class_name}`).index(data);
            shared_height_info.innerHTML = ` ${table_img_data.data[index].height} px `
            shared_width_info.innerHTML = ` ${table_img_data.data[index].width} px `
            shared_type_info.innerHTML = ` ${table_img_data.data[index].ext}`
            shared_size_info.innerHTML = ` ${table_img_data.data[index].size} KB`
            shared_img_name.innerHTML = table_img_data.data[index].name
            folder_name_info.innerHTML = folder_data.name
            if (table_img_data.data[index].name.length > 23) {
                shared_img_name.setAttribute('data-bs-original-title', table_img_data.data[index].name);
            }
            $('[data-toggle="tooltip"]').tooltip();
            update_info.innerHTML =` ${table_img_data.data[index].updated_at.substring(0, 10)}`
            shared_img_info.src = table_img_data.data[index].link
            let id_save = table_img_data.data[index].id;
            let route_new = "{{route('file.download', [":id"])}}"
            route_new = route_new.replace(':id',id_save)
            share_download_link.href = route_new;
        }

        $(".table-img").click(function() {
            load_data(this, ".table-img")
        });
        $(".card_info").click(function() {
            load_data(this, ".card_info")

        });




        //    conditions for empty page
        if(folder_count > 0 &&  table_img_data.data.length === 0)
        {
            $(".section2").show()
            $(".tab-content").hide()
            $(".empty_gallery").hide()
            $(".download-btn-spacing").hide()
            $(".total_img").hide()
            $(".tabs").hide()
        }
        else if(folder_count > 0 ||  table_img_data.data.length > 0)
        {
            $(".section2").show()
            $(".empty_gallery").hide()

        }
        //    show all and show less

        $("#folder-main2").hide()


        $("#show-folders").click(function(){
            $("#folder-main").css("display", "none")
            $("#show-folders").css("display", "none")
            $("#folder-main2").css("display", "grid")
            $("#hide-folders").css("display", "block")
        })
        $("#hide-folders").click(function(){
            $("#folder-main").css("display", "grid")
            $("#show-folders").css("display", "block")
            $("#folder-main2").css("display", "none")
            $("#hide-folders").css("display", "none")
        });

        $(document).ready(function() {
            @if (session('success'))
            $('#confirmation_modal').modal('show');
            @endif
        });
    </script>
@endpush
