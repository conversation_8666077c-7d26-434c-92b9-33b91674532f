@php
    if(isset($row_node['with_formula'])){
        $formula = $row_node['with_formula'];
    }
@endphp
<div class="{{($formula == "short_code") ? "col-6 col-md-3 col-lg-3" : "col-6 col-md-6 col-lg-6"}} assign_formula align-self-sm-center" data-count="{{$row_count}}">
    <div class="row">
        @if($formula == "assign" || $formula == "slug" || $formula == "short_code")
            <div class="{{($formula == "short_code") ? "col-6 col-md-8 col-lg-8" : "col-6 col-md-3 col-lg-3" }} p-0">
                <div class="form-group">
                    <label for="">{{isset($output_array['array_name']) ? $output_array['array_name'] : "Output"}} Attribute</label>
                    @if($method_type == "export")
                        @if(isset($row_node['to'][0]))
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" value="{{$row_node['to'][0]}}" class="form-control right_array">
                        @elseif(isset($input_row_selector) && $input_row_selector != "")
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" value="{{$input_row_selector}}" class="form-control right_array">
                        @else
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" class="form-control right_array">
                        @endif
                    @else
                        <select name="nodes[data][{{$row_count}}][to][]" class="form-control right_array bg-white-smoke">
                            <option value="" class="Poppins regular text-color">Select Field</option>
                            @foreach($output_array['nodes'] as $attr_families)
                                @if(isset($attr_families['name']))
                                    <optgroup label='{{(isset($attr_families['name'])) ?  $attr_families['name'] : 'Others'}}' >
                                        @endif
                                        @if(isset($attr_families['attributes']))
                                            @foreach($attr_families['attributes'] as $attr_key => $attr_value)
                                                @php
                                                    if(isset($attr_families['name'])){
                                                        $family_output_value = $attr_families['name'].','.$attr_key;
                                                    }
                                                    else{
                                                       $family_output_value = $attr_key;
                                                    }
                                                @endphp
                                                @if(isset($row_node['to'][0]))
                                                    <option value="{{$family_output_value}}" {{($row_node['to'][0] == $family_output_value) ? 'selected' : null}} >{{$attr_value}}</option>
                                                @elseif(isset($input_row_selector) && $input_row_selector != "")
                                                    <option value="{{$family_output_value}}" {{($input_row_selector == $attr_value || $input_row_selector == $attr_key) ? 'selected' : null}} >{{$attr_value}}</option>
                                                @else
                                                    <option value="{{$family_output_value}}">{{$attr_value}}</option>
                                                @endif
                                            @endforeach
                                        @endif
                                        @if(isset($attr_families['name']))
                                    </optgroup>
                                @endif
                            @endforeach
                        </select>
                    @endif
                </div>
            </div>

        @elseif($formula == "split")
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">Separator</label>
                    @if(isset($row_node['with']))
                        <input type="text" name="nodes[data][{{$row_count}}][with]" value="{{$row_node['with']}}" style="width: 100%" class="form-control with_field">
                    @else
                        <input type="text" name="nodes[data][{{$row_count}}][with]" style="width: 100%" class="form-control with_field">
                    @endif
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">{{isset($output_array['array_name']) ? $output_array['array_name'] : "Output"}} Attribute (1)</label>
                    @if($method_type == "export")
                        @if(isset($row_node['to'][0]))
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" value="{{$row_node['to'][0]}}" class="form-control right_array">
                        @elseif(isset($input_row_selector) && $input_row_selector != "")
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" value="{{$input_row_selector}}" class="form-control right_array">
                        @else
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" class="form-control right_array">
                        @endif
                    @else
                        <select name="nodes[data][{{$row_count}}][to][]" class="form-control right_array bg-white-smoke" >
                            <option value="" class="Poppins regular text-color">Select Field</option>
                            @foreach($output_array['nodes'] as $attr_families)
                                @if(isset($attr_families['name']))
                                    <optgroup label='{{(isset($attr_families['name'])) ?  $attr_families['name'] : 'Others'}}' >
                                        @endif
                                        @if(isset($attr_families['attributes']))
                                            @foreach($attr_families['attributes'] as $attr_key => $attr_value)
                                                @php
                                                    if(isset($attr_families['name'])){
                                                        $family_output_value = $attr_families['name'].','.$attr_key;
                                                    }
                                                    else{
                                                       $family_output_value = $attr_key;
                                                    }
                                                @endphp
                                                @if(isset($row_node['to'][0]))
                                                    <option value="{{$family_output_value}}" {{($row_node['to'][0] == $family_output_value) ? 'selected' : null}} >{{$attr_value}}</option>
                                                @elseif(isset($input_row_selector) && $input_row_selector)
                                                    <option value="{{$family_output_value}}" {{($input_row_selector == $attr_value || $input_row_selector == $attr_key) ? 'selected' : null}} >{{$attr_value}}</option>
                                                @else
                                                    <option value="{{$family_output_value}}">{{$attr_value}}</option>
                                                @endif
                                            @endforeach
                                        @endif
                                        @if(isset($attr_families['name']))
                                    </optgroup>
                                @endif
                            @endforeach
                        </select>
                    @endif
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">{{isset($output_array['array_name']) ? $output_array['array_name'] : "Output"}} Attribute (2)</label>

                    @if($method_type == "export")
                        @if(isset($row_node['to'][1]))
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" value="{{$row_node['to'][1]}}" class="form-control right_array">
                        @elseif(isset($input_row_selector) && $input_row_selector != "")
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" value="{{$input_row_selector}}" class="form-control right_array">
                        @else
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" class="form-control right_array">
                        @endif
                    @else
                        <select name="nodes[data][{{$row_count}}][to][]" class="form-control right_array bg-white-smoke">
                            <option value="" class="Poppins regular text-color">Select Field</option>
                            @foreach($output_array['nodes'] as $attr_families)
                                @if(isset($attr_families['name']))
                                    <optgroup label='{{(isset($attr_families['name'])) ?  $attr_families['name'] : 'Others'}}' >
                                        @endif
                                        @if(isset($attr_families['attributes']))
                                            @foreach($attr_families['attributes'] as $attr_key => $attr_value)
                                                @php
                                                    if(isset($attr_families['name'])){
                                                        $family_output_value = $attr_families['name'].','.$attr_key;
                                                    }
                                                    else{
                                                       $family_output_value = $attr_key;
                                                    }
                                                @endphp
                                                @if(isset($row_node['to'][1]))
                                                    <option value="{{$family_output_value}}" {{($row_node['to'][1] == $family_output_value) ? 'selected' : null}} >{{$attr_value}}</option>
                                                @elseif(isset($input_row_selector) && $input_row_selector)
                                                    <option value="{{$family_output_value}}" {{($input_row_selector == $attr_value || $input_row_selector == $attr_key) ? 'selected' : null}} >{{$attr_value}}</option>
                                                @else
                                                    <option value="{{$family_output_value}}">{{$attr_value}}</option>
                                                @endif
                                            @endforeach
                                        @endif
                                        @if(isset($attr_families['name']))
                                    </optgroup>
                                @endif
                            @endforeach
                        </select>

                    @endif
                </div>
            </div>

        @elseif($formula == "merge")
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">Glue</label>
                    @if(isset($row_node['with']))
                        <input type="text" name="nodes[data][{{$row_count}}][with]" value="{{$row_node['with']}}" style="width: 100%" class="form-control with_field">
                    @else
                        <input type="text" name="nodes[data][{{$row_count}}][with]" style="width: 100%" class="form-control with_field">
                    @endif

                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label>{{isset($input_array['array_name']) ? $input_array['array_name'] : "Input"}} Attribute (2)</label>
                    <select name="nodes[data][{{$row_count}}][from][]"
                            class="form-control apimio-column left_array bg-white-smoke">
                        <option value="" class="Poppins regular text-color">Select Field</option>
                        @foreach($input_array['nodes'] as $input_key => $input_vals)

                            <optgroup label='{{(isset($input_key)) ?  $input_key : 'Others'}}'>

                                @foreach($input_vals as $input_val)
                                    @php
                                        if(isset($input_key)){
                                            $family_input_value = $input_key.','.$input_val;
                                        }
                                        else{
                                           $family_input_value = $input_val;
                                        }
                                    @endphp
                                    @if(isset($row_node['from'][1]))
                                        <option value="{{$family_input_value}}" {{($row_node['from'][1] == $family_input_value) ? 'selected' : null}} >{{$input_val}}</option>
                                    @elseif(isset($input_row_selector) && $input_row_selector)
                                        <option value="{{$family_input_value}}" {{($input_row_selector == $input_val) ? 'selected' : null}} >{{$input_val}}</option>
                                    @else
                                        <option value="{{$family_input_value}}">{{$input_val}}</option>
                                    @endif
                                @endforeach

                            </optgroup>
                        @endforeach

                    </select>
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">{{isset($output_array['array_name']) ? $output_array['array_name'] : "Output"}} Attribute</label>
                    @if($method_type == "export")
                        @if(isset($row_node['to'][0]))
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" value="{{$row_node['to'][0]}}" class="form-control right_array">
                        @elseif(isset($input_row_selector) && $input_row_selector != "")
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" value="{{$input_row_selector}}" class="form-control right_array">
                        @else
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" class="form-control right_array">
                        @endif
                    @else
                        <select name="nodes[data][{{$row_count}}][to][]"  class="form-control right_array bg-white-smoke">
                            <option value="" class="Poppins regular text-color">Select Field</option>
                            @foreach($output_array['nodes'] as $attr_families)
                                @if(isset($attr_families['name']))
                                    <optgroup label='{{(isset($attr_families['name'])) ?  $attr_families['name'] : 'Others'}}' >
                                        @endif
                                        @if(isset($attr_families['attributes']))
                                            @foreach($attr_families['attributes'] as $attr_key => $attr_value)
                                                @php
                                                    if(isset($attr_families['name'])){
                                                        $family_output_value = $attr_families['name'].','.$attr_key;
                                                    }
                                                    else{
                                                       $family_output_value = $attr_key;
                                                    }
                                                @endphp
                                                @if(isset($row_node['to'][0]))
                                                    <option value="{{$family_output_value}}" {{($row_node['to'][0] == $family_output_value) ? 'selected' : null}} >{{$attr_value}}</option>
                                                @elseif(isset($input_row_selector) && $input_row_selector)
                                                    <option value="{{$family_output_value}}" {{($input_row_selector == $attr_value || $input_row_selector == $attr_key) ? 'selected' : null}} >{{$attr_value}}</option>
                                                @else
                                                    <option value="{{$family_output_value}}">{{$attr_value}}</option>
                                                @endif
                                            @endforeach
                                        @endif
                                        @if(isset($attr_families['name']))
                                    </optgroup>
                                @endif
                            @endforeach
                        </select>
                    @endif
                </div>
            </div>

        @elseif($formula == 'replace')
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">Search</label>
                    @if(isset($row_node['replace']))
                        <input type="text" name="nodes[data][{{$row_count}}][replace]" value="{{$row_node['replace']}}" style="width: 100%" class="form-control replace_field">
                    @else
                        <input type="text" name="nodes[data][{{$row_count}}][replace]" style="width: 100%" class="form-control replace_field">
                    @endif
                </div>
            </div>
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">With </label>
                    @if(isset($row_node['with']))
                        <input type="text" name="nodes[data][{{$row_count}}][with]" value="{{$row_node['with']}}" style="width: 100%" class="form-control with_field">
                    @else
                        <input type="text" name="nodes[data][{{$row_count}}][with]" style="width: 100%" class="form-control with_field">
                    @endif
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">{{isset($output_array['array_name']) ? $output_array['array_name'] : "Output"}} Attribute</label>

                    @if($method_type == "export")
                        @if(isset($row_node['to'][0]))
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" value="{{$row_node['to'][0]}}" class="form-control right_array">
                        @elseif(isset($input_row_selector) && $input_row_selector != "")
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" value="{{$input_row_selector}}" class="form-control right_array">
                        @else
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" class="form-control right_array">
                        @endif
                    @else
                        <select name="nodes[data][{{$row_count}}][to][]" class="form-control right_array bg-white-smoke" >
                            <option value="" class="Poppins regular text-color">Select Field</option>
                            @foreach($output_array['nodes'] as $attr_families)
                                @if(isset($attr_families['name']))
                                    <optgroup label='{{(isset($attr_families['name'])) ?  $attr_families['name'] : 'Others'}}' >
                                        @endif
                                        @if(isset($attr_families['attributes']))
                                            @foreach($attr_families['attributes'] as $attr_key => $attr_value)
                                                @php
                                                    if(isset($attr_families['name'])){
                                                        $family_output_value = $attr_families['name'].','.$attr_key;
                                                    }
                                                    else{
                                                       $family_output_value = $attr_key;
                                                    }
                                                @endphp
                                                @if(isset($row_node['to'][0]))
                                                    <option value="{{$family_output_value}}" {{($row_node['to'][0] == $family_output_value) ? 'selected' : null}} >{{$attr_value}}</option>
                                                @elseif(isset($input_row_selector) && $input_row_selector)
                                                    <option value="{{$family_output_value}}" {{($input_row_selector == $attr_value || $input_row_selector == $attr_key) ? 'selected' : null}} >{{$attr_value}}</option>
                                                @else
                                                    <option value="{{$family_output_value}}">{{$attr_value}}</option>
                                                @endif
                                            @endforeach
                                        @endif
                                        @if(isset($attr_families['name']))
                                    </optgroup>
                                @endif
                            @endforeach
                        </select>
                    @endif
                </div>
            </div>

        @elseif($formula == 'vlookup')

            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">V-Lookup ( <a href="javascript:void(0);" data-bs-toggle="modal" data-bs-target="#vlookup_product">Add</a> )</label>
                    <select name="nodes[data][{{$row_count}}][with]" class="form-control vlookup_class with_field bg-white-smoke">
                        <option value="" class="Poppins regular text-color">Select Field</option>
                        @if(isset($vlookups))
                            @foreach($vlookups as $vlookup)
                                <option value="{{$vlookup->id}}" class="Poppins regular text-color">{{$vlookup->name}}</option>
                            @endforeach
                        @endif
                    </select>
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">{{isset($output_array['array_name']) ? $output_array['array_name'] : "Output"}} Attribute</label>
                    @if($method_type == "export")
                        @if(isset($row_node['to'][0]))
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" value="{{$row_node['to'][0]}}" class="form-control right_array">
                        @elseif(isset($input_row_selector) && $input_row_selector != "")
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" value="{{$input_row_selector}}" class="form-control right_array">
                        @else
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" class="form-control right_array">
                        @endif
                    @else
                        <select name="nodes[data][{{$row_count}}][to][]" class="form-control right_array bg-white-smoke" >
                            <option value="" class="Poppins regular text-color">Select Field</option>
                            @foreach($output_array['nodes'] as $attr_families)
                                @if(isset($attr_families['name']))
                                    <optgroup label='{{(isset($attr_families['name'])) ?  $attr_families['name'] : 'Others'}}' >
                                        @endif
                                        @if(isset($attr_families['attributes']))
                                            @foreach($attr_families['attributes'] as $attr_key => $attr_value)
                                                @php
                                                    if(isset($attr_families['name'])){
                                                        $family_output_value = $attr_families['name'].','.$attr_key;
                                                    }
                                                    else{
                                                       $family_output_value = $attr_key;
                                                    }
                                                @endphp
                                                @if(isset($row_node['to'][0]))
                                                    <option value="{{$family_output_value}}" {{($row_node['to'][0] == $family_output_value) ? 'selected' : null}} >{{$attr_value}}</option>
                                                @elseif(isset($input_row_selector) && $input_row_selector)
                                                    <option value="{{$family_output_value}}" {{($input_row_selector == $attr_value || $input_row_selector == $attr_key) ? 'selected' : null}} >{{$attr_value}}</option>
                                                @else
                                                    <option value="{{$family_output_value}}">{{$attr_value}}</option>
                                                @endif
                                            @endforeach
                                        @endif
                                        @if(isset($attr_families['name']))
                                    </optgroup>
                                @endif
                            @endforeach
                        </select>
                    @endif
                </div>
            </div>

        @elseif($formula == 'calculate')
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">
                        With
                        <a href="#" data-bs-toggle="tooltip" id="calculate-tooltip" data-bs-html="true" data-bs-placement="top" title="
                                    <div class='text-left'>
                                    <b>Example values:</b>
                                    <li>+50  Addition</li>
                                    <li>-50  Subtraction</li>
                                    <li>50%  percentage</li>
                                    <li>+50%  add percentage</li>
                                    <li>-50%  sub percentage</li>
                                    </div>">
                            ( e.g )
                        </a>
                    </label>
                    @if(isset($row_node['with']))
                        <input type="text" name="nodes[data][{{$row_count}}][with]" value="{{$row_node['with']}}" style="width: 100%" class="form-control with_field">

                    @else
                        <input type="text" name="nodes[data][{{$row_count}}][with]" style="width: 100%" class="form-control with_field">
                    @endif

                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">{{isset($output_array['array_name']) ? $output_array['array_name'] : "Output"}} Attribute</label>
                    @if($method_type == "export")
                        @if(isset($row_node['to'][0]))
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" value="{{$row_node['to'][0]}}" class="form-control right_array">
                        @elseif(isset($input_row_selector) && $input_row_selector != "")
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" value="{{$input_row_selector}}" class="form-control right_array">
                        @else
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" class="form-control right_array">
                        @endif
                    @else
                        <select name="nodes[data][{{$row_count}}][to][]" class="form-control right_array bg-white-smoke">
                            <option value="" class="Poppins regular text-color">Select Field</option>
                            @foreach($output_array['nodes'] as $attr_families)
                                @if(isset($attr_families['name']))
                                    <optgroup label='{{(isset($attr_families['name'])) ?  $attr_families['name'] : 'Others'}}' >
                                        @endif
                                        @if(isset($attr_families['attributes']))
                                            @foreach($attr_families['attributes'] as $attr_key => $attr_value)
                                                @php
                                                    if(isset($attr_families['name'])){
                                                        $family_output_value = $attr_families['name'].','.$attr_key;
                                                    }
                                                    else{
                                                       $family_output_value = $attr_key;
                                                    }
                                                @endphp
                                                @if(isset($row_node['to'][0]))
                                                    <option value="{{$family_output_value}}" {{($row_node['to'][0] == $family_output_value) ? 'selected' : null}} >{{$attr_value}}</option>
                                                @elseif(isset($input_row_selector) && $input_row_selector)
                                                    <option value="{{$family_output_value}}" {{($input_row_selector == $attr_value || $input_row_selector == $attr_key) ? 'selected' : null}} >{{$attr_value}}</option>
                                                @else
                                                    <option value="{{$family_output_value}}">{{$attr_value}}</option>
                                                @endif
                                            @endforeach
                                        @endif
                                        @if(isset($attr_families['name']))
                                    </optgroup>
                                @endif
                            @endforeach
                        </select>
                    @endif
                </div>
            </div>

        @elseif($formula == 'expand')
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">Position</label>
                    <select name="nodes[data][{{$row_count}}][replace]" class="form-control replace_field bg-white-smoke">
                        <option value="" class="Poppins regular text-color">Select Field</option>
                        @foreach($expand_positions as $position_handle => $position_value)
                            @if(isset($row_node['replace']))
                                <option value="{{$position_handle}}" {{($row_node['replace'] == $position_handle) ? 'selected' : null}} class="Poppins regular text-color" >{{$position_value}}</option>
                            @elseif(isset($input_row_selector) && $input_row_selector)
                                <option value="{{$position_handle}}" {{($input_row_selector == $position_handle || $input_row_selector == $position_value) ? 'selected' : null}} class="Poppins regular text-color">{{$position_value}}</option>
                            @else
                                <option value="{{$position_handle}}" class="Poppins regular text-color">{{$position_value}}</option>
                            @endif

                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">With </label>
                    @if(isset($row_node['with']))
                        <input type="text" name="nodes[data][{{$row_count}}][with]" value="{{$row_node['with']}}" style="width: 100%" class="form-control with_field">
                    @else
                        <input type="text" name="nodes[data][{{$row_count}}][with]" style="width: 100%" class="form-control with_field">
                    @endif
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">{{isset($output_array['array_name']) ? $output_array['array_name'] : "Output"}} Attribute</label>
                    @if($method_type == "export")
                        @if(isset($row_node['to'][0]))
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" value="{{$row_node['to'][0]}}" class="form-control right_array">
                        @elseif(isset($input_row_selector) && $input_row_selector != "")
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" value="{{$input_row_selector}}" class="form-control right_array">
                        @else
                            <input type="text" name="nodes[data][{{$row_count}}][to][]" class="form-control right_array">
                        @endif
                    @else
                        <select name="nodes[data][{{$row_count}}][to][]" class="form-control right_array bg-white-smoke">
                            <option value="" class="Poppins regular text-color">Select Field</option>
                            @foreach($output_array['nodes'] as $attr_families)
                                @if(isset($attr_families['name']))
                                    <optgroup label='{{(isset($attr_families['name'])) ?  $attr_families['name'] : 'Others'}}' >
                                        @endif
                                        @if(isset($attr_families['attributes']))
                                            @foreach($attr_families['attributes'] as $attr_key => $attr_value)
                                                @php
                                                    if(isset($attr_families['name'])){
                                                        $family_output_value = $attr_families['name'].','.$attr_key;
                                                    }
                                                    else{
                                                       $family_output_value = $attr_key;
                                                    }
                                                @endphp
                                                @if(isset($row_node['to'][0]))
                                                    <option value="{{$family_output_value}}" {{($row_node['to'][0] == $family_output_value) ? 'selected' : null}} >{{$attr_value}}</option>
                                                @elseif(isset($input_row_selector) && $input_row_selector)
                                                    <option value="{{$family_output_value}}" {{($input_row_selector == $attr_value || $input_row_selector == $attr_key) ? 'selected' : null}} >{{$attr_value}}</option>
                                                @else
                                                    <option value="{{$family_output_value}}">{{$attr_value}}</option>
                                                @endif
                                            @endforeach
                                        @endif
                                        @if(isset($attr_families['name']))
                                    </optgroup>
                                @endif
                            @endforeach
                        </select>
                    @endif
                </div>
            </div>

        @else
            <div class="col-lg-4">
                <b class="align-middle">Coming soon</b>
            </div>

        @endif




        {{--        @if($row_count != 0 )--}}
        <div class="col-6 col-md-1 col-lg-1 ms-auto">
            <div class="d-flex align-items-center pt-4 ms-auto float-end">
                <a href="javascript:void(0)" onclick="delete_row(this)">
                    <i class="fa-regular fa-trash-can fs-20 text-danger"></i>
                </a>
            </div>
        </div>
        {{--        @endif--}}
    </div>
</div>
