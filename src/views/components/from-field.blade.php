
@php

 if(isset($row_node['with_formula'])){
     $formula = $row_node['with_formula'];
 }
@endphp

<div class="left_div_selection {{($formula == "short_code") ? "col-6 col-md-6 col-lg-6" : "col-6 col-md-3 col-lg-3" }}">

    <div class="row">
        <div class="col-6 col-md-1 col-lg-1 d-flex align-self-center">
            <div class="row_success" data-bs-toggle="tooltip" data-bs-placement="top" title="Fields successfully mapped.">
                <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12.0156 22.5156C17.5156 22.5156 22.0156 18.0156 22.0156 12.5156C22.0156 7.01562 17.5156 2.51562 12.0156 2.51562C6.51562 2.51562 2.01562 7.01562 2.01562 12.5156C2.01562 18.0156 6.51562 22.5156 12.0156 22.5156Z" stroke="#28A745" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M7.73438 12.4941L10.5644 15.3241L16.2344 9.66406" stroke="#28A745" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <div class="row_warning" data-bs-toggle="tooltip" data-bs-placement="top" title="Please map this row fields.">
                <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 22.5C17.5 22.5 22 18 22 12.5C22 7 17.5 2.5 12 2.5C6.5 2.5 2 7 2 12.5C2 18 6.5 22.5 12 22.5Z" stroke="#FFC107" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M12 8.5V13.5" stroke="#FFC107" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M11.9922 16.5H12.0012" stroke="#FFC107" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
        </div>

        <div class="{{($formula == "short_code") ? "col-6 col-md-10 col-lg-10" : "col-6 col-md-9 col-lg-9" }}">
            @if(isset($formula) && $formula == "short_code")
                <div class="form-group">
                    <label for="name_in_import">{{isset($input_array['array_name']) ? $input_array['array_name'] : "Input"}} Content</label>
                    <input type="hidden" class="left_array apimio-column short_code_hidden_field" name="nodes[data][{{$row_count}}][from][]"
                           value="{{$row_node['from'][0] ?? ''}}"/>
                    <div contenteditable="true" class="form-control short_code_div bg-white-smoke"
                         data-placeholder="Enter your content with multiple elements"
                         oninput="get_short_code_value(this)">{!! isset($row_node['from'][0]) ? html_entity_decode(base64_decode($row_node['from'][0])) : '' !!}</div>

                    <div class="d-flex justify-content-between mt-1">
                        <div>
                            <button data-bs-toggle="tooltip" data-bs-placement="top" title="Clear Content" type="button" class="bg-transparent border-0 d-block" onclick="reset_element(this)"><i class="fa fa-refresh" aria-hidden="true"></i></button>
                        </div>
                        <div>
                            <div class="dropdown">
                                <button class="btn dropdown-toggle insert_element_title" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    {{  '{'.'{ }'.'} Insert Element' }}
                                </button>
                                <div class="dropdown-menu insert_element_main bg-white-smoke" style="max-height: 300px;overflow-y: auto" aria-labelledby="dropdownMenuButton">
                                    @foreach($input_array['nodes'] as $input_key => $input_vals)
                                        <h6 class="dropdown-header insert_element_head">{{(isset($input_key)) ?  $input_key : 'Others'}}</h6>
                                        <div class="insert_element_content">
                                            @foreach($input_vals as $input_val)
                                                @php
                                                    if(isset($input_key)){
                                                        $family_input_value = "{{".$input_key.','.$input_val."}}";
                                                    }
                                                    else{
                                                       $family_input_value = "{{".$input_val."}}";
                                                    }
                                                @endphp
                                                <a class="dropdown-item insert_element" href="javascript:void(0)" onclick="insert_element(this)" data-val="{{$family_input_value}}">{{$input_val}}</a>
                                            @endforeach
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @else
                <div class="form-group">
                    <label for="name_in_import">{{isset($input_array['array_name']) ? $input_array['array_name'] : "Input"}} Attribute</label>
                    <select name="nodes[data][{{$row_count}}][from][]"
                            class="form-control left_array apimio-column bg-white-smoke">
                        <option value="" class="Poppins regular text-color">Select Field</option>
                        @foreach($input_array['nodes'] as $input_key => $input_vals)

                            <optgroup label='{{(isset($input_key)) ?  $input_key : 'Others'}}'>

                                @foreach($input_vals as $input_val)
                                    @php
                                        if(isset($input_key)){
                                            $family_input_value = $input_key.','.$input_val;
                                        }
                                        else{
                                           $family_input_value = $input_val;
                                        }
                                    @endphp
                                    @if(isset($row_node['from'][0]))
                                        <option value="{{$family_input_value}}" {{(trim($row_node['from'][0]) === trim($family_input_value)) ? 'selected' : null}} >{{$input_val}}</option>
                                    @elseif(isset($input_row_selector) && $input_row_selector && isset($family) && $family)
                                        <option value="{{$family_input_value}}" {{(trim($input_row_selector) == trim($input_val)) && (trim($family) == trim($input_key)) ? 'selected' : null}} >{{$input_val}}</option>
                                    @else
                                        <option value="{{$family_input_value}}">{{$input_val}}</option>
                                    @endif
                                @endforeach

                            </optgroup>
                        @endforeach
                    </select>
                </div>
            @endif
        </div>
    </div>
</div>
