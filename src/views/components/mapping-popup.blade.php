<div class="modal fade" id="mapping_product" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title " id="exampleModalLabel">{{ $text_initials['heading_name'] ??  'Mapping Configuration'}}</h3>
                <button type="button" class="btn-sm border-0 bg-white fs-24 px-0 pt-0" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                @if(isset($data_required['template_method_type']) && $data_required['template_method_type'] == 'export')
                    <div class="form-group">
                        <label for="export_type">Choose Export Type&nbsp;<span class="text-danger">*</span></label>
                        <select name="export_type" class="form-control version bg-white-smoke" id="export_type" required>
                            <option value="">Choose</option>
                            @foreach($export_types as $key_export_type => $export_type)
                                @if(isset($template_attributes['export_type']))
                                    <option value="{{$key_export_type}}" {{ ($template_attributes['export_type'] == $key_export_type) ? 'selected' : null}}>{{$export_type}}</option>
                                @else
                                    <option value="{{$key_export_type}}">{{$export_type}}</option>
                                @endif
                            @endforeach
                        </select>
                    </div>
                @endif
                <div class="form-group mt-3">
                    <label for="version">{{trans('products_import_step3.select_language')}}&nbsp;<span class="text-danger">*</span></label>
                    <select name="version" class="form-control version bg-white-smoke" id="version" required>
                        <option value="">Choose</option>
                        @foreach($data_required['versions'] as $key_version => $p_version)
                            @if(isset($template_attributes['version_id']))
                                <option value="{{$key_version}}" {{ ($template_attributes['version_id'] == $key_version) ? 'selected' : null}}>{{$p_version}}</option>
                            @else
                                <option value="{{$key_version}}">{{$p_version}}</option>
                            @endif
                        @endforeach
                    </select>
                </div>
                <div class="form-group mt-3">
                    <label for="status">{{trans('products_import_step3.select_status')}}&nbsp;<span class="text-danger">*</span></label>
                    <select name="status" class="form-control status bg-white-smoke" id="status" required>
                        @if(isset($template_attributes['product_status']))
                            <option value="1" {{ ($template_attributes['product_status'] == 1) ? 'selected' : null}}>Published</option>
                            <option value="0" {{ ($template_attributes['product_status'] == 0) ? 'selected' : null}}>Draft</option>
                        @else
                            <option value="1">Published</option>
                            <option value="0">Draft</option>
                        @endif
                    </select>
                </div>
                <div class="form-group mb-3 mt-3">
                    <label for="catalog">{{trans('products_import_step3.select_catalog')}}&nbsp;<span class="text-danger">*</span></label>
                    <select name="catalog" class="form-control catalog bg-white-smoke" id="catalog" required>
                        <option value="">Choose</option>
                        @foreach($data_required['catalogs'] as $key_catalog => $p_catalog)
                                <option value="{{$key_catalog}}"
                                    {{ (isset($template_attributes['channel_id']) && $template_attributes['channel_id'] == $key_catalog) ? 'selected' : null}}>
                                    {{$p_catalog}}
                                </option>
                        @endforeach
                    </select>
                </div>
                    <div class="form-group mb-3 mt-3">
                        <label for="catalog">Select Store Location&nbsp;<span class="text-danger">*</span></label>
                        <select name="location" class="form-control catalog bg-white-smoke" id="location" required data-bs-toggle="tooltip" data-bs-placement="top" title="Please select store first then allow to select store location">
                            <option value="">Choose</option>
                            @if(isset($template_attributes['channel_id']) && ($locations = $data_required['locations'][$template_attributes['channel_id']] ?? null))
                                @foreach($locations as $key_location => $p_location)
                                    <option value="{{$key_location}}"
                                        {{ (isset($template_attributes['location_id']) && $template_attributes['location_id'] == $key_location) ? 'selected' : null}}>
                                        {{$p_location}}
                                    </option>
                                @endforeach
                            @endif

                        </select>
                    </div>
                <div id="template_checkbox_div">
                    <div class="d-flex flex-row mb-3 align-items-center" >
                            <label class="Roboto bold text-dark">
                                {{trans('products_import_step3.save_template_option')}}
                            </label>
                        <div class="custom-control custom-switch formStyle ms-3">
                            <label for="customSwitch1" class="me-2">
                                {{trans('products_import_step3.no')}}
                            </label>
                            <div class="form-check form-switch d-inline" id="draft">
                                <input name="temp_status" type="checkbox" class="custom-control-input form-check-input"
                                       id="customSwitch1" checked>
                                <label class="custom-control-label custom-label"
                                       for="customSwitch1"> {{trans('products_import_step3.yes')}}</label>
                            </div>
                        </div>
                    </div>
                </div>
                    <div class="form-group mb-4 template-name">
                    <label for="catalog">{{trans('products_import_step3.template_name')}}&nbsp;<span class="text-danger">*</span></label>
                    <div class="input-group">
                        <input required type="text" class="form-control border-end-0 border" name="temp_name" {!! (isset($template_attributes['name']) && isset($template_attributes['id'])) ? 'value="'.$template_attributes['name'].'" readonly' : null !!}>
                        <span class="input-group-append">
                            <button class="text-secondary border-start-0 border bg-white-smoke px-3 h-100" style="border-radius: 0px 4px 4px 0px" id="edit_temp_name" type="button">
                                <i class="fas fa-edit"></i>
                            </button>
                          </span>
                    </div>
                    <small id="save_temp_error" class="text-danger" style="display: none"></small>

                    <input type="hidden" class="form-control" name="temp_id" {{(isset($template_attributes['name']) && isset($template_attributes['id'])) ? 'value='.$template_attributes['id'] : null }}>
                </div>
                 <hr class="mt-1 mb-4 divider">
                <div class="form-group" id="mapping_submit_popup">
                    <input type="hidden" id="btn-text" name="btn-text" value="">
                    @if(isset($data_required['template_method_type']) && $data_required['template_method_type'] != 'shopify')
                        <button id="pro_imp_start_btn" type="submit" name="import_csv" class="btn btn-primary float-end onclick-disabled-with-loader"
                                onclick="startImporting()">{{ $text_initials['btn_name'] ??  'Start Mapping'}}</button>
                        <span id="loading_btn" class="form-control btn btn-primary btn-block">
                                    {{__('Mapping')}}
                        <span class="spinner-border spinner-border-sm ms-2"
                              role="status" aria-hidden="true"></span>
                    </span>
                    @endif

                    <button type="button" name="save_template" id="template-btn"
                            class="btn btn-outline-primary me-4 mb-1 float-end template-name" data-btn_name = "{{trans('products_import_step3.save_template_btn')}}">{{trans('products_import_step3.save_template_btn')}}</button>

                </div>
            </div>
        </div>
    </div>
</div>

@push('footer_scripts')
    <script>

        @if(!isset($template_attributes['id']))
        $('input[name="temp_status"]').attr('checked', false);
        $('input[name="temp_status"]').click();
        @endif

        $('input[name="temp_status"]').on('change', function (){
            if ($(this).is(':checked')){
                $(".template-name").show("fast");
                $('input[name="temp_name"]').prop('required', true)
            }else{
                $(".template-name").hide("fast");
                $('input[name="temp_name"]').prop('required', false)
            }
        })


        $("#edit_temp_name").click(function (event) {
            $('input[name="temp_name"]').prop('readonly', false);
        })


        // Handle change event on catalog select
        $('#catalog').on('change', function () {
            var selectedCatalog = $(this).val();

            // If a catalog is selected, show the location select
            if (selectedCatalog !== '') {
                $('#location').show();

                // Clear existing options in location select
                $('#location').empty();

                // Add the options for the selected catalog
                var locations = {!! json_encode($data_required['locations']) !!}[selectedCatalog];
                $('#location').append($('<option>', {
                    value: '',
                    text: "Choose"
                }));
                $.each(locations, function (locationId, locationName) {
                    $('#location').append($('<option>', {
                        value: locationId,
                        text: locationName
                    }));
                });

                // Trigger change event to update tooltip (if any)
                $('#location').trigger('change');
            } else {
                // If no catalog is selected, hide and reset the location select
                $('#location').empty();
                $('#location').append($('<option>', {
                    value: '',
                    text: "Choose"
                }));
            }
        });

        function startImporting() {
            let versionval = $(".version").val();
            let familyval = $(".family").val();
            let catalogval = $(".catalog").val();
            let startimport = $('#pro_imp_start_btn').val();
            let temp_name = $('input[name="temp_name"]').val();


            if (versionval === "" && familyval === "" && catalogval === "") {
                return false;
            }
            if (versionval !== "" && familyval !== "" && catalogval !== "" && startimport !== "") {
                if ($('input[name="temp_status"]').is(':checked')){
                    if (temp_name === ""){
                        return false
                    }
                }
                $('#pro_imp_start_btn').hide();
                $('#template-btn').hide();
                $('#loading_btn').css('display', 'block');
            }
        }



        //hide-back button here
        $('#import_product').on('hidden.bs.modal', function () {
            // $('input[name="temp_status"]').trigger('click');
            $("#template_checkbox_div").show();
            $("#pro_imp_start_btn").show();
        });
    </script>
@endpush
