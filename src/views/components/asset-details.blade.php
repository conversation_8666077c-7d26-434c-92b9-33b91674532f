<div id="sidebar_modal">
    <style>

        .modal .modal-dialog-aside{
            width: 346px;
            max-width:80%; height: 100%; margin:0;
            transform: translate(0); transition: transform .2s;
        }


        .modal .modal-dialog-aside .modal-content{  height: inherit; border:0; border-radius: 0;}
        .modal .modal-dialog-aside .modal-content .modal-body{ overflow-y: auto }
        .modal.fixed-left .modal-dialog-aside{ margin-left:auto;  transform: translateX(100%); }
        .modal.fixed-right .modal-dialog-aside{ margin-right:auto; transform: translateX(-100%); }

        .modal.show .modal-dialog-aside{ transform: translateX(0); }
        .modal-open .modal {
            overflow-y: hidden;
        }
        .card-container {
            /*border: 1px  #CCC solid;*/
            box-shadow: inset 0px 0px 8px -10px #CCC, inset 0px -12px 8px -10px #CCC;

        }
        #languages-1 option {
            background-color: white;
        }

        #languages-1 option:hover {
            background-color: lightgray;
        }

        #languages-1 option.selected-option {
            background-color: darkgrey;
        }
        .btn-copy,
        input {
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
        }

        input {
            padding: 0.5em;
            -webkit-border-radius: 0;
            -moz-border-radius: 0;
            border-radius: 0;
            border: 1px solid #E5E5E5;
            width: 87%;
        }

        .btn-copy {
            background-color: #8C8C8C;
            color: #fff;
            border: none;
            margin-left: -4px;
            cursor: pointer;
            min-width: 40px;
            padding: 0 !important;
            border-radius: 0px;
        }

        .clicked::after {
            color: black;
        }
        .modal-title {
            width: 290px;
        }
        .select2-container .select2-selection--multiple {

            min-height: 44px !important;
        }
        .img-thumbnail {
            padding: 0 !important;
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: .25rem;
            max-width: 100%;
            height: 32px;
            margin-right: 5px;
        }
        textarea {
            margin-top: 12px !important;
        }
    </style>

    <div id="modal_aside" class="modal fixed-left fade sidebar-modal side_bar" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-dialog-aside" role="document">
            <div class="modal-content" style="height: 100vh; right: -8px">
                <div class="modal-header" style="background-color: white">
                    <h2 class="modal-title text-truncate shared-details-typography-head"
                        @if(strlen($data['file_details']->file->name)>23)
                            data-toggle="tooltip" data-placement="top" title="{{isset($data) ? $data['file_details']->file->name : ''}}"
                        @endif


                    >{{isset($data) ? $data['file_details']->file->name : ''}}</h2>
                    <button type="button" class="btn-sm border-0 bg-white fs-24 px-0 pt-0" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">

                    {{--                        Tab Switching--}}

                    <div class="d-flex justify-content-center mt-3">
                        <ul class="nav nav-tabs justify-content-center" id="myTab" role="tablist">
                            <li class="nav-item nav-pills">
                                <a class="nav-link active switch-details justify-content-center" id="home-tab" data-bs-toggle="tab" href="#home" role="tab" aria-controls="home"
                                   aria-selected="true" style="width: 145px;">Details</a>

                            </li>
                            <li class="nav-item nav-pills mb-2">
                                <a class="nav-link switch-details justify-content-center" id="profile-tab" data-bs-toggle="tab" href="#profile" role="tab" aria-controls="profile"
                                   aria-selected="false" style="width: 145px;">Link Product</a>
                            </li>

                        </ul>
                    </div>

                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active" id="home" aria-labelledby="home-tab">
                            <img class="card-img-top img_info" src="{{isset($data) ? $data['file_details']->file->link : ''}}" onclick="open_side()" alt="Card image cap">

                            <div class="mt-3">
                                <h3 class="info-typography">Image properties</h3>

                                <div class="d-flex justify-content-between">
                                    <div class="info-width mr-4">
                                        <div class="d-flex justify-content-between ">
                                            <h4 class="info-typography head-pad me-4">Height</h4>
                                            <p class="asset-details-typo mr-4 height_info ">{{isset($data) ? $data['file_details']->file->height : ''}}px</p>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <h4 class="info-typography head-pad">Size</h4>
                                            <p class="asset-details-typo mr-4 size_info ">{{isset($data) ? $data['file_details']->file->size : ''}}kb</p>
                                        </div>
                                    </div>

                                    <div class="info-width ml-4">
                                        <div class="d-flex justify-content-between">
                                            <h4 class="info-typography head-pad me-4">Width</h4>
                                            <p class="asset-details-typo mr-4 width_info me-1">{{isset($data) ? $data['file_details']->file->width : ''}}px</p>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <h4 class="info-typography head-pad">Type</h4>
                                            <p class="asset-details-typo mr-4 type_info me-1">{{isset($data) ? $data['file_details']->file->ext : ''}}</p>
                                        </div>
                                    </div>
                                </div>
                                <a  type="button" class="btn btn-outline-dark mt-2 mb-2 px-3 download_img" href="{{route('file.download',$data['file_details']->file->id)}}"
                                >Download Image</a>
                            </div>

                            <form>
                                <div class="form-group mt-3">
                                    <label for="exampleFormControlSelect1" class="input-label mb-1">Select Folder</label>
                                    <select class="form-control input-bg" id="folder_change" name="folder_id">
                                        @isset($data['folders'])
                                            @foreach($data['folders'] as $folder)
                                                <option class="select_btn" value="{{$folder->id}}" {{in_array($folder->id, $data['linked_folder']) ? 'selected' : ''}}>{{$folder->name}}</option>
                                            @endforeach
                                        @else
                                            <option value="">No folders created</option>
                                        @endif
                                    </select>
                                </div>

                                <div class="form-group mt-4" style="position: relative;">
                                    {{--                                <label for="exampleFormControlInput1 " class="input-label">Image Url</label>--}}

                                    {{--                                <input type="url" class="form-control input-bg" id="exampleFormControlInput1" placeholder="Enter to rename" value="{{ $data['file_details']->file->link}}" disabled>--}}
                                    {{--                                <span class="copy-link-icon" style="cursor: pointer;">--}}
                                    {{--                                        <em class="fa-solid fa-link" aria-hidden="true" style="margin-top: 11px; margin-left: 10px"></em>--}}
                                    {{--                                            </span>--}}
                                    <label for="exampleFormControlInput1 " class="input-label mb-1">Image Url</label>
                                    <div>
                                        <input type="text" value="{{ $data['file_details']->file->link}}"   id="copy-input" readonly>
                                        <button class="btn btn-copy"><i class="fa fa-clipboard" style="color: black;" aria-hidden="true"></i></button>
                                    </div>
                                </div>
                                <div class="form-group mt-4">
                                    <label for="exampleFormControlInput1" class="input-label mb-1">ALT Text</label>
                                    <input class="form-control input-bg" id="image_name"  placeholder="Enter alt text" value="{{ $data['file_details']->file->name}}" >
                                </div>

                                <div class=" d-flex justify-content-end" style="margin-top: 97px;">
                                    {{--                                <button type="button" class="btn btn-outline-dark   px-3 mt-3" data-toggle="modal" data-target="#asset_upload_image">Update Image</button>--}}
                                    <button type="button" class="btn btn-primary  px-5 ml-3 mt-3" id="save_details" >Save</button>
                                </div>
                            </form>
                        </div>


                        <div class="tab-pane fade" id="profile" aria-labelledby="profile-tab">

                            <div class="form-group mt-3">
                                <h3 for="exampleFormControlSelect1" class="input-label mt-1">Select Product</h3>
                                {{--                            @if(count($products) > 0)--}}
{{--                                <label for="example">Select an option:</label>--}}
                                <select class="form-select" id="example" style="width: 300px">

                                    @isset($data)
                                        @foreach($data['products'] as $product)
                                            <option value="{{$product->id}}" data-image="{{count($product->files) > 0 ?  $product->files[0]->link : asset('img/apimio_default.jpg')}}">
                                                {{$product->sku}} {{strlen($product->get_name()) > 0 ? '(' .$product->get_name() . ')' : '' }}  </option>
                                        @endforeach
                                    @else
                                        <option value="">No products found.</option>
                                    @endif
                                </select>

                                <div class="d-flex mt-1 justify-content-end">
                                </div>
                            </div>
                            <h3 class="info-typography mt-2">Linked Product</h3>

                            <div class="mb-4 card-container" style="max-height: 438px !important; min-height: 473px !important; overflow: auto; scrollbar-width: none;">
                                @isset($data)
                                    @foreach($data['file_linked_products']->products as $product)
                                        <div class="card link-card py-3 px-2 mt-1" style="margin-left: 3px; margin-right: 3px; box-shadow: rgba(9, 30, 66, 0.25) 0px 4px 8px -2px, rgba(9, 30, 66, 0.08) 0px 0px 0px 1px;  ">
                                            <div class="d-flex justify-content-between custom-margin ">
                                                <h4 class="info-typography head-pad link-card-text ms-2 fs-16 fw-600 ">SKU</h4>
                                                <p class="asset-details-typo link-card-text me-3 fs-14 "> {{$product->sku}}</p>
                                            </div>

                                            <div class="d-flex justify-content-between">
                                                <h4 class="info-typography link-card-text head- ms-2 fs-16 fw-600">Product Name</h4>
                                                <p class="asset-details-typo link-card-text me-3 fs-14 " id="pro_id" data_id="{{$product->id}}"> {{strlen($product->get_name()) > 0  ? $product->get_name() : '-'}}</p>
                                            </div>
                                            <div>
                                                <button type="button" class="btn btn-outline-danger unlink_btn   px-3 ms-2" id="btn_unlink" data-bs-toggle="modal" data-bs-target="#unlink_product" style="opacity: 1;">Unlink
                                                    product
                                                </button>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <p></p>
                                @endif


                            </div>
                            {{--                        <input type="hidden" name="folder_id" value="" id="folderId">--}}
                            <p class="file_id"></p>

                            <button type="button" id="submit_btn" class="btn btn-primary  mb-2 px-5 ml-3 " style="float: right; margin-top: 5px;">Save</button>
                        </div>

                    </div>

                </div>
            </div> <!-- modal-dialog .// -->
        </div> <!-- modal.// -->
    </div>
</div>
@push('footer_scripts')

    <script>

    </script>
@endpush


