<style>
    .card-img-top {
        height: 172px;
        object-fit: cover;
    }

    .card-width {
        max-height: 280px !important;
    }

    .card-body {
        padding: 10px 15px 6px 15px ;
    }

    .card-text-style p {
        margin-top: -3px;
        font-size: 13px !important;
    }
    /*.card-collection {*/
    /*    grid-gap: 30px;*/
    /*}*/
</style>
<div class="card-collection">
@foreach($files as $file)
    <div data-id="{{ $file->id }}" class="card card-width card-toggle card_info"  style="cursor: pointer;">
        <img class="card-img-top"
             src="{{$file->link}}"
             data-bs-toggle="modal"
             data-bs-target="#modal_aside_left"
             {{--         onclick="open_side()"--}}
             alt="Card image cap">

        <div class="card-body card-text-style">
            <div class="d-flex justify-content-between card-head share_page_emoji "
                 style="position: relative;">
                <h5 class="card-title mb-2 "
                    style=" width: 200px; white-space: nowrap; overflow: hidden;   text-overflow: ellipsis;">{{$file->name??"no name found"}}</h5>
                <img
                    @if($file->imageQualityScoreByImage($file)['approve'] == 100)
                    src="{{asset('img/icon_good.png')}}"
                    @elseif($file->imageQualityScoreByImage($file)['warning'] == 100)
                    src="{{asset('img/icon_Fair.png')}}"
                    @elseif($file->imageQualityScoreByImage($file)['error'] == 100)
                    src="{{asset('img/icon_bad.png')}}"
                    @endif
                    style="height: 16px; margin-top: 3px"  data-bs-html="true"
                    data-trigger="hover"
                    data-toggle="tooltip"
                    data-bs-placement="right"
                    data-bs-original-title="
                 1. Image should be JPG, WebP.
                             @if(in_array($file->ext,['JPG','WebP','jpg'])) ✔ @else ❌ @endif
                                <br/>
                           2. Image should be less than 200kb.
                              @if($file->size <=200 && $file->size >0) ✔ @else ❌ @endif
                                </br>
                           3. Image should be greater then or equal to 1080 x 1080 in dimension.
                            @if(($file->width >= 1080 ) && ($file->height >= 1080)) ✔ @else  ❌ @endif">

            </div>
            <div class="d-flex justify-content-between delete_div" >
                <p class="card-text mb-1">{{$file->size}} kb's</p>
                <a class="btn-delete fa-solid fa-trash-can trash-icon"
                   style="color: #DC3545"
                   role="button"
                   data-bs-toggle="modal"
                   data-bs-target="#delete-{{$file->id}}
                   "
                ></a>
            </div>

        </div>
    </div>
        <x-gallery-delete-asset id="delete-{{$file->id}}" fileid="{{$file->id}}" Link="{{route('file.delete',$file->id)}}"></x-gallery-delete-asset>

    @endforeach
</div>

@push('footer_scripts')
    <script>
        $(function () {
            $('[data-toggle="popover"]').popover()
        })
        @auth()
        $(document).ready(function(){
            // TODO: Fahad please use the selector as whole table row but not the last column.
            let isClickable = true;
            $(".card_info").click(function() {
                if (isClickable) {
                    if (event.currentTarget !== event.target && event.currentTarget.contains(event.target) && event.target.classList.contains('btn-delete')) {
                        // Return early if it is
                        return;
                    }
                    let isProgrammaticClose = true;
                    var isTriggered = false;
                    initialize(this, isProgrammaticClose, isTriggered);

                    isClickable = false;
                    setTimeout(function() {
                        isClickable = true;
                    }, 2000); // 5000 milliseconds = 5 seconds
                }
            });
        });
        @endauth
    </script>




@endpush
<br><br>
