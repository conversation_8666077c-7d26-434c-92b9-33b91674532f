<?php
namespace Apimio\Gallery\Classes;


use Apimio\Gallery\Models\File;
use App\Classes\Plan\PlanClass;
use App\Models\Organization\Plan;
use App\Models\Channel\ShopifyChannel;
use App\Models\Organization\Organization;

class SubscribedPlan{

    public $subplan;
    public $sum;
    public function GetPlan($user){
        $organization = Organization::findorfail($user->organization_id);
        $plans = PlanClass::getPlans();
        if ($organization) {
            $checkStripeTrial = $organization->onTrial();
            $shopify = new ShopifyChannel();
            $checkShopifyTrial = $shopify->on_trial($user->organization_id) == 'trial_started' ? true : false ;
            $subscribePlanHandle =  $organization->subscribed_plan_handle();
            if($checkStripeTrial || $checkShopifyTrial) {
                $subplan = $plans->where("handle", 'standard_plan')->first();
            }
            else{
                $subplan = $plans->where("handle", $subscribePlanHandle)->first();
            }
            $this->subplan = $subplan;
            return $this;
        } else {
            redirect(route('dashboard'));
        }

    }

    public function CalculateUsedStorage(){
        $files = File::all();
        $sum = 0;
        foreach ($files as $file){
            $sum  = $sum + $file->size;
        }
        $this->sum  =round(($sum/1024)/1024,3);
        return $this;
    }

}