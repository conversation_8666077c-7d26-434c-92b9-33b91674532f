<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Cross-Origin Resource Sharing (CORS) Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your settings for cross-origin resource sharing
    | or "CORS". This determines what cross-origin operations may execute
    | in web browsers. You are free to adjust these settings as needed.
    |
    | To learn more: https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
    |
    */

     'paths' => ['api/*', 'sanctum/csrf-cookie'], // Ensure CSRF cookie is allowed
    'allowed_methods' => ['*'],
    'allowed_origins' => [env('APP_URL', 'http://localhost')], // Use APP_URL from environment variables
    'allowed_origins_patterns' => [],
    'allowed_headers' => ['*'],
    'supports_credentials' => true, // Important for authentication cookies
    'credentials' => true, // Enable cookies

];
