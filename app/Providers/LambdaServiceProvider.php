<?php

namespace App\Providers;

use Illuminate\Foundation\PackageManifest;
use Illuminate\Support\ServiceProvider;

class LambdaServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        if (env('APP_ENV') === 'lambda') {
            // Override the PackageManifest path to use /tmp/cache instead of /var/task
            $this->app->singleton(PackageManifest::class, function ($app) {
                return new PackageManifest(
                    $app['files'], // At this point, 'files' will be available
                    $app->basePath(),
                    '/tmp/cache/packages.php'
                );
            });
        }
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // Ensure the /tmp/cache directory exists
        if (env('APP_ENV') === 'lambda' && !is_dir('/tmp/cache')) {
            mkdir('/tmp/cache', 0755, true);
        }
    }
}
