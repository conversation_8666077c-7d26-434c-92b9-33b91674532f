<?php

namespace App\Traits\Billing;

use App\Models\Invite\Invite;
use App\Classes\Plan\PlanClass;
use App\Models\Channel\Channel;
use App\Models\Product\Product;
use App\Models\Product\Version;
use App\Models\Organization\Plan;
use Laravel\Cashier\Subscription;
use Illuminate\Support\Facades\DB;
use App\Models\Cashier\SubscriptionItem;
use App\Models\Billing\ShopifySubscription;

trait BillingTrait {

    /**
     * Check if organization has an active subscription.
     *
     * @return bool
     * */
    public function is_subscribed() {
        return $this
                ->stripe_subscriptions()
                ->where("stripe_status", "active")
                ->count() > 0
            || $this
                ->shopify_subscriptions()
                ->where("status", "active")
                ->count() > 0;
    }

    public function plans() {

        // check for stripe subscription
        $subscriptions = $this
            ->stripe_subscriptions()
            ->get();
        $plan_collection = collect();

        foreach ($subscriptions as $subscription) {
            $plan = Plan::where("stripe_monthly_id", $subscription->stripe_price)
                ->orWhere("stripe_yearly_id", $subscription->stripe_price)
                ->first();

            if($plan) {
                $plan->status = $subscription->stripe_status;
                $plan->current_stripe_plan = $subscription->stripe_price;
                $plan_collection->push(
                    $plan
                );
            }
        }

        // check for shopify subscription
        $subscriptions = $this
            ->shopify_subscriptions()
            ->get();

        foreach ($subscriptions as $subscription) {
            $plan = Plan::where("id", $subscription->plan_id)
                ->latest('id')
                ->first();
            if($plan) {
                $plan->status = $subscription->status;
                $plan->current_shopify_plan = $subscription->plan_id;
                $plan_collection->push(
                    $plan
                );
            }
        }
        return $plan_collection;
    }

    public function downgradable_plan_ids() :array {
        $downgrade_plan_ids = array();
        $plans = Plan::all();
        $current_plans = $this->plans()->first();

        if ($current_plans && $current_plans->status != 'canceled') {
            foreach ($plans as $plan) {
                $downgrade_plan_ids[] = $plan->stripe_monthly_id;
                if ($current_plans->current_stripe_plan == $plan->stripe_monthly_id) {
                    break;
                }
                $downgrade_plan_ids[] = $plan->stripe_yearly_id;
                if ($current_plans->current_stripe_plan == $plan->stripe_yearly_id) {
                    break;
                }
            }
        }
        return $downgrade_plan_ids;
    }

    public function shopify_downgrade_plan_ids() {
        $downgrade_plan_ids = array();
        $plans = Plan::all();
        $current_plans = $this->plans()->last();
        if ($current_plans) {
            foreach ($plans as $plan) {
                $downgrade_plan_ids[] = $plan->id;
                if ($current_plans->current_shopify_plan == $plan->id) {
                    break;
                }
            }
        }
        return $downgrade_plan_ids;
    }

    //return limit of vendors allowed in the subscribed plan
    public function invite_limit() {
        if ($this->is_subscribed()) {
            $subscribed_plan = DB::table('subscriptions')->where('organization_id', $this->id)->value('stripe_price');
            if ($subscribed_plan) {
                return Plan::where('stripe_monthly_id', $subscribed_plan)->orWhere('stripe_yearly_id', $subscribed_plan)->value('no_of_vendors');
            } else {
                $plan_id = ShopifySubscription::where('organization_id', $this->id)->value('plan_handle');
                return Plan::where('id', $plan_id)->value('no_of_vendors');
            }
        } else {
            return 0;
        }
    }

    public function subscription_type() {
        if (DB::table('subscriptions')->where('organization_id',$this->id)->first()) {
            return "stripe";
        } else {
            return "shopify";
        }
    }

    /**
     * return allowed number of team members
     *
     * @return int
     * */
    public function team_limit() {
        if ($this->is_subscribed()) {
            $subscribed_plan = DB::table('subscriptions')->where('organization_id', $this->id)->value('stripe_price');
            if ($subscribed_plan) {
                return Plan::where('stripe_monthly_id', $subscribed_plan)->orWhere('stripe_yearly_id', $subscribed_plan)->value('no_of_team_members');
            } else {
                $plan_id = ShopifySubscription::where('organization_id', $this->id)->value('plan_id');
                return Plan::where('id', $plan_id)->value('no_of_team_members');
            }
        } else {
            return 0;
        }
    }

    /**
     * return handle of subscribed plan
     *
     * @return string
     * */
    public function subscribed_plan_handle()
    {
        $subscription = Subscription::where('organization_id',$this->id)->first();
        $stripe_plan = null;
        $stripe_plan_items = null;
        if($subscription){
            $stripe_plan = $subscription->stripe_price;
            $stripe_plan_items = SubscriptionItem::where('subscription_id',$subscription->id)->get()->pluck('stripe_price');
        }
        if ($subscription) { //if user is on stripe subscription
            return PlanClass::getPlans()->whereIn('stripe_monthly_id',$stripe_plan_items)->value('handle');
        } else { //checking shopify subscription
            $plan_id = ShopifySubscription::where('organization_id',$this->id)->value('plan_handle');
            if ($plan_id) { // if user have subscribed through shopify
                return PlanClass::$plans->where('handle',$plan_id)->value('handle');
            } else { // when user have not subscribed any plan
                null;
            }
        }
    }

    /**
     * check number of allowed options in trial
     *
     * @return boolean
     */
    public function allowed_options()
    {
        if ($this->onTrial()) {

            //checking allowed products
            if (Product::count_variant() > 1000) {
                return false;
            }

            //checking allowed team members
            $invites_count = Invite::where('organization_id_sender',$this->id)->count();
            $allowed_no_of_channel_partners = Plan::where('handle','community_plan')->value('no_of_vendors');
            if ($invites_count > $allowed_no_of_channel_partners) {
                return false;
            }

            //checking allowed languages
            $version_count = Version::where('organization_id',$this->id)->count();
            $allowed_no_of_languages = Plan::where('handle','community_plan')->value('no_of_languages');
            if ($version_count > $allowed_no_of_languages) {
                return false;
            }


            //checking allowed stores
            $channels_count = Channel::where('organization_id',$this->id)->count();
            $allowed_no_of_channels = Plan::where('handle','community_plan')->value('no_of_channels');
            if ($channels_count > $allowed_no_of_channels) {
                return false;
            }
            return true;
        } else {
            return true;
        }
    }
}
