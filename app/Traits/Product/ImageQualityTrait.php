<?php

namespace App\Traits\Product;


use App\Models\Organization\File;
use App\Models\Product\Product;

trait ImageQualityTrait {
    public function __construct()
    {

    }

    public function imageQualityScoreByProductId()
    {
        $approve = 0;
        $warning = 0;
        $error = 0;
        $self = $this;

        $product = new Product();
        $product = $product->fetch(function ($product) use($self) {
            return $product->find($self->id);
        });

        $file_obj = new File();
        foreach ($product->files as $file) {
            if(($file->pivot->uploaded_for != 'youtube') && ($file->pivot->uploaded_for != 'file')) {
                $status = $file_obj->get_img_status($file);
                if ($status == "approve")
                    $approve++;
                else if ($status == "warning")
                    $warning++;
                else if ($status == "error")
                    $error++;
            }
        }


        $total = $approve + $warning + $error;

        if ($total > 0) {
            return [
                "approve" => (int)round(($approve * 100) / $total),
                "warning" => (int)round(($warning * 100) / $total),
                "error" => (int)round(($error * 100) / $total)
            ];
        }

        return [
            "approve" => $approve,
            "warning" => $warning,
            "error" => $error
        ];
    }

    public function TreeEditor($image)
    {
        $approve = 0;
        $warning = 0;
        $error = 0;
        $self = $this;

        $file_obj = new File();
        $status = $file_obj->get_img_status($image);
        if ($status == "approve")
            $approve++;
        else if ($status == "warning")
            $warning++;
        else if ($status == "error")
            $error++;



        $total = $approve + $warning + $error;

        if ($total > 0) {
            return [
                "approve" => (int)round(($approve * 100) / $total),
                "warning" => (int)round(($warning * 100) / $total),
                "error" => (int)round(($error * 100) / $total)
            ];
        }

        return [
            "approve" => $approve,
            "warning" => $warning,
            "error" => $error
        ];
    }

}
