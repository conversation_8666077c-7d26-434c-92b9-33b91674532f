<?php

namespace App\Events;

use App\Models\Product\Product;
use Illuminate\Broadcasting\Channel;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class ChannelUpdateStatusEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Product $product; // product variableis used to store incomming product object

    public array $data; // data variable is used to store data in array for example we are storing shopify_product_id

    public bool $isUpdated; // isUpdate variable is used for checking any action of updation in product

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Product $product , array $data = [], bool $isUpdated = false)
    {
        $this->product = $product;

        $this->isUpdated = $isUpdated;

        $this->data = $data;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
