<?php

namespace App\Policies;

use App\Models\Billing\ShopifySubscription;
use App\Models\Invite\Invite;
use App\Models\Organization\Organization;
use App\Models\Organization\Plan;
use App\Models\Organization\TeamInvite;
use App\Models\Product\Version;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Support\Facades\Auth;

class LanguageLimitPolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }



    /**
     * @param  \App\User  $user
     * @return mixed
     */
    public function createLanguage( $user)
    {
        if (get_class($user) == User::class) {

            $org = Organization::where('id', $user->organization_id)->first();
        } else {
            $org = $user;
        }
        if ($org) {
            //check if subscription exists
            if ($org->is_subscribed()) {
                if ($org->subscribed('default')) { //if user subscribed through stripe
                    if ($org->subscribed_plan_handle() == 'community_plan') {
                        $version = Version::where('organization_id', $org->id)->count();
                        $no_of_languages = Plan::where('handle', $org->subscribed_plan_handle())->value('no_of_languages');
                        if ($no_of_languages == 0) {
                            return  $this->allow();
                        } else {
                            return $version < $no_of_languages ? $this->allow() : $this->deny("<b>Language limit reached.</b> <br> Please <a href='" . route('billing') . "'>upgrade</a> your plan to add more languages.");
                        }
                    }
                } elseif (Auth::user()->shopify_shop_id) { //if user subscribed through shopify
                    $price = ShopifySubscription::where('organization_id', Auth::user()->organization_id)->value('price');
                    if ($price) {
                        $version = Version::where('organization_id', $org->id)->count();
                        $no_of_languages = Plan::where('handle', $org->subscribed_plan_handle())->value('no_of_languages');
                        if ($no_of_languages == 0) {
                            return  $this->allow();
                        } else {
                            return $version < $no_of_languages ? $this->allow() : $this->deny("<b>Language limit reached.</b> <br> Please <a href='" . route('billing') . "'>upgrade</a> your plan to add more languages.");
                        }
                    }
                }
            } else {
                $invite = new Invite();
                if ($invite->IsInvited(Auth::user()->email)) { //if user is invited and on free plan
                    $version = Version::where('organization_id', $org->id)->count();
                    $no_of_languages = Plan::where('handle', 'free_plan')->value('no_of_languages');
                    return $version < $no_of_languages ? $this->allow() : $this->deny("<b>Language limit reached.</b> <br> Please <a href='" . route('billing') . "'>upgrade</a> your plan to add more languages.");
                } else { //if user is on trial
                    $version = Version::where('organization_id', $org->id)->count();
                    $no_of_languages = Plan::where('handle', 'standard_plan')->value('no_of_languages');
                    if ($no_of_languages == 0) {
                        return  $this->allow();
                    } else {
                        return $version < $no_of_languages ? $this->allow() : $this->deny("<b>Language limit reached.</b> <br> Please <a href='" . route('billing') . "'>upgrade</a> your plan to add more languages.");
                    }
                }
            }
        } else {
            return $this->deny("Please add organization first.");
        }
    }
}
