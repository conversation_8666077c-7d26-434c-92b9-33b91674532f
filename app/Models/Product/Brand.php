<?php

namespace App\Models\Product;

use App\Rules\UniqueManyToMany;
use FontLib\Table\Type\name;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class Brand extends Model
{
    private $data, $filter;

    protected $fillable = ['name', 'is_default','organization_id'];
    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    public static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if(!isset($model->organization_id)) {
                if (Auth::check()) {
                    $model->organization_id = Auth::user()->organization_id;
                }
            }
        });

        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', Auth::user()->organization_id);
            }
        });
    }

    public function set_data(array $data)
    {
        if (isset($data['_token']))
            unset($data['_token']);

        $this->data = $data;
        return $this;
    }


    public function rules()
    {
        $attributes = [];

        if(isset($this->data["organization_id"])) {
            $attributes["organization_id"] = $this->data["organization_id"];
        }

        if(isset($this->data["id"])) {
            $attributes["id"] = $this->data["id"];
        }

        return [
            'name' => ['required', 'max:255', new UniqueManyToMany(new Brand(), $attributes)]
        ];
    }

    public function validation()
    {
        $validator = Validator::make($this->data, $this->rules());
        if (!$validator->fails()) {
            $this->attributes = $this->data;
        }
        return $validator;
    }

    public function store($error_callback, $success_callback)
    {

        $validation = $this->validation();
        if ($validation->fails()) {
            return $error_callback($validation->errors());
        }


        if (isset($this->data['id'])) {

            $brand = $this->find($this->data['id']);
        }
        else {
            $brand = $this;
        }

        if (isset($this->data['organization_id']))
        {
            $brand->organization_id = $this->data['organization_id'];
        }
        $brand->name = $this->data["name"];


        $brand->save();
        return $success_callback($brand);
    }

    public static function findOrCreateForProduct($name, $product)
    {
        // Find the brand with the given name for the specified product
        $brand = static::where('name', $name)
            ->where('organization_id' , $product->organization_id)
            ->first();

        // If the brand exists, return it
        if ($brand) {
            return $brand;
        }

        // Otherwise, create a new brand and associate it with the product
        $brand = new static();
        $brand->name = $name;
        $brand->organization_id = $product->organization_id;
        $brand->save();
        return $brand;
    }

    public function show()
    {

        $data = self::get();

        return $data;
    }

    public function set_id($id)
    {
        $this->id = $id;
        return $this->find($this->id);
    }

    public function fetch()
    {
        $attribute = $this;
        if ($this->filter) {
            if (isset($this->filter["name"]))
                $attribute = $attribute->where("name", "LIKE", "%" . $this->filter["name"] . "%");
        }
        return $attribute->orderBy("id", "DESC")->paginate(8);
    }

    public function fetch_data($callback)
    {
        $brands = $this::query();
        if($callback)
            $brands = $callback($brands);
        return $brands;
    }

    public function filter($filter)
    {
        $this->filter = $filter;
        return $this;
    }

    public function delete_by_id($id)
    {
        $this->set_id($id)->delete();
    }

    public function search_by_name($name)
    {
        $data = self::where('name', 'like', '%' . $name['q'] . '%')->get();
        return $data;
    }

    public function get_total_count()
    {
        return $this->count();
    }

    //===relationships ===//
    public function products()
    {
        return $this->belongsToMany(Product::class);
    }

}
