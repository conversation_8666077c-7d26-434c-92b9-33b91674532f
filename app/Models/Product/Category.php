<?php

namespace App\Models\Product;

use App\Models\Channel\CategoryChannel;
use App\Models\Organization\Organization;
use App\Rules\UniqueManyToMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class Category extends Model
{
    private $data, $filter;

    protected $fillable = [
        'name',
        'description',
        'is_default',
        'status',
        'organization_id',
        'category_id',
        'response'
    ];
    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    public static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if(!isset($model->organization_id)) {
                if (Auth::check()) {
                    $model->organization_id = Auth::user()->organization_id;
                }
            }
        });

        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', Auth::user()->organization_id);
            }
        });
    }

    public function set_data($data)
    {
        if (isset($data['_token']))
            unset($data['_token']);

        $this->data = $data;
        return $this;
    }

    public function set_id($id)
    {
        $this->id = $id;
        return $this->find($this->id);
    }

    public function rules()
    {
        $attributes = [];

        if(isset($this->data["organization_id"])) {
            $attributes["organization_id"] = $this->data["organization_id"];
        }

        if(isset($this->data["id"])) {
            $attributes["id"] = $this->data["id"];
        }

        return [
            'name' => ['required', 'max:255', new UniqueManyToMany(new Category(), $attributes)],
            'description' => 'max:255',
        ];
    }

    public function validation()
    {
        $validator = Validator::make($this->data, $this->rules());
        if (!$validator->fails()) {
            $this->attributes = $this->data;
        }
        return $validator;
    }

    public function fetch()
    {
        $attribute = $this;
        if ($this->filter) {
            if (isset($this->filter["name"]))
                $attribute = $attribute->where("name", "LIKE", "%" . $this->filter["name"] . "%");
        }
        return $attribute->orderBy("id", "DESC")->paginate(8);
    }

    public function fetch_data($callback)
    {
        $categories = $this::query();
        if ($callback)
            $categories = $callback($categories);
        return $categories;
    }

    public function filter($filter)
    {
        $this->filter = $filter;
        return $this;
    }

    public function show()
    {
        $data = $this->paginate(8);
        return $data;
    }



    public function delete_by_id($id)
    {
        $this->set_id($id)->delete();
    }

    public function store($error_callback, $success_callback)
    {


        $validation = $this->validation();
        if ($validation->fails()) {
            return $error_callback($validation->errors());
        }
        if (isset($this->data['id'])) {
            $category = $this->find($this->data['id']);

            //Setting parent category id to null if user removes parent category
//            if (!isset($this->data['category_id'])) {
//                $category->category_id = null;
//                $category->save();
//            }

        } else {
            $category = $this;
        }

        if (isset($this->data['organization_id'])){
            $category->organization_id = $this->data['organization_id'];
        }
        $category->name = $this->data['name'];
        $category->description = isset($this->data['description']) ? $this->data['description'] : null;
        $category->is_default = isset($this->data['is_default']) ? 1 : 0;
        $category->status = isset($this->data['status']) ? 1 : 0;
        $category->category_id = $this->data['category_id'] ?? null;
        if(isset($this->data['response'])){
            $category->response = json_encode($this->data['response']);
        }
        $category->save();
        // ADD OR UPDATE CATEGORY CHANNEL
        if(isset($this->data['response']) ){

            $category_channels = CategoryChannel::query()
                ->where('category_id',$category->id)
                ->where('store_connect_type','shopify')
                ->where('channel_id',$this->data['response']['channel_id'])
                ->first();
            if(!$category_channels){
                $category_channels = new CategoryChannel();
            }
            $category_channels->category_id = $category->id;
            $category_channels->channel_id = $this->data['response']['channel_id'];
            $category_channels->store_connect_type =$this->data['response']['type'] ;
            $category_channels->store_connect_id = $this->data['response']['collection_id'];
            $category_channels->save();

        }
        return $success_callback($category);
    }

    public static function findOrCreateForProduct($name, $product)
    {
        // Find the brand with the given name for the specified product
        $category = static::where('name', $name)
            ->where('organization_id' , $product->organization_id)
            ->first();

        // If the brand exists, return it
        if ($category) {
            return $category;
        }

        // Otherwise, create a new brand and associate it with the product
        $category = new static();
        $category->name = $name;
        $category->organization_id = $product->organization_id;
        $category->save();
        return $category;
    }

    public function get_total_count()
    {
        return $this->count();
    }


    /**
     * fetch names of parent with child category e.g (parent > child > child)
     *
     * @param $category_id
     * @return array
     */
    public function get_parent_categories_with_name($category_id = null): array
    {
        $temp_arrays =array();
        $parent_array = array();
        $category_obj = Category::query();
        if ($category_id) {
            $category= $category_obj->where('id',$category_id)->get()->first();
        }
        if (isset($category) && $category != null){
            if ($category->category_id != null){
                $temp_arrays = $this->get_parent_categories_with_name($category->category_id);
            }
            $parent_array[] = $category->name;
        }
        return array_merge($temp_arrays,$parent_array);
    }


    /**
     * @return array
     */
    public function get_parent_category( $id = null , int $parent_category_id = null, string $type = null): array
    {
        $category_data = array();
        $parent_array = array();
        $temp_array = array();
        $categories = Category::query();
        if($id){
            $categories = $categories->where('id','!=',$id);
        }
        if($parent_category_id){
            $categories = $categories->where('id',$parent_category_id);
        }
        $categories = $categories->whereNull('category_id')->get();

        foreach ($categories as $category){
            $child_categories = Category::query()
                ->where('category_id',$category->id)
//                ->where('id','!=',$id)
                ->get();

            if (count($child_categories)>0){
                $cat_params = ['category' => $category, 'id' => $id ? $id : null, 'type'=> $type ? $type : null];
                if(isset($type)) {
                    $temp_array[] = $this->getCategories($cat_params);
                }else{
                    $temp_array[] = $this->getCategories($cat_params);
                }
            }

            if(Category::query()->where('category_id',$category->id)->count()  == 0){
                $parent_array[] = [
                    'value'=>$category->id,
                    'name'=>$category->name,
                ];
            }
            $category_data =   array_merge($temp_array,$parent_array);
        }
        return $category_data;
    }

    /**
     * @param $data
     * @return array
     */
    private function getCategories($data): array
    {
        $category = $data['category'];
        $id = isset($data['id']) ? $data['id'] : null;
        $type = isset($data['type']) ? $data['type'] : null;

        $temp_array_child =array();
        $child_categories = Category::query();
        if ($id) {
            $child_categories = $child_categories->where('id','!=',$id);
        }
        $child_categories = $child_categories->where('category_id',$category->id)
            ->get();

        if (!empty($child_categories)){
            foreach ($child_categories as $child){
                if(isset($type)) {
                    $temp_array_child['children'][] = $this->getCategories(['category' => $child, 'id'=>$id]);
                }else{
                    $temp_array_child['children'][] = $this->getCategories(['category' => $child, 'id'=>$id]);
                }
            }
        }

        $parent_array = [
            'value'=>$category->id,
            'name'=>$category->name,
        ];

        return array_merge($temp_array_child,$parent_array);
    }

    /**
     * @param $id it is id of the child category which is used to get its parent categories
     * @return void
     */
    public function get_categories_parent($id){
        $category = Category::findOrFail($id);

        if($category){
            while($category->category_id != null){
                $category = Category::findOrFail($category->category_id);
            }
        }

        return $this->get_parent_category(null, $category->id);


    }
    //--=====relationships =====--//
    public function products()
    {
        return $this->belongsToMany(Product::class);
    }

    public function category_channels()
    {
        return $this->hasMany(CategoryChannel::class);
    }

    public function channel()
    {
        return $this->hasOne(CategoryChannel::class);
    }


    /**
     * Local scopes
     */
    public function scopeFilter($query, $request) {
        if($request->filled("id")) {
            $query->where("id", $request->get("id"));
        }

        if($request->filled("organization_id")) {
            $query->where("organization_id", $request->get("organization_id"));
        }

        if($request->filled("name")) {
            $query->where("name", "LIKE", "%".$request->get("name")."%");
        }

        if($request->filled("is_default")) {
            $query->where("is_default", $request->get("is_default"));
        }

        if($request->filled("status")) {
            $query->where("status", $request->get("status"));
        }
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function children()
    {
        return $this->hasMany(Category::class, 'category_id');
    }

//    public function attributes()
//    {
//        return $this->hasMany(Attribute::class)->withoutGlobalScopes();
//    }
}
