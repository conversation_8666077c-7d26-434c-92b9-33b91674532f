<?php

namespace App\Models\Invite;

use App\Models\Channel\Channel;
use App\Models\Channel\ChannelProductStatus;
use App\Models\Organization\Organization;
use App\Models\Product\Product;
use App\Notifications\ApimioNotification;
use App\Notifications\Invite\RetailerInvite;
use App\Rules\ImageDimension;
use App\Rules\UniqueManyToMany;
use App\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class Invite extends Model
{

    private $data, $user,$filter;

    protected $fillable = ['fname', 'lname', 'email', 'phone', 'organization_id_sender', 'organization_id_receiver', 'is_accepted', 'is_declined', 'token', 'type', 'designation'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    protected static function booted()
    {
        parent::booted();
        static::addGlobalScope('organization_id_sender', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where("organization_id_sender", Auth::user()->organization_id??Auth::user()->id);
            }
        });

        static::creating(function ($organization) {
            if (Auth::check())
                $organization->organization_id_sender = Auth::user()->organization_id??Auth::user()->id;
        });
    }
    public function fetch()
    {
        $vendor = $this;

        if ($this->filter) {
            if (isset($this->filter["fname"])) {
                $vendor = $vendor->where("fname", "LIKE", "%" . $this->filter["fname"] . "%");
            }
        }

        return $vendor->orderBy("id", "DESC")->paginate(8);
    }

    public function filter($filter)
    {
        $this->filter = $filter;
        return $this;
    }

    public function set_user($user)
    {
        $this->user = $user;
        return $this;
    }

    /**
     * Get data attribute.
     *
     * @return string
     */
    public function get_data()
    {

    }

    /**
     * Set data attribute.
     *
     *
     * @param array
     * @return Organization
     */
    public function set_data(array $data)
    {
        $this->data = $data;
        return $this;
    }

    /**
     * @return array
     */
    public function get_Invite($email)
    {
        $invites = Invite::query()
            ->withoutGlobalScopes()
            ->where("email", $email)
            ->where("is_accepted", "0")
            ->where("is_declined", "0")
            ->get();
        return $invites;

    }

    /**
     * Get email attribute.
     *
     * @return boolean
     */
    public function generate_invite_notifications($user)
    {

        $invitation_count = $this->withoutGlobalScope('organization_id_sender')->where('email', $user->email)
            ->where('is_accepted', '0')
            ->where('is_declined', '0')
            ->count();

        if ($invitation_count > 0) {
            $details = [
                'subject' => "New pending invitation",
                'greeting' => 'Hi ' . $user->fname,
                'body' => 'You have ' . $invitation_count . ' invitation pending.',
                'thanks' => 'Thank you for using ' . env("APP_NAME", "Apimio"),
                'actionText' => 'Show Invitations',
                'actionURL' => url(route('vendor.index')),
            ];

            $user->notify(new ApimioNotification($details));

            return true;
        } else {
            return false;
        }
    }

    public function store($error_callback, $success_callback)
    {
        $validation = $this->validation();
        if ($validation->fails()) {
            return $error_callback($validation->errors());
        }

        DB::beginTransaction();
        try {
            if (isset($this->data["id"])) {
                $obj = $this->find($this->data["id"]);
            } else {
                $obj = $this;
            }
            $obj->email = $this->data["email"] ?? null;
            $obj->fname = $this->data["fname"] ?? null;
            $obj->lname = isset($this->data["lname"]) ? $this->data["lname"] : $obj->lname;
            $obj->designation = isset($this->data["designation"]) ? $this->data["designation"] : $obj->designation;
            $obj->phone = isset($this->data["phone"]) ? $this->data["phone"] : $obj->phone;
            $obj->token = isset($this->data["token"]) ? $this->data["token"] : Str::random(25);
            $obj->is_accepted = isset($this->data["is_accepted"]) ? $this->data["is_accepted"] : 0;
            $obj->type = isset($this->data["type"]) ? $this->data["type"] : $obj->type ?? "vendor";
            $obj->save();

            if (isset($this->data["channel_ids"])) {
                if (isset($this->data["id"])) {
                    $obj->channels()->sync($this->data["channel_ids"]);
                } else {
                    $obj->channels()->attach($this->data["channel_ids"]);
                }
            }

            DB::Commit();

            if ($obj->wasChanged(['fname', 'lname', 'phone', 'type', 'is_accepted', 'designation'])) {
                return $success_callback($obj);
            }
            // try {
            //     Notification::route("mail", $obj->email)->notify(new RetailerInvite($this->user, $obj));
            // } catch (\Exception $e) {
            //     Log::error($e);
            //     return $error_callback(["main" => 'User is added but invitation is not sent. Please copy the link to send it to user.']);
            // }
            return $success_callback($obj);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error($e);
            return $error_callback(["main" => 'Something went wrong. Please try again later.']);
        }

    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return \Illuminate\Contracts\Validation\Validator
     */
    public function validation()
    {
        $validator = Validator::make($this->data, $this->rules(), [
            // 'fname.alpha' => 'First name can only contain letters',
            // 'lname.alpha' => 'Last name can only contain letters',
            // 'channel_ids.required' => 'catalog is required',
            'phone.max' => 'Phone number is too long',
        ], [
            'fname' => 'Name',
        ]);

        $validator->after(function ($validator) {
            $data = $validator->getData();

            if (isset ($data["email"]) ? !empty ($data["email"]) : false) {
                if ($data["email"] == $this->user->email) {
                    $validator->errors()->add("email", "You cannot invite your self as a retailer.");
                }
                if (!isset ($data["id"])) {
                    if (
                        Invite::where("email", $data["email"])
                            ->where("organization_id_sender", Auth::user()->organization_id)
                            ->where("type", $data["type"])->count() > 0
                    ) {
                        $validator->errors()->add("email", "The email should be unique.");
                    }
                }
                if (isset ($data["id"])) {
                    if (Invite::where("id", '!=', $data["id"])->where('email', $data["email"])->count() > 0) {
                        $validator->errors()->add("email", "The email can only be assigned to one Vendor.");
                    }
                }
            }
        });



        return $validator;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $attributes = [];

        if (isset($this->data["organization_id"])) {
            $attributes["organization_id"] = $this->data["organization_id"];
        }

        if (isset($this->data["id"])) {
            $attributes["id"] = $this->data["id"];
        }

        return [
            'fname' => ['required', 'max:255', new UniqueManyToMany(new Invite(), $attributes)],
            // 'lname' => ['alpha'],
            // 'email' => ['required', 'max:76'],
            // 'channel_ids' => 'required',
            'phone' => 'max:16',
            // "type" => "required"
        ];
    }

    /**
     * Relationships
     */

    public function channels()
    {
        return $this->belongsToMany(Channel::class);
    }

    public function storeVendor($error_callback, $success_callback)
    {

        DB::beginTransaction();
        try {

            $obj = $this;
            $obj->fname = $this->data['name'];
            $obj->organization_id_sender = $this->data['organization_id'];
            $obj->type = $this->data['type'];
            $obj->save();
            DB::commit();

            return $success_callback($obj);
        } catch (\Exception $e) {
            DB::rollBack();
            return $error_callback(["main" => 'Something went wrong. Please try again later.']);
        }
    }

    public function get_status_badge()
    {
        $status = $this->get_status();

        if ($status == 1) {
            return '<span class="status status-success">Connected</span>';
        } else if ($status == 2) {
            return '<span class="status status-danger">Declined</span>';
        } else if ($status == 3) {
            return '<span class="status status-warning">Received</span>';
        } else if ($status == 4) {
            if ($this->email == Null) {
                return '<span class="status status-draft ">Draft</span>';
            } else {
                return '<span class="status status-publish ">Sent</span>';
            }
        } else {
            return '<span class="status status-danger ">Disconnected</span>';
        }
    }

    public function get_status()
    {
        if ($this->is_accepted) {
            if ($this->is_declined) {
                return 5; //disconnected
            } else {
                return 1;// connected
            }
        } else if ($this->is_declined) {
            return 2; // declined
        } else if (Auth::user()->email == $this->email) {
            return 3; // received
        } else {
            return 4; // sent
        }
    }

    public function get_products()
    {
        $products = new Product();
        if (Auth::user()->email == $this->email) {
            $channel_ids = array();
            if ($this->channels_without_scope) {
                foreach ($this->channels_without_scope as $channel) {
                    $channel_ids[] = $channel->pivot->channel_id;
                }
            }
            $organization_id = $this->organization_id_sender;
            return $products->fetch(function ($product) use ($organization_id, $channel_ids) {
                return $product->withoutGlobalScopes()->where("organization_id", $organization_id)
                    ->whereHas('channels', function ($q) use ($channel_ids) {
                        $q->whereIn('channel_id', $channel_ids);
                    })
                    ->paginate(10);
            });
        } else {
            $channel_ids = array();
            if ($this->channels_without_scope) {
                foreach ($this->channels_without_scope as $channel) {
                    $channel_ids[] = $channel->pivot->channel_id;
                }
            }

            $organization_id = $this->organization_id_receiver;
            return $products->fetch(function ($product) use ($organization_id, $channel_ids) {
                return $product->withoutGlobalScopes()->where("organization_id", $organization_id)
                    ->whereHas('channels', function ($q) use ($channel_ids) {
                        $q->whereIn('channel_id', $channel_ids);
                    })
                    ->paginate(10);
            });
        }
    }

    public function get_product_by_id($product_id, $invite_channel_ids = null, $authuser)
    {
        if ($this->organization_id_sender == $authuser->organization_id || $this->organization_id_receiver == $authuser->organization_id) {
            $products = new Product();
            //   $organization_id = $this->organization_id_sender;
            if ($invite_channel_ids) {
                return $products->fetch(function ($product) use ($product_id, $invite_channel_ids) {
                    return $product->withoutGlobalScopes()->with([
                        'channels' => function ($q) use ($invite_channel_ids) {
                            $q->whereIn('channel_id', $invite_channel_ids);
                        }
                    ])->find($product_id);
                });
            } else {
                return $products->fetch(function ($product) use ($product_id) {
                    return $product->withoutGlobalScopes()->find($product_id);
                });
            }

        } else {
            return null;
        }
    }

    public function get_product_id($data)
    {

        $product_status = ChannelProductStatus::where('type', 'clone')
            ->whereJsonContains('response', ['product_id' => (int) $data['product_id']])
            ->whereJsonContains('response', ['channel_id' => $data['product_channel_id']])
            ->with('channel_product')
            ->first();
        return $product_status ? $product_status->channel_product ? $product_status->channel_product->product_id : 0 : 0;

    }

    public function set_id($id)
    {
        $this->id = $id;
        return $this->find($this->id);
    }

    public function delete_by_id($id)
    {
        $this->set_id($id)->delete();
    }

    public function get_catalogs()
    {
        return implode(", ", array_column($this->channels_without_scope->toArray(), "name"));
    }

    public function get_vendor()
    {
        return $this->organization_sender_without_scope->users_without_scope->first();
    }

    public function scopeRetailer($query)
    {
        return $query->withoutGlobalScopes()
            ->with('organization_sender_without_scope.users_without_scope')
            ->where(function ($q) {
                $q->where("organization_id_sender", Auth::user()->organization_id);
                $q->where("email", "!=", Auth::user()->email)
                    ->where("type", "retailer");
            })
            ->orWhere(function ($q) {
                $q->where("organization_id_sender", "!=", Auth::user()->organization_id);
                $q->where("email", Auth::user()->email)
                    ->where("type", "vendor");
            });
    }

    public function scopeVendor($query)
    {
        return $query->withoutGlobalScopes()
            ->where(function ($q) {
                $q->where("organization_id_sender", "!=", Auth::user()->organization_id);
                $q->where("email", Auth::user()->email)
                    ->where("type", "retailer");
            })
            ->orWhere(function ($q) {
                $q->where("organization_id_sender", Auth::user()->organization_id);
                $q->where(function ($q1) {
                    $q1->where("email", "!=", Auth::user()->email)
                        ->orWhereNull("email");
                })->where("type", "vendor");
            });
    }

    public static function findOrCreateForProduct($name, $product)
    {
        // Find the invite with the given name for the specified product
        $invite = Invite::where('fname', $name)
            ->where('organization_id_sender', $product->organization_id)
            ->first();

        // If the invite exists, return it
        if ($invite) {
            return $invite;
        }

        // Otherwise, create a new invite and associate it with the product
        $invite = new Invite();
        $invite->organization_id_sender = $product->organization_id;
        $invite->fname = $name;
        $invite->is_declined = 0;
        $invite->type = 'vendor';
        $invite->save();
        return $invite;
    }

    public function IsInvited($email)
    {
        return Invite::withoutGlobalScopes()->where("email", $email)->get()->first() ? true : false;
    }


    public function channels_without_scope()
    {
        return $this->belongsToMany(Channel::class)->withoutGlobalScopes();
    }

    public function organization_sender()
    {
        return $this->belongsTo(Organization::class, "organization_id_sender", "id");
    }

    public function organization_sender_without_scope()
    {
        return $this->belongsTo(Organization::class, "organization_id_sender", "id")->withoutGlobalScopes();
    }

    public function user_sender()
    {
        return $this->belongsTo(User::class, "email", "email");
    }
    public function user_sender_without_scope()
    {
        return $this->belongsTo(User::class, "email", "email")->withoutGlobalScopes();
    }

    public function products()
    {
        return $this->belongsToMany(Product::class);
    }

}
