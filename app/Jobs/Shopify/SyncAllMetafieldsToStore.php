<?php

namespace App\Jobs\Shopify;

use App\User;
use Illuminate\Bus\Queueable;
use App\Models\Product\Attribute;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use App\Notifications\ApimioNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use App\Classes\Shopify\CreateMetaFieldDefinition;

class SyncAllMetafieldsToStore implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public array $shopifyData)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        //get the count of metafield definitions currently exists in shopify store
       /* $count = (new CreateMetaFieldDefinition($this->shopifyData['channel_id'],$this->shopifyData['organization_id']))
        ->getmetafieldDefinitionsCount();

        if($count >= 250){
            return false;
        }

        $count = 250 - $count;*/
        //get all attributes which are not default
       $attributes = Attribute::query()
           ->where('organization_id',$this->shopifyData['organization_id'])
           ->with('families')
           ->whereHas('families',function($q){
                           $q->where('is_default',0);
           })
          // ->limit($count)
           ->get();

       //create metafield definitions for each attribute in shopify
       foreach($attributes as $attribute){
          $response =  (new CreateMetaFieldDefinition($this->shopifyData['channel_id'],$this->shopifyData['organization_id']))
               ->setData($attribute)
               ->create();
          if($response){
              break;
          }
       }

       if($attributes->count() > 0){
           // All jobs completed successfully
           $path = route('attributes.index');
           $notificationDetails = [
               'subject' => 'Attribute Sync Queue Processed.',
               'greeting' => 'Hi ',
               'body' => "Attribute syncing to your new Shopify store is complete! Please check the activity log to verify if there were any issues during the attribute (metafield) synchronization" ,
               'thanks' => 'Thank you for using '.request()->getHttpHost(),
               'actionText' => 'View',
               'actionURL' => url($path),
               'user_id' =>$this->shopifyData['user_id'],
               'organization_id' =>$this->shopifyData['organization_id'],
           ];

           $notifier = new ApimioNotification($notificationDetails);

           //un-comment if you want to stop notification in email
           $notifier->only_db_notify(true);
           $user  = User::query()->find($this->shopifyData['user_id']);
           $user->notify($notifier);
       }
    }
}
