<?php

namespace App\Jobs\Shopify;

use App\Classes\Shopify\CreateMetaFieldDefinition;
use App\Notifications\ApimioNotification;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CreateMetafieldDefinationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public array $details)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        foreach($this->details['channel_ids'] as $channel_id){
            (new CreateMetaFieldDefinition($channel_id,$this->details['organization_id']))
                ->setData($this->details['attribute'])
                ->create();
        }

        // All jobs completed successfully
        $path = route('attributes.index');
        $notificationDetails = [
            'subject' => 'Attribute Sync successfully.',
            'greeting' => 'Hi ',
            'body' => "Your attribute is sync to shopify as a metafield." ,
            'thanks' => 'Thank you for using '.request()->getHttpHost(),
            'actionText' => 'View',
            'actionURL' => url($path),
            'user_id' =>$this->details['user_id'],
            'organization_id' =>$this->details['organization_id'],
        ];

        $notifier = new ApimioNotification($notificationDetails);

        //un-comment if you want to stop notification in email
        $notifier->only_db_notify(true);
        $user  = User::query()->find($this->details['user_id']);
        $user->notify($notifier);
    }
}
