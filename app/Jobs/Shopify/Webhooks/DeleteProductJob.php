<?php

namespace App\Jobs\Shopify\Webhooks;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use App\Models\Product\Product;
use App\Models\Product\FileProduct;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use App\Models\Channel\ChannelFileProduct;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\Channel\ChannelProductStatus;

class DeleteProductJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $request;

    /**
     * Create a new job instance.
     */
    public function __construct( $request )
    {
        $this->request = $request;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        usleep(500000);
        if (isset($this->request['id'])) {
            $channel_product =   ChannelProductStatus::query()
                ->with('channel_product')
                ->where('type', 'shopify')
                ->whereJsonContains('response', ['sync_id' => $this->request['id']])
                ->first();

            //update the updated_at date of product
            if ($channel_product && $channel_product->channel_product) {
                $product = Product::find($channel_product->channel_product->product_id);
                if ($product) {
                    $product->updated_at = Carbon::now();
                    $product->save();

                    // to delete the shopify id of files
                    $fileProductIds = FileProduct::query()
                        ->where('product_id', $product->id)
                        ->pluck('id')
                        ->toArray();

                    ChannelFileProduct::query()
                        ->whereIn('file_product_id', $fileProductIds)
                        ->where([
                            'store_connect_type' => 'shopify',
                            'channel_id' => $channel_product->channel_product?->channel_id,
                        ])
                        ->delete();
                }
            } else {
                Log::channel('webhook')->info($this->request['id'] . ' product not found.');
            }
            if ($channel_product) {
                $channel_product->delete();
            }

            Log::channel('webhook')->info($this->request['id'] . ' DELETE PRODUCT WEBHOOK hit successfully');
        }
    }
}
