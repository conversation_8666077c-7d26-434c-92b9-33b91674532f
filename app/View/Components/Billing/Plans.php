<?php

namespace App\View\Components\Billing;

use App\Models\Billing\ShopifySubscription;
use App\Models\Channel\Channel;
use App\Models\Invite\Invite;
use App\Models\Organization\Organization;
use App\Models\Organization\Plan;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\Component;

class Plans extends Component
{
    public $data;
    public $route_name;
    public $channel_id;
    public $user_data;
    public $stripe_id;
    public $plan_type = '';
    public $orgs;
    public $is_invited;
    public $shopify_trial; //checking shopify user trial

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($name, $shopifyChannelsId, $user, $stripe, $shopifyTrial)
    {
        $this->route_name = $name;
        $this->channel_id = $shopifyChannelsId;
        $this->user_data = $user;
        $this->stripe_id = $stripe;
        $this->shopify_trial = $shopifyTrial;

        $org_id = Auth::user()->organization_id;
        $this->orgs = Organization::where('id',$org_id)->first();
        $invite = new Invite();
        $this->is_invited = $invite->isInvited(Auth::user()->email);

        if($this->is_invited && !$this->orgs->is_subscribed()) {
            $this->data['plans'] = Plan::all();
        } else {
            if (isset($user->shopify_shop_id)) {
                $this->data['plans'] = Plan::where('handle','!=','free_plan')->where('handle','!=','plus_plan')->get();
            } else {
                $this->data['plans'] = Plan::where('handle','!=','free_plan')->get();
            }
        }
        foreach ($this->data['plans'] as $plan) {
            $plan->btn_monthly_link = route($this->route_name, ['plans_id' => $plan->id, 'shopify_channel_id' => $this->channel_id, 'plans_type' => 'month']);
            $plan->btn_yearly_link = route($this->route_name, ['plans_id' => $plan->id, 'shopify_channel_id' => $this->channel_id, 'plans_type' => 'year']);
        }
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {

        return view('components.billing.plans')->with([
            'data'=> $this->data,
            'stripe'=> $this->stripe_id,
            'plan_type' => $this->plan_type,
            'plan_ids' => isset(Auth::user()->shopify_shop_id) ? $this->orgs->shopify_downgrade_plan_ids() : $this->orgs->downgradable_plan_ids(),
            'allowed_options' => $this->orgs->allowed_options(), //this function returns false is user add more then allowed options in grow plan
            'org' => $this->orgs,
            'is_invited' => $this->is_invited,
            'shopify_trial' => $this->shopify_trial,
        ]);
    }
}
