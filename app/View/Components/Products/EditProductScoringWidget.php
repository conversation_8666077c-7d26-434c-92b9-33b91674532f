<?php

namespace App\View\Components\Products;

use App\Models\Product\Version;
use Illuminate\View\Component;

class EditProductScoringWidget extends Component
{
    public $data;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($product, $version)
    {
        $this->data = $version->getFieldsStateByVersionId($product->id);

    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.products.edit-product-scoring-widget')->with('data',$this->data);
    }
}
