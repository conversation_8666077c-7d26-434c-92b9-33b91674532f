<?php

namespace App\View\Components\Products;

use Illuminate\View\Component;

class PageTitle extends Component
{
    public $name;
    public $description;
//    public $buttonname;
    public $button;
    public $links;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($name, $description,/* $buttonname,*/ $button, $links)
    {
        $this->name = $name;
        $this->description = $description;
//        $this->buttonname = $buttonname;
        $this->button = $button;
        $this->links = $links;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.products.page-title');
    }
}
