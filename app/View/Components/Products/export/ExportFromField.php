<?php

namespace App\View\Components\Products\export;

use Illuminate\View\Component;

/**
 * @deprecated This class will be removed in the future.
 */
class ExportFromField extends Component
{
    /**
     * The heading attributes and apimio attributes.
     *
     * @var array
     */
    public $heading_attributes,$apimio_attributes;




    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($heading_attributes,$apimio_attributes)
    {
        $this->heading_attributes = $heading_attributes;
        $this->apimio_attributes = $apimio_attributes;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.products.export.export-from-field');
    }
}
