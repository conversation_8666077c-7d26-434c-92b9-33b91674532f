<?php

namespace App\Rules;

use App\Models\Channel\ChannelVersion;
use App\Models\Product\Product;
use App\Models\Product\ProductVersion;
use Illuminate\Contracts\Validation\Rule;

class ShopifyProductSync implements Rule
{
    public $channel_id;
    public $product_name;
    public $version_name;
    public $variant_size;
    public $options_size;
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($channel_id , $product_name = null , $variant_size = null,$version_name = null,$options_size = null)
    {
       $this->channel_id =$channel_id;
       $this->product_name =$product_name;
       $this->version_name =$version_name;
       $this->variant_size =$variant_size;
       $this->options_size =$options_size;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $product_version_id = ProductVersion::where('product_id',$value)->pluck('version_id')->toArray();
        $version_ids = ChannelVersion::where('channel_id',$this->channel_id)->whereIn('version_id',$product_version_id)->pluck('version_id')->toArray();
        //get product
        $products = new Product();

        $product = $products->with(["brands",
            "categories",
            "vendors",
            "channels",
            "variants",
            "files",'versions'=>function($q) use ($version_ids){
            $q->whereIn('version_id',$version_ids);
        }])->findOrFail($value);

        $product = $products->product_fetch($product);

        //set check for product name
        foreach($product->versions as $version)
        {

            $version_product_name = $version->families
                ->where("name", "General")
                ->first()
                ->attributes
                ->where("handle", "product_name")
                ->pluck("value")
                ->first();
            if($version_product_name == null)
            {
                $this->version_name = $version->name;
                return $this->product_name = false;
            }
        }

        $flag = 0;

        foreach($product->variants as $variant )
        {
            $json = json_decode($variant->option , TRUE);
            if(isset($json['attributes']))
            {
                if(count($json['attributes'])>3)
                {
                    $flag = 1;
                    break;
                }
            }
        }

        // check variants size
        if(sizeof($product->variants) > 100)
            return $this->variant_size = false;
        if($flag == 1)
            return $this->options_size = false;

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
      //  dd ($this->variant_size);
        if($this->product_name !== null) {
            return ('Product name is not added in version "'. $this->version_name . '" of product. Kindly add it before sync product to shopify.');
        }
        if($this->variant_size !== null) {
            return ('This product is not compatible with shopify due to more than 100 variant types.');
        }
        if($this->options_size !== null) {
            return ('This product is not compatible with shopify due to more than 3 options.');
        }
    }
}
