<?php

namespace App\Rules;

use App\Models\Organization\OrganizationUser;
use App\Models\Organization\TeamInvite;
use App\User;
use Illuminate\Contracts\Validation\Rule;

class EmailUniqueForOrganization implements Rule
{
   private $data ;
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $email = null;
        $invite = TeamInvite::where(['organization_id'=>$this->data['organization_id'],'email'=>$value]);
        if(isset($this->data['id'])) {
            $invite = $invite->where('id', '!=', $this->data['id']);
            $email = $value;
        }
        $invite = $invite->first();

        if($invite){
            return false;
        }

        $user = User::where('email',$value);
        if($email)
            $user = $user->where('email', '!=', $email );

        $user =$user->first();
        if($user) {
            $org = OrganizationUser::query()
                ->where('user_id', $user->id)
                ->where('organization_id', $this->data['organization_id'])
                ->first();
            if ($org) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Member already exist.';
    }
}
