<?php

namespace App\Listeners\Product;

use App\Models\Product\AttributeFamilyProductVersion;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class ManageAttributesListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        if ($event->version) {
            foreach ($event->data["attribute"] as $key => $value) {
                // check data is coming in array form or not
                if(is_array($value))
                {
                    //delete the previous data
                    AttributeFamilyProductVersion::query()
                        ->where('product_version_id', $event->version->pivotId)
                        ->where('attribute_family_id', $key)
                        ->delete();

                    //check if data has single value
                    if (array_key_exists('value',$value) && array_key_exists('measurement',$value)) {
                        if(is_array($value['value'])){

                            for ($i=0; $i< sizeOf($value['value']); $i++) {
                                $v = $value['value'][$i];
                                $m = $value['measurement'][$i];

                                //save new product value
                                $event->product->saveAttributeVersionValue($event->version, $key, $v, 'no_update',$m );

                            } //end of values list foreach
                        }
                        else{
                            $event->product->saveAttributeVersionValue($event->version, $key, $value['value'], 'no_update',$value['measurement'] );
                        }
                    }
                    else{
                        foreach($value as $val) {
                            if(is_array($val)) {
                                foreach ($val as $v) {
                                    $event->product->saveAttributeVersionValue($event->version, $key, $v, 'no_update');
                                }
                            }
                            else{
                                $event->product->saveAttributeVersionValue($event->version, $key, $val, 'no_update');
                            }
                        }
                    }
                }// end of else condition for list saving
                else{
                    $event->product->saveAttributeVersionValue($event->version, $key, $value,'update_available');
                } // end of saving string or single values, not in form of array

            }
        } else {
            Log::channel('events')->warning('Version cannot be null');
        }

    }
}
