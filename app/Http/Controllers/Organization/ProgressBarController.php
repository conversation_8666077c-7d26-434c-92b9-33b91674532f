<?php

namespace App\Http\Controllers\Organization;

use App\Events\BatchProgressEvent;
use App\Http\Controllers\Controller;
use App\Models\BatchProgress;
use Illuminate\Support\Facades\Bus;



class ProgressBarController extends Controller
{
    public function index($batch_id){

        event(new BatchProgressEvent($batch_id));

        // Retrieve the batch from the job_batches table
        return $batch = Bus::findBatch($batch_id);

        if (!$batch) {
            // Handle the case when the batch is not found
            return;
        }
        $totalJobs = $batch->totalJobs;
        $processedJobs = 0;

    }
    public function listenEvent(){
       // (new BatchProgress())->store(['organization_id'=>1,'batch_id'=>'98d18526-7ef9-4c65-b56b-d40ec2963780','type'=>'shopify_syncing']);
        return view('test_file');
    }

}
