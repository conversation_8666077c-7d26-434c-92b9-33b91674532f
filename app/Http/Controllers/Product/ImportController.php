<?php

namespace App\Http\Controllers\Product;

use App\Services\AntDesignArrayConvertor;
use Carbon\Carbon;
use SplFileObject;
use Inertia\Inertia;
use Illuminate\Bus\Batch;
use App\Traits\AWSSetting;
use Illuminate\Support\Str;
use App\Jobs\ImportProducts;
use Illuminate\Http\Request;
use App\Classes\ImportExport;
use App\Models\BatchProgress;
use App\Models\Product\Family;
use App\Models\Channel\Channel;
use App\Models\Product\Product;
use App\Models\Product\Version;
use App\Models\Product\Attribute;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\MessageBag;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\Product\AttributeType;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Redirect;
use PhpOffice\PhpSpreadsheet\IOFactory;
use App\Jobs\Shopify\FetchAllShopifyData;
use App\Models\Organization\Organization;
use Illuminate\Support\Facades\Validator;
use PhpOffice\PhpSpreadsheet\Reader\IReadFilter;
use Apimio\MappingConnectorPackage\models\Template;
use Apimio\MappingConnectorPackage\Http\Controllers\MappingFieldController;

class ImportController extends Controller
{
    use AWSSetting;

    public function __construct()
    {
        $this->middleware(['auth', 'verified', "activeOrganization"]);
    }


    /**
     * Upload csv step 1 appears
     *
     * @return \Inertia\Response
     */
    public function import_step1()
    {
        return Inertia::render('product/import/CSVImport');
//        return view('products.import.createOne');
    }

    public function processFile($filePath)
    {
        $extension = $filePath->getClientOriginalExtension();
        $limit = 5; // Limit to the first 5 rows
        $importedRows = [];

        if ($extension === 'xlsx') {
            $reader = IOFactory::createReaderForFile($filePath);
            $reader->setReadDataOnly(true);
            $reader->setReadFilter(new class implements IReadFilter {
                private $startRow = 1;
                private $endRow = 5;

                public function readCell($column, $row, $worksheetName = ''): bool
                {
                    if ($row >= $this->startRow && $row <= $this->endRow) {
                        return true;
                    }
                    return false;
                }
            });

            $spreadsheet = $reader->load($filePath);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            $importedRows = array_slice($rows, 0, $limit);
        } elseif ($extension === 'csv') {
            $handle = fopen($filePath, 'r');
            while (($row = fgetcsv($handle)) !== false && $limit > 0) {
                $importedRows[] = $row;
                $limit--;
            }
            fclose($handle);
        } else {
            // Unsupported file type
            return [];
        }

        return $importedRows;
    }


    /**
     * fetch csv file and store temporary in storage and display import csv step 2
     * Also handles GET requests by retrieving data from session
     *
     * @param Request $request
     * @return mixed
     */
    public function import_step2(Request $request)
    {
        if ($request->isMethod('get')) {
            return $this->handleGetRequest();
        }

        if ($request->isMethod('post')) {
            return $this->handlePostRequest($request);
        }

        // If method not GET or POST, optionally return 405 or redirect
        abort(405);
    }

    private function handleGetRequest()
    {
        try {
            $data = session()->get('data');
            if (empty($data)) {
                return redirect()->route('import.csv.step1')
                    ->withErrors(['error' => 'No import data found. Please upload a file first.']);
            }

            return $this->renderTemplatesOrMapping($data);

        } catch (\Exception $e) {
            return redirect()->route('import.csv.step1')
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    private function handlePostRequest(Request $request)
    {
        try {
            $this->validateFile($request);

            $selected_template = $this->getSelectedTemplate($request->template_id);

            $filePath = $request->file('file');
            $firstFiveRows = $this->getFirstFiveRows($request, $filePath);

            $headingAttributes = $firstFiveRows[0] ?? [];

            $versionArray = $this->getVersionArray();
            $catalogArray = $this->getCatalogArray();

            $notFetchableAttributes = Attribute::not_fetch_able_attributes();

            $data = $this->prepareDataArray(
                $headingAttributes,
                $versionArray,
                $catalogArray,
                $notFetchableAttributes,
                $request->import_action ?? 3
            );

            $this->handleTemplateMapping($data, $selected_template);

            Session::put('data', $data);

        } catch (\Exception $e) {
            return back()->withErrors(['main' => [$e->getMessage()]]);
        }

        // Force UTF-8 encoding for safety
        $data = mb_convert_encoding($data, 'UTF-8', 'UTF-8');

        // If template applied flag is set, skip template selection
        if (request()->has('template_applied') && request()->template_applied) {
            return Inertia::render('CSVMapping', ['data' => $data]);
        }

        return $this->renderTemplatesOrMapping($data);
    }

    private function validateFile(Request $request)
    {
        $messages = [
            'file.required' => 'The :attribute field is required.',
            'file.max' => "The :attribute may not be greater than 50 MB's or 51,200 KB's.",
        ];

        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:csv,txt,html,xlsx|max:51200',
        ], $messages, [
            'file' => 'Upload File',
        ]);

        if ($validator->fails()) {
            throw new \Exception($validator->errors()->first());
        }
    }

    private function getSelectedTemplate($templateId)
    {
        if (!$templateId) {
            return null;
        }

        $template = Template::findOrFail($templateId);
        $payload = json_decode($template->payload, true);

        return [
            'id' => $template->id,
            'name' => $template->name,
            'created_at' => $template->created_at,
            'payload' => $payload,
        ];
    }

    private function getFirstFiveRows(Request $request, $filePath)
    {
        $rows = json_decode($request->first_five_rows, true);

        if (empty($rows)) {
            // Your existing processFile method to get first five rows
            $rows = $this->processFile($filePath);
        }

        if (count($rows) > 0) {
            $rows[0] = array_map('trim', $rows[0]);
        }

        return $rows;
    }

    private function getVersionArray()
    {
        return Version::all()->pluck('name', 'id')->toArray();
    }

    private function getCatalogArray()
    {
        return Channel::all()->pluck('name', 'id')->toArray();
    }

    private function prepareDataArray($headingAttributes, $versionArray, $catalogArray, $notFetchableAttributes, $importAction)
    {
        $data = [];

        // Prepare input array for mapping
        $data['input_array'] = [
            'array_name' => "CSV",
            'nodes' => [
                [
                    'name' => 'Default',
                    'attributes' => array_combine($headingAttributes, $headingAttributes),
                ]
            ]
        ];

        $data['converted_input_array'] = (new AntDesignArrayConvertor())->FamilyAttributes($data['input_array']);

        // Save uploaded file to S3 with timestamped name
        $fileExtension = request()->file('file')->getClientOriginalExtension();
        $fileName = time() . '_datafile.' . $fileExtension;

        $path = Storage::disk('s3')->putFileAs('/mapping_fields/upload/json', request()->file('file'), $fileName);
        $data['file_path'] = $path;
        $data['file_url'] = Storage::disk('s3')->url($path);

        $data['data_required'] = [
            'template_method_type' => 'import',
            'output_type' => 'Apimio',
            'sync' => false,
            'redirect_url_route' => 'products.import',
            'organization_id' => Auth::user()->organization_id,
            'versions' => $versionArray,
            'catalogs' => $catalogArray,
            'import_action' => $importAction,
        ];

        $data['import_action'] = $importAction;

        $allFamilies = Family::select('id', 'name')->whereNotIn('name', ['General', 'SEO'])->get()->toArray();
        $allAttributes = AttributeType::select('id', 'name')->whereNotIn('id', array_keys($notFetchableAttributes))->get()->toArray();

        $data['apimio_attributes_required'] = [
            "all_families" => $allFamilies,
            "all_attributes" => $allAttributes,
        ];

        $data['template_attributes'] = [];

        $data['output_array'] = Template::apimio_mapping_array();
        $data['converted_output_array'] = (new AntDesignArrayConvertor())->FamilyAttributes($data['output_array']);

        return $data;
    }

    private function handleTemplateMapping(&$data, $selectedTemplate)
    {
        if ($selectedTemplate) {
            $payload = $selectedTemplate['payload'] ?? [];
            $data['mapping_data'] = $payload['data'] ?? $payload;

            // Add template info to data_required
            $data['data_required']['selected_template'] = $selectedTemplate;
        } else {
            $data['mapping_data'] = $this->generateMapping($data['input_array'], $data['output_array']);
        }
    }

    private function renderTemplatesOrMapping($data)
    {
        $templates = Template::where('organization_id', $data['data_required']['organization_id'])
            ->where('type', $data['data_required']['template_method_type'])
            ->get();

        if ($templates->isNotEmpty()) {
            return Inertia::render('Templates', [
                'templates' => $templates,
                'file_path' => $data['file_path'] ?? null,
                'import_action' => $data['import_action'] ?? 3,
            ]);
        }

        return Inertia::render('CSVMapping', ['data' => $data]);
    }


    /**
     * checking array contains duplicate values
     *
     * @param Request $my_arr
     *
     */
    function get_keys_for_duplicate_values($my_arr, $clean = false)
    {
        if ($clean) {
            return array_unique($my_arr);
        }

        $dups = $new_arr = array();
        if (isset($my_arr)) {
            foreach ($my_arr as $key => $val) {
                if (!isset($new_arr[$val])) {
                    $new_arr[$val] = $key;
                } else {
                    if (isset($dups[$val])) {
                        $dups[$val][] = $key;
                    } else {
                        $dups[$val] = array($key);
                    }
                }
            }
        }

        return $dups;
    }

    /**
     * Handle the create template request, retrieving data from session if page is reloaded
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function create_template(Request $request)
    {
        try {
            // Get the data from session or create new data array
            $data = session()->get('data', []);

            unset($data['data_required']['selected_template']);

            $data['mapping_data'] = $this->generateMapping($data['input_array'], $data['output_array']);

            // Check if we have data in the session
            if (empty($data)) {
                // If no data in session, redirect back to import step 1
                return redirect()->route('import.csv.step1')
                    ->withErrors(['error' => 'No import data found. Please upload a file first.']);
            }

            // Render the CSVMapping component with the data
            return Inertia::render('CSVMapping', [
                'data' => $data
            ]);
        } catch (\Exception $e) {
            // Return an Inertia error response
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }


    /**
     * Apply a template to the current import process
     * Handles GET requests for applying templates
     *
     * @param Request $request
     * @return \Illuminate\Http\Response|\Inertia\Response
     */
    public function apply_template(Request $request)
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'template_id' => 'required|exists:templates,id',
            ]);

            if ($validator->fails()) {
                if ($request->isMethod('get')) {
                    return back()->withErrors($validator);
                } else {
                    return response()->json(['error' => $validator->errors()], 422);
                }
            }

            // Get the template
            $template = Template::findOrFail($request->template_id);

            // Get the data from session or create new data array
            $data = session()->get('data', []);

            // If file_path is provided in the request, update the session data
            if ($request->has('file_path')) {
                $data['file_path'] = $request->file_path;
            }

            // Set import action
            $data['import_action'] = $request->import_action ?? 3;

            // Parse the template payload
            $templatePayload = json_decode($template->payload, true);

            // Update the mapping data with the template data
            if (isset($templatePayload['data'])) {
                $data['mapping_data'] = $templatePayload['data'];
            }

            // Add template information to the data
            $data['data_required']['selected_template'] = [
                'temp_id' => $template->id,
                'temp_name' => $template->name,
                'created_at' => $template->created_at,
                'payload' => $templatePayload,
                'catalog' => $template->channel_id,
                'version' => $template->version_id,
                'type' => $template->type,
                'export_type' => $template->export_type,
                'product_status' => $template->product_status,
            ];

            // Save the updated data to the session
            Session::put('data', $data);

            // Store a flash message for success
            session()->flash('success', 'Template applied successfully');

            // Render the CSVMapping component with the data
            return Inertia::render('CSVMapping', [
                'data' => $data
            ]);

        } catch (\Exception $e) {
            // Return an Inertia error response
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }



    function save_template(Request $request)
    {

        $default_template_name_FLAG = false;

        $default_temp_names = [
            'Shopify Default',
            'Magento Default',
        ];


        $template_obj = new Template();
        $template_obj_count = $template_obj->where('organization_id', $request->organization_id)
            ->where(function ($query) use ($request) {

                if (is_array($request->version)) {
                    foreach ($request->version as $version) {
                        $query->orWhereJsonContains('version_id', $version);
                    }
                } else {
                    $query->whereJsonContains('version_id', $request->version);
                }
            })
            ->where(function ($query) use ($request) {
                if (is_array($request->catalog)) {
                    foreach ($request->catalog as $catalog) {
                        $query->orWhereJsonContains('channel_id', $catalog);
                    }
                } else {
                    $query->whereJsonContains('channel_id', $request->catalog);
                }
            })
            ->where('name', $request->temp_name)
            ->where('type', $request->template_method_type)
            ->where('export_type', $request->export_type);




        if (isset($request->temp_id)) {
            $temp_ids = is_array($request->temp_id) ? $request->temp_id : [$request->temp_id];
            $template_obj_count = $template_obj_count->whereNotIn('id', $temp_ids);
        }

        $template_obj_count = $template_obj_count->get();



        if (in_array($request->temp_name, $default_temp_names) && $request->template_method_type == 'export') {
            $default_template_name_FLAG = true;
        }

        if (count($template_obj_count) > 0 || (isset($request->temp_status) && $request->temp_status == "on" && $default_template_name_FLAG)) {
            return response()->json([
                'status' => 'error',
                'data' => "",
                'message' => "Template name already exist in this organization",
            ], 500);
        }

        if(isset($request->temp_status) && $request->temp_status == "on"){
            $template_obj = $template_obj->set_data($request->all())->store(function ($tem_obj) use ($request) {
                $msg = "";
                if (!isset($request->temp_id)) {
                    $msg = "Template saved successfully.";
                } else {
                    $msg = "Template update successfully.";
                }

                $new_temp_data['selected_template'] = [
                    'temp_id' => $tem_obj->id,
                    'temp_name' => $tem_obj->name,
                    'created_at' => $tem_obj->created_at,
                    'payload' => json_decode($tem_obj->payload, true),
                    'version' => $tem_obj->version_id,
                    'catalog' => $tem_obj->channel_id,
                    'type' => $tem_obj->type,
                    'export_type' => $tem_obj->export_type,
                    'product_status' => $tem_obj->product_status,
                ];


                return response()->json([
                    'status' => 'success',
                    'data' => $new_temp_data,
                    'message' => $msg,
                ], 200);
            }, function ($error) {
                return response()->json([
                    'status' => 'error',
                    'data' => "",
                    'message' => $error,
                ], 500);
            });

            return $template_obj;
        }
    }

    /**
     * fetch csv heading, attributes and display import step 3
     *
     * @param Request $request
     *
     */
    public function import_step3(Request $request)
    {
        try {

            $messages = array(
                'file.required' => 'The :attribute field is required.',
                'file.max' => "The :attribute may not be greater than 50 MB's or 51,200 KB's.",
            );
            $validator = Validator::make($request->all(), [
                'file' => 'required|mimes:csv,txt,html,xlsx|max:51200',
            ], $messages, [
                'file' => 'Upload File',
            ]);

            if ($validator->fails()) {
                return back()->withErrors($validator)->withInput();
            }

            $data = session()->get('data');

            if (isset($data['temp']['attributes_count'])) {

                // file is empty
                if ($data['temp']['attributes_count'] <= 1) {
                    return back()->withErrors(trans('products_import_step2.csv_upload_file_empty'))->withInput();
                }

                // check columns
                if ($data['temp']['attributes_count'] > 300) {
                    return back()->withErrors(['main' => trans('products_import_step2.csv_upload_column_limit')]);
                }
            }

            //record saving temporarily
            $fileExtension = $request->file('file')->getClientOriginalExtension();
            $fileName = time() . '_datafile';

            if ($fileExtension === 'xlsx') {
                $fileName .= '.xlsx';
            } elseif ($fileExtension === 'csv') {
                $fileName .= '.csv';
            }
            $path = Storage::disk('s3')->putFileAs('/mapping_fields/upload/json', $request->file('file'), $fileName);
            $path_temp = Storage::disk('s3')->url($path);
            $data['file_path'] = $path;
            $data['file_url'] = $path_temp;

            $data['import_action'] = $request->import_action ?? 3;

            Session::put('data', $data);


            $mapping = new MappingFieldController();
            return $mapping->mapping_view($data['input_array'], $data['output_array'], $data['data_required']);
        } catch (\Exception $e) {
            return back()->withErrors(['main' => [$e->getMessage()]]);
        }
    }


    /**
     * convert csv data into array and display on import step 3
     *
     * @param Request $request
     * @return JsonResponse|MessageBag|RedirectResponse|void
     */
    public function import_csv(Request $request)
    {
        try {
            $import_export = new ImportExport();
            $validator = Validator::make($request->all(), [
                'nodes' => 'required',
                'organization_id' => 'required',
            ]);

             $data = session()->get('data');

             $file_path = $request->file_path ?? $data['file_path'] ?? null;

            $request->merge(['file_path' => $file_path]);

            if (!Storage::disk('s3')->exists($request->file_path)) {
                $validator->errors()->add('file_path', 'Your product data json file is not exist.');
                return $validator->errors();
            }

            $combinedData = Product::select('products.sku as product_handle')
                ->selectRaw('GROUP_CONCAT(variants.sku) as variant_skus')
                ->leftJoin('variants', 'products.id', '=', 'variants.product_id')
                ->where('products.organization_id', $request->organization_id)
                ->groupBy('products.sku')
                ->get();

            $final_skus = [];

            foreach ($combinedData as $item) {
                $product_handle = $item->product_handle;
                $variant_skus = $item->variant_skus ? explode(',', $item->variant_skus) : [];

                $final_skus[$product_handle] = array_values($variant_skus);
            }


            if (isset($request->temp_status) && $request->temp_status == "on") {
                $template_obj = new MappingFieldController();
                $template_response = $template_obj->save_template($request);
                $response_data = $template_response->getData();
                if ($template_response->getStatusCode() == 200) {
                    if ($response_data->status != 'success') {
                        // Check if this is an Inertia request
                        if ($request->header('X-Inertia')) {
                            return back()->withErrors(['template_error' => $response_data->message]);
                        }
                        return response()->json($response_data, 500);
                    }
                } else {
                    // Check if this is an Inertia request
                    if ($request->header('X-Inertia')) {
                        return back()->withErrors(['template_error' => $response_data->message ?? 'Template save failed']);
                    }
                    return response()->json($response_data, 500);
                }
            }


            $request->query->add(['all_product_skus' => $final_skus]);
            $request->query->add(['request_data' => $data]);


            //            if (isset($request->temp_status) && $request->temp_status == "on") {
//                $template_obj = new Template();
//                $template = $template_obj->set_data($request->all())->store(function ($tem_obj) {
//                    return $tem_obj;
//                }, function ($error) {
//                    return $error;
//                });
//            }

            $filename = "error_products_" . Carbon::now();
            $filename = Str::slug($filename, '_') . ".xlsx";
            $data = [
                'all_data' => $request->all() ?? [],
                'user' => Auth::user(),
                'organization_id' => Auth::user()->organization_id ?? null,
                'filename' => 'temp_files/' . $filename,
                'batch' => null,
                'notification_id' => null,
            ];

            $notification = $import_export->send_notification_for_import(
                $data,
                false,
                "Your CSV file is now being processed by Apimio. We’ll notify you once all product data has been successfully imported."
            );
            $data['notification_id'] = $notification->id ?? null;

            //////////////////////////////////////below code is for without queue testing do not remove this code//////////////////////////////////

            //                        $mapping_field_package = new MappingFieldController();
//                        $converted_arrays = $mapping_field_package->mapping_convert($request);
//
//                        //for incorrect row of sku in import csv
//                        if (!isset($request->save_template) ) {
//                            $import_export->set_data($data)->convert_CSV_to_apimio(
//                             $converted_arrays->put('organization_id' , Auth::user()->organization_id),
//                                function ($errors) {
//                                    Log::error($errors);
//                                },
//                                function ($success) use ($import_export,$data) {
//
//                                    (new ImportExport)->send_notification_for_import($data, true);
//                                    dd($success);
//                                    if ($success == 'template') {
//                                        return false;
//                                    }
//                                }
//                            );
//                        }


            if (!isset($request->save_template)) {

                if (env('CUSTOM_QUEUE_WORKER') == 'local') {
                    $batch_import_csv = Bus::batch([])->then(function (Batch $batch) use ($data) {
                        Log::channel('mapping')->info("job done");
                    })->dispatch();
                    $data['batch'] = $batch_import_csv->id ?? null;

                    // below code is for batch progress monitoring
                    $batch_progress_data = [
                        'user_id' => $data['user']->id,
                        'organization_id' => $data['organization_id'],
                        'batch_id' => $data['batch'],
                        'type' => 'import_csv',
                    ];
                    (new BatchProgress())->store($batch_progress_data);


                    if (env('TESTING_QUEUE')) {
                        ImportProducts::dispatchSync($data);
                    } else {
                        ImportProducts::dispatch($data);
                    }
                } else {
                    $jobs = [];
                    $jobs[] = new ImportProducts($data);
                    $this->dispatchJobsToFifoQueue($jobs, $data['organization_id'] . '-main', $data['organization_id']);
                }
            }
            if (isset($request->save_template)) {
                $msg = 'Template saved successfully.';
            } else {
                $msg = 'Import started successfully. Refresh products page after some time.';
            }
            $responseData = [
                'message' => $msg,
                'data' => $data, // Add your data here
            ];
            // return Inertia::render('ImportStatus', [
            //     'message' => $msg,
            //     'data' => $data,
            // ]);


            session()->flash('success', $msg);
            return Inertia::location('/products');

        } catch (Exception $e) {
            log::error($e->getMessage());
            return back()->withErrors(["main" => $e->getMessage()]);
        }
    }




    /**
     * Generates a mapping between CSV and Apimio attributes.
     *
     * A mapping is created if either the CSV attribute's key or its value matches (case-insensitive)
     * any Apimio attribute key or value. When building the lookup index, if the key and value are the same,
     * it is stored only once.
     *
     * @param array $csvArray    The CSV array (first array) containing nodes and attributes.
     * @param array $apimioArray The Apimio array (second array) containing nodes and attributes.
     *
     * @return array An array of mappings in the format:
     *               [
     *                 'from' => ['Default,SKU'],
     *                 'with_formula' => 'assign',
     *                 'to' => ['Variant,sku'],
     *               ]
     */
    function generateMapping(array $csvArray, array $apimioArray): array {
        $result = [];

        // Build a lookup index for Apimio attributes using both attribute key and value (lowercased).
        // If the key and value are identical, only add it once.
        $apimioLookup = [];
        foreach ($apimioArray['nodes'] as $node) {
            foreach ($node['attributes'] as $key => $value) {
                $lookupKey = strtolower($key);
                $lookupValue = strtolower($value);

                if ($lookupKey === $lookupValue) {
                    $apimioLookup[$lookupKey][] = ['node' => $node['name'], 'key' => $key];
                } else {
                    $apimioLookup[$lookupKey][]   = ['node' => $node['name'], 'key' => $key];
                    $apimioLookup[$lookupValue][] = ['node' => $node['name'], 'key' => $key];
                }
            }
        }

        // Iterate over each CSV node and its attributes.
        foreach ($csvArray['nodes'] as $csvNode) {
            $csvName = $csvNode['name'];
            foreach ($csvNode['attributes'] as $csvKey => $csvValue) {
                // Normalize both key and value.
                $normalizedCsvKey = strtolower($csvKey);
                $normalizedCsvValue = strtolower($csvValue);

                // If the CSV attribute key matches an Apimio attribute, add mapping(s).
                if (isset($apimioLookup[$normalizedCsvKey])) {
                    foreach ($apimioLookup[$normalizedCsvKey] as $match) {
                        $result[] = [
                            'from'         => [$csvName . ',' . $csvKey],
                            'with_formula' => 'assign',
                            'to'           => [$match['node'] . ',' . $match['key']],
                        ];
                    }
                }
                else{
                    $result[] = [
                        'from'         => [$csvName . ',' . $csvKey],
                        'with_formula' => 'assign',
                        'to'           => [],
                    ];
                }

//                // If the CSV attribute value (when different from the key) matches, add mapping(s).
//                if ($normalizedCsvValue !== $normalizedCsvKey && isset($apimioLookup[$normalizedCsvValue])) {
//                    foreach ($apimioLookup[$normalizedCsvValue] as $match) {
//                        $result[] = [
//                            'from'         => [$csvName . ',' . $csvKey],
//                            'with_formula' => 'assign',
//                            'to'           => [$match['node'] . ',' . $match['key']],
//                        ];
//                    }
//                }
            }
        }

        return $result;
    }

    /**
     * Delete a template
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function delete_template(Request $request)
    {
        try {
            // Get template_id from query string or request body
            $templateId = $request->query('template_id') ?? $request->input('template_id');

            if (!$templateId) {
                return back()->withErrors(['error' => 'Template ID is required']);
            }

            // Find and delete the template
            $template = Template::find($templateId);

            if (!$template) {
                return back()->withErrors(['error' => 'Template not found']);
            }
            $templateName = $template->name;
            $template->delete();

            // Get the data from session
            $data = session()->get('data', []);

            // Redirect back to the templates page with success message
            session()->flash('success', "Template '{$templateName}' deleted successfully");

            unset($data['data_required']['selected_template']);

            $data['mapping_data'] = $this->generateMapping($data['input_array'], $data['output_array']);

            Session::put('data', $data);

            // Force a full page reload to get fresh data
            return Inertia::location(route('import.csv.step2'));

        } catch (\Exception $e) {
            // Return an error response
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }
}

