<?php

namespace App\Http\Controllers\Product;

use App\Http\Controllers\Controller;
use App\Models\Product\AttributeFamily;
use App\Models\Product\AttributeFamilyProductVersion;
use App\Models\Product\FamilyProductVersion;
use App\Models\Product\Product;
use Illuminate\Http\Request;

class ProductFamilyController extends Controller
{

    public function family($id)
    {
       return AttributeFamily::with('attributes')->where('id',$id)->get();
    }

    public function save(Request $request)
    {
        return $request->all();
       $save_product_data = new Product();
       return $save_product_data->set_data($request->all())->save_family_data(
           function () {
                return "success";
           },
           // when error
           function ($errors) {
               return $errors;
           });
       // dump($r->attribute_family_id);
//        foreach($r->attribute_family_id as $index=>$opt){
//
//            echo  $opt['id'].'<br>';
//            echo  $opt['value'].'<br>';
//
//        }
    }

    public function delete(Request $request)
    {
       $data = [
           'product_id'=>$request->get('product_id'),
           'version_id'=>$request->get('version_id'),
           'family_id'=>$request->get('family_id'),
       ] ;
      // dd($data);
        $delete_product_data = new Product();
        return $delete_product_data->set_data($data)->delete_family_data(
            function () {
                return "success";
            });

        $family_product_version_ids = FamilyProductVersion::where('product_id',$request->get('product_id'))->where('version_id',$request->get('version_id'))->pluck('id')->toArray();
        $attribute_family_ids = AttributeFamily::where('family_id',$request->get('family_id'))->pluck('id')->toArray();

        $delete = AttributeFamilyProductVersion::whereIn('attribute_family_id',$attribute_family_ids)->whereIn('product_version_id',$family_product_version_ids)->delete();
    }
}
