<?php

namespace App\Http\Controllers\Webhooks;

use App\Traits\AWSSetting;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\Channel\Channel;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Models\Product\Category;
use App\Classes\Shopify\Metafield;
use Illuminate\Support\Facades\DB;
use App\Models\Product\FileProduct;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use App\Models\Channel\ChannelProduct;
use App\Models\Channel\ChannelVariant;
use App\Models\Channel\ShopifyChannel;
use App\Classes\ConvertToApimioProduct;
use App\Classes\Shopify\ConvertPayload;
use App\Models\Channel\CategoryChannel;
use Illuminate\Database\Eloquent\Model;
use App\Models\Organization\Organization;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Channel\ChannelFileProduct;
use App\Classes\Shopify\RetrieveCollection;
use App\Models\Channel\ChannelProductStatus;
use Illuminate\Database\Eloquent\Collection;
use App\Jobs\Shopify\Webhooks\UninstallAppJob;
use App\Jobs\Shopify\Webhooks\CreateProductJob;
use App\Jobs\Shopify\Webhooks\DeleteProductJob;
use App\Jobs\Shopify\Webhooks\UpdateProductJob;
use App\Jobs\Shopify\Webhooks\CreateCollectionJob;
use App\Jobs\Shopify\Webhooks\DeleteCollectionJob;
use App\Jobs\Shopify\Webhooks\UpdateCollectionJob;
use Ghazniali95\ShopifyConnector\App\Classes\Rest\Admin2023_10\Product as ShopifyProduct;


class ShopifyWebhookController extends Controller
{
    use AWSSetting;
    /**
     * @return object|null
     */
    public function getShopifyChannelDetail(): object|null
    {
        $headers = getallheaders();
        return ShopifyChannel::query()
            ->where('shop',$headers['X-Shopify-Shop-Domain'])
            ->first();
    }

    /**
     * @param $channel_id
     * @return Model|Collection|Builder|array|null
     */
    public function getChannelDetails($channel_id): Model|\Illuminate\Database\Eloquent\Collection|Builder|array|null
    {
        return Channel::query()->find($channel_id);
    }



    /**
     * @param $channel_id
     * @return mixed
     */
    public function getOrganizationId($channel_id): mixed
    {
        return $this->getChannelDetails($channel_id)->organization_id;
    }

    /**
     * @param $productId
     * @param $shopify_channel
     * @return mixed
     */
    public function getProduct($productId, $shopify_channel): mixed
    {

        $base_url = (new ShopifyChannel())->set_base_url($shopify_channel->channel_id);
        $response = Http::get($base_url."products/$productId.json");
        if ($response->successful()) {
            return $response->json()['product'];
        }
        else{
            return [];
        }
    }



    /**
     * @param Request $request
     * @return string
     */
    public function create_product(Request $request): string
    {
        Log::channel('webhook')->info('create product webhook start');
        $jobs = [];
        $shopify_channel = $this->getShopifyChannelDetail();

        // Early return if channel details are not found or queue is in progress
        if (!$shopify_channel || $shopify_channel->is_queue_start) {
            $message = !$shopify_channel ? 'CHANNEL NOT FOUND' : 'QUEUE IN PROGRESS';
            Log::channel('webhook')->info($message);
            return "success";
        }

        $channel_id = $shopify_channel->channel_id;
        $product_id = null;

        // Check if create product webhook is enabled for the channel
        $channel = $this->getChannelDetails($channel_id);
        if ($channel && !$channel->is_create_product_webhook_enabled) {
            return "success";
        }
        $organization_id = $this->getOrganizationId($channel_id);

        // Check if the Shopify product is already present
        $channel_products = ChannelProductStatus::query()
            ->where('type', 'shopify')
            ->whereJsonContains('response', ['sync_id' => $request->id])
            ->first();

        if ($channel_products) {
            Log::channel('webhook')->error('SHOPIFY PRODUCT ALREADY PRESENT IN APIMIO');
            return "success";
        }

        if(env('CUSTOM_QUEUE_WORKER') == 'local'){
            dispatch(new CreateProductJob($channel_id, $organization_id, $request->only('id')));
        }else{
            $jobs[] = new CreateProductJob($channel_id, $organization_id, $request);
            $this->dispatchJobsToFifoQueue($jobs, $organization_id.'-webhook', $organization_id);
        }
            return "success";

/*            $product = new ShopifyProduct();
            $product->initializeSession(["channel_id" => $channel_id]);
            $response = $product::find($product->session, $request->id);

            if ($response) {
                // Directly assign the returned value from the store method to $product_id
                $product_id = (new \App\Classes\Shopify\StoreProduct($response, $channel_id, $organization_id))
                    ->store(function ($obj) {
                        Log::channel('webhook')->info('Product OBJ Now');
                        Log::channel('webhook')->error($obj);
                        return $obj->id; // Ensure this is the ID you want
                    });
            }

            // Ensure product_id is used after being potentially set
            if ($product_id !== null) {
                // for meta-fields and collection saving process
                $ids = [
                    'shopify_product_id' => $request->id,
                    'product_id' => $product_id
                ];
                Log::channel('webhook')->info('Categories and Metafields');
                Log::channel('webhook')->error($ids);

                // Proceed with operations that depend on $product_id
                (new Metafield($channel_id, $organization_id))->getMetaFields($ids);
                $product_ids[] = $ids; // Assuming this is declared elsewhere
                (new RetrieveCollection($channel_id, $organization_id))->getCollectProduct($product_ids);
            } else {
                Log::channel('webhook')->error('Failed to create or retrieve product ID.');
            }*/


       // return 'done';
    }

    /**
     * @param Request $request
     * @return void
     */
    public function updateProduct(Request $request)
    {
        Log::channel('webhook')->info('create product webhook start');

        $shopify_channel = $this->getShopifyChannelDetail();

        // Early return if channel details are not found or queue is in progress
        if (!$shopify_channel || $shopify_channel->is_queue_start) {
            $message = !$shopify_channel ? 'CHANNEL NOT FOUND' : 'QUEUE IN PROGRESS';
            Log::channel('webhook')->info($message);
            return "success";
        }

        $channel_id = $shopify_channel->channel_id;
        $product_id = null;

        if($channel_id)
        {
            $channel = $this->getChannelDetails($channel_id);
            if($channel && !$channel->is_product_update_webhook_enabled){
                return "success";
            }
            $organization_id = $this->getOrganizationId($channel_id);
            $channel_products = ChannelProductStatus::query()
                ->where('type','shopify')
                ->whereJsonContains('response',['sync_id'=>$request->id] )
                ->first();
            if($channel_products){

                $product_id = ChannelProduct::query()->find($channel_products->channel_product_id)->product_id;
                $product = Product::query()->find($product_id);
                dispatch(new UpdateProductJob($channel_id, $organization_id, $request->only('id')));
                return "success";

        //         $product = new ShopifyProduct();
        //         $product->initializeSession(["channel_id" => $channel_id]);
        //         $response = $product::find($product->session, $request->id);

        //         if ($response) {
        //             foreach ($response->images as $key => $image) {
        //                       $productChannel = ChannelFileProduct::where([
        //                         'channel_id' => $channel_id,
        //                          'store_connect_id' => $image->id ,
        //                           'store_connect_type' => 'shopify'
        //                         ])->first();
        //                         if($productChannel){
        //                             $productChannel->delete();
        //                         }
        //                         $VariantChannel = ChannelVariant::where([
        //                             'channel_id' => $channel_id,
        //                              'store_connect_image_id' => $image->id,
        //                               'store_connect_type' => 'shopify'
        //                             ])->first();
        //                             if(isset($VariantChannel)){
        //                                 $VariantChannel->store_connect_image_id = null;
        //                                 $VariantChannel->save();
        //                                 $variant = Variant::find($VariantChannel->variant_id);
        //                                 if($variant){
        //                                     $variant->file_id = null;
        //                                     $variant->save();
        //                                 }
        //                             }
        //             }
        //             // Directly assign the returned value from the store method to $product_id
        //             $product_id = (new \App\Classes\Shopify\StoreProduct($response, $channel_id, $organization_id))
        //                 ->store(function ($obj) {
        //                     return $obj->id; // Ensure this is the ID you want
        //                 });
        //         }

        //  // Ensure product_id is used after being potentially set
        //     if ($product_id !== null) {
        //         // for meta-fields and collection saving process
        //         $ids = [
        //             'shopify_product_id' => $request->id,
        //             'product_id' => $product_id
        //         ];

        //         // Proceed with operations that depend on $product_id
        //         (new Metafield($channel_id, $organization_id))->getMetaFields($ids);
        //         $product_ids[] = $ids; // Assuming this is declared elsewhere
        //         (new RetrieveCollection($channel_id, $organization_id))->getCollectProduct($product_ids);
        //     } else {
        //         Log::channel('webhook')->error('Failed to create or retrieve product ID.');
        //     }

            }else{
                Log::channel('webhook')->info('product_update create new product');
                $this->create_product($request);
            }
        }
        return "success";
    }

    public function uninstall_app(Request $request){

        $headers = getallheaders();
        Log::info('uninsall app webhook : '.$headers['X-Shopify-Shop-Domain']);
        usleep(500000);
        DB::beginTransaction();
        try {
            $channel = new ShopifyChannel();
            $channel = $channel->set_data(['shop_name' => $headers['X-Shopify-Shop-Domain']])
                ->disconnect(function ($error) {
                    Log::channel('webhook')->debug($error);
                }, function () {
                    Log::channel('webhook')->info("account delete successful");
                });
            DB::commit();
            // return $channel;
        } catch (\Exception $e) {
            DB::rollback();
            Log::channel('webhook')->debug($e->getMessage());
        }
        // dispatch(new UninstallAppJob($headers));
        return "success";
        // DB::beginTransaction();
        // try{
        //     $channel = new ShopifyChannel();
        //     $channel = $channel->set_data(['shop_name'=>$headers['X-Shopify-Shop-Domain']])
        //         ->disconnect(function($error){
        //             Log::channel('webhook')->debug($error);
        //         },function(){
        //             Log::channel('webhook')->info("account delete successful");
        //         });
        //     DB::commit();
        //     return $channel;
        // }catch (\Exception $e) {
        //     DB::rollback();
        //     Log::channel('webhook')->debug($e->getMessage());
        // }
    }

    public function delete_product(Request $request){

        Log::channel('webhook')->info('DELETE PRODUCT WEBHOOK');
        dispatch(new DeleteProductJob($request->only('id')));
        return "success";
        // if($request->has('id')){
        //     $channel_product =   ChannelProductStatus::query()
        //         ->with('channel_product')
        //         ->where('type','shopify')
        //         ->whereJsonContains('response',['sync_id'=>$request->id])
        //         ->first();

        //     //update the updated_at date of product
        //     if($channel_product && $channel_product->channel_product){
        //         $product = Product::find($channel_product->channel_product->product_id);
        //         if($product){
        //             $product->updated_at = Carbon::now();
        //             $product->save();

        //             // to delete the shopify id of files
        //             $fileProductIds = FileProduct::query()
        //                 ->where('product_id',$product->id)
        //                 ->pluck('id')
        //                 ->toArray();

        //             ChannelFileProduct::query()
        //                 ->whereIn('file_product_id',$fileProductIds)
        //                 ->where([
        //                     'store_connect_type'=>'shopify',
        //                     'channel_id'=>$channel_product->channel_product?->channel_id,
        //                 ])
        //                 ->delete();
        //         }
        //     }
        //     else{
        //         Log::channel('webhook')->info($request->id . ' product not found.');
        //     }
        //     if($channel_product){
        //         $channel_product->delete();
        //     }

        //     Log::channel('webhook')->info($request->id .' DELETE PRODUCT WEBHOOK hit successfully');
        // }
    }

    public function createCollection(Request $request){

        $channel_id = $this->getShopifyChannelDetail()->channel_id;
        $organization_id = $this->getOrganizationId($channel_id);
        $data=$request->all();
        $data = (array)$data;
        dispatch(new CreateCollectionJob($channel_id, $organization_id, $request->only('id','title','body_html')));
        return "success";


        // $array['name'] = $data['title'];
        // $array['description'] = $data['body_html'];
        // $array['organization_id'] = $organization_id;
        // //$collection_id = ['type'=>'shopify','collection_id'=>$data['id']];
        // $array['response'] = ['type'=>'shopify','collection_id'=>$data['id'],'channel_id'=>$channel_id];
        // //$array['response'] = json_encode($collection_id);

        // $category = new Category();
        // $category->set_data($array)->store(function ($error) {
        //     Log::channel('shopify')->info('error in saving category in db.');
        // }, function () {
        //     Log::channel('shopify')->info('success in saving cat.');
        // });
    }



    public function updateCollection(Request $request){
        Log::channel('webhook')->info('collection_update');

        $shopify_channel = $this->getShopifyChannelDetail();
        if(!$shopify_channel){
            Log::channel('webhook')->info('CHANNEL NOT FOUND');
            return "success";
        }
        if($shopify_channel->is_queue_start){
            Log::channel('webhook')->info('QUEUE IN PROGRESS');
            return "success";
        }

        $channel_id = $shopify_channel->channel_id;

        $organization_id = $this->getOrganizationId($channel_id);
        $data=$request->all();
        $data = (array)$data;
        dispatch(new UpdateCollectionJob($channel_id, $organization_id, $request->only('id','title','body_html')));
        return "success";
        // $category = Category::query()
        //     ->where('organization_id',$organization_id)
        //     ->whereHas('channel',function($q) use ($channel_id,$data){
        //         $q->where('store_connect_type','shopify')
        //           ->where('store_connect_id',$data['id'])
        //           ->where('channel_id',$channel_id);
        //     })
        //     ->first();

        // if($category) {
        //     $array['id'] = $category->id;
        // }
        // $array['name'] = $data['title'];
        // $array['description'] = $data['body_html'];
        // $array['organization_id'] = $organization_id;
        // $array['category_id'] = $category->category_id??null;
        // /*$collection_id = ['type'=>'shopify','collection_id'=>$data['id']];
        // $array['response'] = json_encode($collection_id);*/
        // $array['response'] = ['type'=>'shopify','collection_id'=>$data['id'],'channel_id'=>$channel_id];

        // $category = new Category();
        // $category->set_data($array)->store(function ($error) {
        //     Log::channel('shopify')->info('error in saving category in db.');
        // }, function () {
        //     Log::channel('shopify')->info('success in saving cat.');
        // });

    }

    public function deleteCollection(Request $request){
        Log::channel('webhook')->info('collection_update');

        $shopify_channel = $this->getShopifyChannelDetail();
        if(!$shopify_channel){
            Log::channel('webhook')->info('CHANNEL NOT FOUND');
            return "success";
        }

        $channel_id = $shopify_channel->channel_id;

        $organization_id = $this->getOrganizationId($channel_id);
        $data=$request->all();
        $data = (array)$data;
        dispatch(new DeleteCollectionJob($channel_id, $organization_id, $request->only('id')));
        return "success";
        // $category = Category::query()
        //     ->where('organization_id',$organization_id)
        //     ->whereHas('channel',function($q) use ($channel_id,$data){
        //         $q->where('store_connect_type','shopify')
        //             ->where('store_connect_id',$data['id'])
        //             ->where('channel_id',$channel_id);
        //     })
        //     ->first();
        // CategoryChannel::query()->where([
        //     'channel_id'=>$channel_id,
        //     'category_id'=>$category->id,
        //     'store_connect_type'=>'shopify',
        //     'store_connect_id'=>$data['id']
        // ])->delete();



    }
}
