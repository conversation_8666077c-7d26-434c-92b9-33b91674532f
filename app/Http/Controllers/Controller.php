<?php

namespace App\Http\Controllers;

use App\Models\Organization\Organization;
use App\Traits\Api\ApiResponseTrait;
use App\User;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests, ApiResponseTrait;


    /**
     * @param $user
     * @param $organization_id
     *
     * @throws \Exception
     */
    public function check_user_organization($user, $request = null)
    {
        if (!isset($user->organization_id) || ($request->has('organization_id') && isset($user->organization_id) && $user->organization_id != $request->get('organization_id')) ) {
            $organization = $user->organizations()->find($request->get('organization_id'));
            if ($organization){
                session(
                    [
                        'organization_id' => $request->get('organization_id') ?? null,
                    ]
                );
            }
            else{
                throw new \Exception("Organization not found", 404);
            }
            session(
                [
                    // 'organization_id' => $request->get('organization_id') ?? null,
                    'organization_id' => $organization->id,
                ]
            );
        } 
        elseif (get_class($user) == User::class) {
            session(
                [
                    'organization_id' => $user->organization_id,
                ]
            );
        }
        else {
            throw new \Exception("Organization not found", 404);
        }
        

        return $request;
    }
}
