<?php

namespace App\Http\Controllers;


use App\Jobs\TestLambdaBatchTestJob;
use App\Traits\AWSSetting;
use Bus;
use Illuminate\Bus\Batch;
use Illuminate\Http\Request;
use Log;
use Throwable;

class TestLambdaFunctionsController extends Controller
{
    use AWSSetting;
    public function __construct()
    {
        $this->initializeTrait();
    }
    public function testLamabdaBatching(){


        $queue_key =mt_rand(0, 999).auth()->user()->organization_id;
        $queue_name = $queue_key;
        $sqs_url =  $this->createSQS($queue_key);

        //sleep(60);
        TestLambdaBatchTestJob::dispatch([
//            'organization_id'=>5,
//            'channel_id'=>5,
//            'user_id'=>1,
            'jobs' =>[
                new \App\Jobs\TestJob(1),
                new \App\Jobs\TestJob(2),
                new \App\Jobs\TestJob(3),
                new \App\Jobs\TestJob(4),
                new \App\Jobs\TestJob(5),
                new \App\Jobs\TestJob(6),
                new \App\Jobs\TestJob(7),
                new \App\Jobs\TestJob(8),
            ],
            'queue_name'=>$sqs_url,
            'batch_name'=>'testBatch'
        ])->onConnection('sqs')->onQueue($sqs_url);

    }

    public function testLamabdaBatching2(){
        $sqs_url = '111';
        TestLambdaBatchTestJob::dispatch([
//            'organization_id'=>5,
//            'channel_id'=>5,
//            'user_id'=>1,
            'jobs' =>[
                new \App\Jobs\TestJob(9),
                new \App\Jobs\TestJob(10),
                new \App\Jobs\TestJob(11),
                new \App\Jobs\TestJob(12),
                new \App\Jobs\TestJob(13),
                new \App\Jobs\TestJob(14),
                new \App\Jobs\TestJob(15),
                new \App\Jobs\TestJob(16),
            ],
            'queue_name'=>$sqs_url,
            'batch_name'=>'testBatch'
        ])->onConnection('sqs')->onQueue($sqs_url);
    }

    public function createBatch()
    {
        $sqs_url = '111';
        dispatch(
            (new \App\Jobs\TestJob(11))
                ->onConnection('sqs-fifo')
                ->onQueue('UserQueue-1231.fifo')
                ->onMessageGroup('test')
                ->withDeduplicator('unique')
        );
    }

    public function testLamabdaBatching3()
    {
        //create fifo sqs
        //   return $this->createFifoSQS(22);

        try {
            $jobs = [
                new \App\Jobs\TestJob(1),
                new \App\Jobs\TestJob(2),
                new \App\Jobs\TestJob(3),
                new \App\Jobs\TestJob(4),
                new \App\Jobs\TestJob(5),
            ];

            $batch = Bus::batch($jobs)->then(function (Batch $batch) {
                Log::info('Batch completed successfully.');
            })->catch(function (Batch $batch, Throwable $e) {
                Log::error(['Batch failed with error: ' => $e->getMessage()]);
            })->finally(function (Batch $batch) {
                Log::info('Batch finally executed.');
            })->dispatch();

            // Send jobs to the FIFO queue
            $this->dispatchJobsToFifoQueue($batch,$jobs);

        } catch (Throwable $e) {
            Log::error(['Error creating batch: ' => $e->getMessage()]);
        }
    }



}
