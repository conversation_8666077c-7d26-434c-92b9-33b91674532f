<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AttributeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'value' => $this->id,
            'organization_id' => $this->organization_id,
            'name' => $this->name,
            'title' => $this->name,
            'handle' => $this->handle,
            'attribute_type' => new AttributeTypeResource($this->attribute_type),
            'attribute_families' => FamilyResource::collection($this->families),
            'is_required' => $this->is_required,
            'is_default' => $this->is_default,
            'response' => $this->response,
            'created_at' => $this->created_at->toDateTimeString(),
            'updated_at' => $this->updated_at->toDateTimeString(),
        ];
    }
}
