<?php

namespace App\Http\Requests\Category;

use Illuminate\Foundation\Http\FormRequest;

class CategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function prepareForValidation()
    {
          // Convert response to a JSON string if it's an array
          if (is_array($this->response)) {
            $this->merge([
                'response' => json_encode($this->response), // Encode the array as a JSON string
            ]);
        }

    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'organization_id' => 'prohibited',
            'name' => 'required|unique:categories,name,' . $this->route('category') . ',id,organization_id,' . auth()->user()->id,
            'description' => 'string|max:255',
            'is_default' => 'boolean',
            'status' => 'nullable',
            'category_id' => 'integer|exists:categories,id',
            'response' => 'nullable|string',

        ];
    }
}
