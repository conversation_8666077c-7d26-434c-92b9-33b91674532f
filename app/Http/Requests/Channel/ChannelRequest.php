<?php

namespace App\Http\Requests\Channel;

use Illuminate\Foundation\Http\FormRequest;

class ChannelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
         return [
            'organization_id' => 'prohibited',
            'name' => 'required|unique:channels,name,' . $this->route('channel') . ',id,organization_id,' . auth()->user()->id,
            'type' => 'required|in:shopify,magento,woocommerce',
            'product_update' => 'nullable',
            'inventory' => 'nullable',
            'export_status' => 'nullable',
            'category_empty' => 'nullable',
            'syncing_method' => 'nullable',
            'is_create_product_webhook_enabled' => 'nullable',
            'is_product_update_webhook_enabled' => 'nullable',
            'is_product_delete' => 'nullable',
            'versions' => 'required|array',
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'The name field is required.',
            'type.required' => 'The type field is required.',
            'type.in' => 'The type field must be one of the following types: shopify, magento, woocommerce.',
            'versions.required' => 'The versions field is required.',
        ];
    }
}
