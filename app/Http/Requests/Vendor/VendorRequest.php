<?php

namespace App\Http\Requests\Vendor;

use Illuminate\Foundation\Http\FormRequest;

class VendorRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'organization_id' => 'prohibited',
            'fname' => 'required|string|max:255|unique:invites,fname,' . $this->route('vendor') . ',id,organization_id_sender,' .auth()->user()->id,
            'lname' => 'nullable|string|max:255',
            'email' => 'nullable|email',
            'phone' => 'nullable|string|max:15',
            'is_accepted' => 'nullable|boolean',
            'is_declined' => 'nullable|boolean',
            'designation' => 'nullable|string|max:255',
            'type' => 'nullable|in:vendor,retailer',
            'channel_ids' => 'nullable|array',
            'channel_ids.*' => 'exists:channels,id', // Validate each channel ID exists in the channels table

        ];
    }

        /**
     * Prepare the data for validation.
     *
     * This method is called before validation runs.
     *
     * We add the organization_id_sender to the request data.
     */
    protected function prepareForValidation()
    {
        // Add organization_id_sender to the request data
        $this->merge([
            'organization_id_sender' => $this->user()->id, // Assuming the authenticated user is the sender
        ]);
    }

    public function messages()
    {
        return [
            'email.email' => 'Please provide a valid email address.',
            'fname.required' => 'The first name is required.',
            'fname.string' => 'The first name must be a string.',
            'fname.max' => 'The first name may not be longer than 255 characters.',
            'fname.unique' => 'This name is already in use.',
            'lname.string' => 'The last name must be a string.',
            'lname.max' => 'The last name may not be longer than 255 characters.',
            'designation.string' => 'The designation must be a string.',
            'designation.max' => 'The designation may not be longer than 255 characters.',
            'phone.string' => 'The phone number must be a string.',
            'phone.max' => 'The phone number may not be longer than 15 characters.',
            'token.string' => 'The token must be a string.',
            'token.max' => 'The token may not be longer than 255 characters.',
            'is_accepted.boolean' => 'The accepted status must be either true or false.',
            'type.string' => 'The type must be a string.',
            'type.in' => 'The type must be one of the following values: vendor, admin, customer.',
            'channel_ids.array' => 'The channel IDs must be an array.',
            'channel_ids.*.exists' => 'Each channel ID must exist in the channels table.',
        ];
    }
}
