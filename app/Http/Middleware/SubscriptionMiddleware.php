<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\Invite\Invite;
use App\Classes\Plan\PlanClass;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use App\Models\Organization\Organization;
use Symfony\Component\HttpFoundation\Response;

class SubscriptionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, $module = null): Response
    {
        if (!Auth::check()) {
            return route('login');
        }

        $user = Auth::user();
        if($user->organization_id){
            $org = Organization::findOrFail($user->organization_id);

            if ($org->onTrial()) {
            } else {
                if (!$org->is_subscribed()) {
                    return redirect(route('billing'));
                }
            }
        }
        return $next($request);

    }
}
