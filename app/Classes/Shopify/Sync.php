<?php

namespace App\Classes\Shopify;

use App\Models\Product\FileProduct;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\Channel\ChannelVariant;
use App\Models\Channel\CategoryChannel;
use App\Models\Channel\ChannelFileProduct;

class Sync
{
    public array $data;
    public array $mutationParts = [];
    public array $aliasMapping = [];

    public function __construct(array $data = [])
    {
        $this->data = $data;
    }

    /**
     * Build collection creation mutations and store alias mappings.
     */
    public function createCollections(): void
    {
        if (!isset($this->data['collections']) && count($this->data['collections']) != 0) {
            return;
        }

        foreach ($this->data['collections'] as $index => $collection) {
            // Capture the internal category id and remove it from the input array
            $catId = $collection['id'];
            unset($collection['id']);

            // Create a unique alias for each mutation call
            $alias = 'collection' . $index;
            $this->aliasMapping[$alias] = $catId;

            // Build the input string for the GraphQL mutation.
            $inputParts = [];
            foreach ($collection as $key => $value) {
                // Escape quotes in the value
                $inputParts[] = $key . ': "' . addslashes($value) . '"';
            }
            $inputString = implode(', ', $inputParts);

            // Build the mutation part for collectionCreate
            $this->mutationParts[] = <<<GRAPHQL
{$alias}: collectionCreate(input: { {$inputString} }) {
    collection {
      id
    }
    userErrors {
      message
      field
    }
}
GRAPHQL;
        }
    }

    /**
     * Build a single media creation mutation if media data and a product id are provided.
     */
    public function createMedia(): void
    {
        if (!isset($this->data['media']) || !is_array($this->data['media']) || count($this->data['media']) === 0) {
            return;
        }

        if (!isset($this->data['shopify_product_id'])) {
            return;
        }

        $mediaInputs = [];
        foreach ($this->data['media'] as $media) {
            $mediaParts = [];
            foreach ($media as $key => $value) {
                if ($key === 'mediaContentType') {
                    // Do not wrap enum values in quotes.
                    $mediaParts[] = $key . ': ' . addslashes($value);
                } else {
                    $mediaParts[] = $key . ': "' . addslashes($value) . '"';
                }
            }
            $mediaInputs[] = '{ ' . implode(', ', $mediaParts) . ' }';
        }
        $mediaInputString = '[' . implode(', ', $mediaInputs) . ']';

        // Build the media mutation as a single field (no alias needed)
        $this->mutationParts[] = <<<GRAPHQL
productCreateMedia(media: {$mediaInputString}, productId: "{$this->data['shopify_product_id']}") {
    media {
      alt
      id
      status
    }
    mediaUserErrors {
      field
      message
    }
}
GRAPHQL;
    }

    /**
     * Placeholder for future option mutations.
     */
    public function createOptions(): void
    {
        // Future implementation for options mutations.
    }

    /**
     * Combine all mutation parts into one GraphQL mutation string.
     *
     * @return string|null Returns null if no mutations were generated.
     */
    public function setMutations(): ?string
    {
        // Build each type of mutation if provided
        $this->createCollections();
        $this->createMedia();
        $this->createOptions();

        // If no mutation parts were added, return null.
        if (count($this->mutationParts) === 0) {
            return null;
        }

        return 'mutation { ' . implode(' ', $this->mutationParts) . ' }';
    }

    /**
     * Call the Shopify API with the built mutations if any exist.
     */
    public function sync(): void
    {
        // try {
        $mutation = $this->setMutations();

        if ($mutation === null) {
            Log::channel('shopify')->info('No mutations generated. Skipping API call.');
            return;
        }

        $response = Http::retry(1, 1000)
            ->withHeaders([
                'Content-Type' => 'application/json',
                'Accept'       => 'application/json',
            ])
            ->timeout(180)
            ->post($this->data['shopify_base_url'] . "graphql.json", [
                'query' => $mutation,
            ]);

        if ($response->successful()) {
            $data = $response->json();
            // Debug: dump data or log as needed.
            // dd($data);

            // Process collection creation responses using aliasMapping.
            foreach ($this->aliasMapping as $alias => $catId) {
                if (isset($data['data'][$alias])) {
                    $result = $data['data'][$alias];

                    // Log any errors for this collection mutation.
                    if (!empty($result['userErrors'])) {
                        Log::channel('shopify')
                            ->error("Error creating collection for internal id {$catId}: " . json_encode($result['userErrors']));
                        continue;
                    }

                    // Retrieve and extract the numeric portion of Shopify's collection id.
                    $shopifyId = $result['collection']['id'] ?? null;
                    if ($shopifyId) {
                        $parts = explode('/', $shopifyId);
                        $shopifyNumericId = end($parts);

                        // Update or create the CategoryChannel record.
                        $cat = CategoryChannel::query()
                            ->where('category_id', $catId)
                            ->where('channel_id', $this->data['channel_id'])
                            ->where('store_connect_type', 'shopify')
                            ->first();

                        if (!$cat) {
                            $cat = new CategoryChannel();
                        }
                        $cat->category_id = $catId;
                        $cat->channel_id = $this->data['channel_id'];
                        $cat->store_connect_type = 'shopify';
                        $cat->store_connect_id = $shopifyNumericId;
                        $cat->save();
                    } else {
                        Log::channel('shopify')->error("Shopify did not return an id for internal id {$catId}.");
                    }
                } else {
                    Log::channel('shopify')->error("No response found for alias {$alias} (internal id {$catId}).");
                }
            }

            // Process product media creation response if present.
            if (isset($data['data']['productCreateMedia'])) {
                $mediaResult = $data['data']['productCreateMedia'];
                if (!empty($mediaResult['mediaUserErrors'])) {
                    Log::channel('shopify')
                        ->error("Error creating product media: " . json_encode($mediaResult['mediaUserErrors']));
                } else {

                    foreach ($mediaResult['media'] as $media) {

                        $parts = explode('-', $media['alt']);

                        $type = $parts[0] ?? null;
                        $image_id = $parts[1] ?? null;
                        $variant_id = $parts[2] ?? null;

                        $shopifyId = $media['id'] ?? null;
                        if ($shopifyId) {

                            $parts = explode('/', $shopifyId);
                            $shopify_id = end($parts);

                            // Example: update a ChannelFileProduct record.
                            // Replace $shopify_id and $image_id with your proper variables.
                            if ($type == "product") {
                                $file_product = FileProduct::query()
                                    ->where('file_id', $image_id)
                                    ->where('product_id', $this->data['product_id'])
                                    ->first();
                                if ($file_product) {
                                    $channel_file_product = ChannelFileProduct::query()
                                        ->where([
                                            'channel_id'         => $this->data['channel_id'],
                                            'store_connect_type' => 'shopify',
                                            'file_product_id'    => $file_product->id
                                        ])
                                        ->first();
                                    if (!$channel_file_product) {
                                        $channel_file_product = new ChannelFileProduct();
                                    }
                                    $channel_file_product->channel_id = $this->data['channel_id'];
                                    $channel_file_product->file_product_id = $file_product->id;
                                    $channel_file_product->store_connect_type = 'shopify';
                                    $channel_file_product->store_connect_id = $shopify_id;
                                    $channel_file_product->save();
                                }
                            }
                            if ($type == "variant") {
                                // save new image id in the variant table
                                $channel_variant = ChannelVariant::where([
                                    'channel_id' => $this->data['channel_id'],
                                    'variant_id' => $variant_id
                                ])->first();
                                if (!$channel_variant) {
                                    $channel_variant = new ChannelVariant();
                                    $channel_variant->variant_id = $shopify_id;
                                }
                                $channel_variant->store_connect_image_id = $shopify_id;
                                $channel_variant->save();
                            }
                            Log::channel('shopify')
                                ->info("Product media created successfully for product id {$this->data['product_id']}");
                        }
                    }
                }
            }
        } else {
            $errorData = $response->json();
            Log::channel('shopify')->error('Bulk sync error: ' . json_encode($errorData));
        }
        // } catch (\Exception $exception) {
        //     Log::channel('shopify')
        //         ->error('Syncing to Shopify encountered an error: ' . $exception->getMessage());
        // }
    }
}
