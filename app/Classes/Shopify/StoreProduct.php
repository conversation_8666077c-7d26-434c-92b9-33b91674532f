<?php

namespace App\Classes\Shopify;

use DB;
use Exception;
use Throwable;
use App\Models\Product\Product;
use App\Classes\Product\Attribute;
use App\Traits\Shopify\ShopifyAPI;
use App\Events\Product\ManageFiles;
use Illuminate\Support\Facades\Log;
use App\Exceptions\VariantException;
use Illuminate\Support\Facades\Http;
use App\Events\Product\ManageInvites;
use App\Events\Product\ManageVersion;
use App\Models\Notification\ErrorLog;
use App\Events\Product\ManageCategory;
use App\Events\Product\ManageChannels;
use App\Events\Product\ManageVariants;
use App\Models\Channel\ChannelProduct;
use App\Models\Channel\ChannelVersion;
use App\Models\Channel\ShopifyChannel;
use App\Events\ChannelUpdateStatusEvent;
use App\Providers\ProductServiceProvider;
use Apimio\Completeness\Events\CalculateScore;
use App\Events\Product\ManageFamilyAttributes;

class StoreProduct
{
    use ShopifyAPI;
    private Product $productObj;

    public function __construct(public object $product , public int $channel_id , public int $organization_id , public mixed $version_id = null)
    {
        $this->productObj = new Product();
        if(!$this->version_id){
            $this->getVersionFromChannel();
        }
        //set base url
        $shopify_channel = ShopifyChannel::query()->where('channel_id', $channel_id)->first();
        $this->setupCredentialsManually($shopify_channel->access_token, $shopify_channel->shop)->createBaseUrl();
        // 2) Gather all inventory_item_ids from these products/variants
        $inventoryIds = [];
            if (!empty($this->product->variants)) {
                foreach ($this->product->variants as $v) {
                    if (!empty($v->inventory_item_id)) {
                        $inventoryIds[] = $v->inventory_item_id;
                    }
                }
            }

        // 3) Fetch cost in chunks of up to 250 inventory_item_ids
        $inventoryIdsChunks = array_chunk($inventoryIds, 250);

        $costMap = []; // key: inventory_item_id => cost
        foreach ($inventoryIdsChunks as $chunkIds) {
            $idsParam = implode(',', $chunkIds);

            $response = Http::retry(1, 1000)->get($this->base_url . "inventory_items.json?ids={$idsParam}");

            $data = $response->json();
            if (!empty($data['inventory_items'])) {
                foreach ($data['inventory_items'] as $invItem) {
                    // Make sure your plan + scopes allow 'cost'
                    if (isset($invItem['id'], $invItem['cost'])) {
                        $costMap[$invItem['id']] = $invItem['cost'];
                    }
                }
            }
        }

        // 4) Assign the cost back to each variant's cost_price field
            if (!empty($this->product->variants)) {
                foreach ($this->product->variants as $v) {
                    $iid = $v->inventory_item_id ?? null;
                    if ($iid && isset($costMap[$iid])) {
                        // Set variant->cost_price so StoreProduct can pick it up
                        $v->cost_price = $costMap[$iid];
                    }
                }
            }
    }

    /**
     * @throws Throwable
     */
    public function store(callable $success)
    {
        try {
            $product_update_flag = false ;
            $productObj = $this->productObj::query()
                ->where('sku', $this->product->handle)
                ->where('organization_id', $this->organization_id)
                ->first();
            if (!$productObj) {
                $product_update_flag = true;
                $productObj = $this->productObj->Create([
                    'sku' => $this->product->handle,
                    'organization_id' => $this->organization_id,
                    'status' => ($this->product->status == 'active') ? 1 : 0,
                ]);
            } else {
                $data = [
                    'organization_id' => $this->organization_id,
                    'description' => "Product already exist in our system <b> handle : {$this->productObj->handle} </b>. We are updating the existing product.",
                    'type' => 'shopify',
                    'link' => route('products.edit', $productObj->id),
                    'link_text' => 'View Product',
                    'status' => 'info',
                ];

               (new ErrorLog())->saveErrorLog($data);
                Log::channel('shopify')->info('Product already exist in our system', [$this->product->handle]);
            }
            $eventsArray[] = new ManageFiles($productObj, $this->getImages(), $this->channel_id, true);
            $eventsArray[] = new ManageInvites($productObj, [['name'=> $this->product->vendor]], $this->channel_id, true);
            $eventsArray[] = new ManageVersion($productObj, [['id' => $this->version_id]]);
            $eventsArray[] = new ManageChannels($productObj, array(['id' => $this->channel_id]) );

            $eventsArray[] = new ManageVariants(
                $productObj,
                $this->getVariants(),
                [
                    'versions' => [['id' => $this->version_id]],
                    'channel_id' => $this->channel_id
                ]);

            $eventsArray[] = new ManageFamilyAttributes($productObj, $this->createGeneralFamilyAttributeArray(), [['id' => $this->version_id]]);

               $eventsArray[] = new ChannelUpdateStatusEvent(
                    product: $productObj,
                    data: ['shopify_product_id' => $this->product->id]
                );

            ProductServiceProvider::trigger($eventsArray);

            event(new CalculateScore($productObj));

        } catch (Exception $e) {
            $data = [
                'organization_id' => $this->organization_id,
                'description' => "There is an error while saving product <b> sku : {$productObj->sku} </b>.<br>  {$e->getMessage()}",
                'type' => 'shopify',
                'link' => route('products.edit', $productObj->id),
                'link_text' => 'View Product',
                'status' => 'error',
            ];

            (new ErrorLog())->saveErrorLog($data);

            Log::error([$e->getMessage()]);
            if($product_update_flag){
                Product::where('id', $productObj->id)->delete();
            }
        }

       return $success($productObj);
    }

    public function getVersionFromChannel(){
       $this->version_id =  ChannelVersion::query()
           ->select('version_id')
           ->where('channel_id',$this->channel_id)
           ->first()
           ?->version_id;
    }

    public function getImages() : array {
        $images = [];
        foreach ($this->product->images as $image) {
            if(isset($image->variant_ids)){
                if(count($image->variant_ids) == 0){
                    $images[] = [
                        "shopify_image_id" => $image->id,
                        "link" => $image->src,
                        "width" => $image->width ?? null,
                        "height" => $image->height ?? null,
                    ];
                }
            }else{
                $images[] = [
                    "shopify_image_id" => $image->id,
                    "link" => $image->src,
                    "width" => $image->width ?? null,
                    "height" => $image->height ?? null,
                ];
            }

        }
        return $images;
    }

    public function createGeneralFamilyAttributeArray() {
        return [
            [
                "name"=>"General",
                "attributes"=>[
                    [
                        "name"=>"Product Name",
                        'handle'=>"product_name",
                        'attribute_type_id'=>1,
                        "value" => $this->product->title,
                    ],
                    [
                        "name"=>"Description",
                        'handle'=>"description",
                        'attribute_type_id'=>3,
                        "value" => $this->product->body_html,
                    ]
                ]
            ],
            [
                "name"=>"SEO",
                "attributes"=>[
                    [
                        'handle'=>"seo_keyword",
                        'name'=>'Tags',
                        'attribute_type_id'=>1,
                        'value'=>$this->product->tags
                    ],
                    [
                        'name'=>"URL Slug",
                        'handle'=>"seo_url",
                        'attribute_type_id'=>11,
                        "value" => $this->product->handle,
                    ]
                ]
            ]
        ];
    }

    public function getVariants() : array {
        $variants = [];

        $variants['attributes'] = $this->product->options? $this->getOptionAttributes($this->product->options) : [] ;
            foreach ($this->product->variants as $variant) {
                $imgObj = $this->product->images ? $this->getVariantImageObj($variant->id, $this->product->images): [];
                $variants['data'][] = [
                    "id"=>$variant->id??null,
                    "attributes" => $this->product->options? $this->getOptionAttributes($this->product->options) : [],
                    "options" => $this->getOptionValues($variant),
                    "name" => $variant->title??null,
                    "sku" => $variant->sku??null,
                    "price" => $variant->price??null,
                    "weight" => $variant->weight??null,
                    "weight_unit" => $variant->weight_unit??null,
                    "cost_price" => $variant->cost_price??null,
                    "compare_at_price" => $variant->compare_at_price??null,
                    "quantity" => $variant->inventory_quantity??null,
                    "barcode" => $variant->barcode??null,
                    "track_quantity" => $variant->inventory_management == "shopify" ? 1 : 0,
                    "continue_selling" => $variant->inventory_policy == "continue" ? 1 : 0,
                    "files"=>[
                        [
                            "id" => $variant->image_id??null,
                            "link" => !empty($imgObj) ? $imgObj['src'] : null,
                            "width" => !empty($imgObj) ? $imgObj['width'] : '',
                            "height" => !empty($imgObj) ? $imgObj['height'] : '',
                        ]
                    ],
                    "created_at" => $variant->created_at??null,
                    "inventory_id" => $variant->inventory_item_id,
                ];
            }


            return $variants;
    }
    public function getOptionAttributes($options)  {
        try {
            $option_array = [];
            foreach($options as $option) {
                $option_array[] = [
                    "name" => $option['name'],
                    "options" => $option['values']
                ];
            }
            return $option_array;
        }
        catch (\Exception $e) {
            return [];
        }
    }
    public function getOptionValues($variant)  {
        $variants = [];
        if($variant->option1) {
            $variants[] = $variant->option1;
        }
        if($variant->option2) {
            $variants[] = $variant->option2;
        }
        if($variant->option3) {
            $variants[] = $variant->option3;
        }
        return $variants;
    }
    public function getVariantImageObj(string $shopify_variant_id, array $images) {
        try {
            $link = [];
            foreach ($images as $image) {
                foreach ($image->variant_ids as $variant_id) {
                    if($variant_id == $shopify_variant_id) {
                        $link['src'] = $image->src;
                        $link['id'] = $image->id;
                        $link['width'] = $image->width;
                        $link['height'] = $image->height;
                        break;
                    }
                }
            }
            return $link;
        }
        catch(Exception $e) {
            return [];
        }
    }
}


