<?php
namespace App\Classes;

use App\Models\Product\ProductShopifyMapping;

/**
 * @deprecated class
 **/

class ConvertProductObject{

    public function getHandle(string $attribute_value) :string {
        return str_replace(" ", "-", $attribute_value);
    }

    public function getOptions(object $variants) : array{
        $count1=1;
        $options = array();
            foreach( $variants as $variant)
            {
                $variant_decode = json_decode($variant->option,TRUE);
                if(isset($variant_decode['attributes'])) {
                    foreach($variant_decode['attributes'] as $key=>$attribute)
                    {
                        $options[$key]['name']=$attribute['name'];
                        $options[$key]['position']=$count1;
                        $options[$key]['values']=$attribute['options'];
                        $count1++;
                    }
                }
            }
        return $options;
    }

    public function getDefaultOptions() :array{
        $options[0]['name']="Title";
        $options[0]['position']=1;
        $options[0]['values']=["Default Title"];
        return $options;
    }

    public function getDefaultVariant(object $general_family) :array {
        $attribute_array = array();
        if($general_family) {
            foreach ($general_family['attributes'] as $key=>$attribute) {
                $shopify_value = $this->getProductShopifyMapping($attribute->pivotId);

                if ($shopify_value != '') {
                    if ($shopify_value != 'body_html') {
                        $attribute_array['variants'][0][$shopify_value] = $attribute->value;
                    }
                }
            }
        }
        return $attribute_array;
    }

    public function getVariants(object $variants) :array {
        $attribute_array = array();
        $count = 1;
        foreach ($variants as $key=>$variant)
        {
            $count1 = 1;
            $attribute_array['variants'][$key]['title'] = $variant->name;
            $attribute_array['variants'][$key]['price'] = $variant->price;
            $attribute_array['variants'][$key]['sku'] = $variant->sku;
            $attribute_array['variants'][$key]['position'] = $count;
            $attribute_array['variants'][$key]['barcode'] = $variant->barcode;
            $variant_decode = json_decode($variant->option,TRUE);

            for($i=0 ; $i<3; $i++)
            {

                if(isset($variant_decode['options'][$i])) {
                    $attribute_array['variants'][$key]['option'.$count1] = $variant_decode['options'][$i];
                }
                else {
                    $attribute_array['variants'][$key]['option' . $count1] = null;
                }
                $count1++;
            }
            $count++;
        }

        return $attribute_array;
    }

    public function getProductShopifyMapping(int $attribute_family_id) : string{
       $shopify_fields =  ProductShopifyMapping::where("attribute_family_id", $attribute_family_id)
                    ->pluck("shopify_fields")
                    ->first();
       if(!$shopify_fields)
           return '';
       return $shopify_fields;
    }

    public function getGeneralFamily(object $general_family) : array {

        $attribute_array = array();
        if($general_family){
            foreach ($general_family['attributes'] as $attribute) {

                $shopify_value = $this->getProductShopifyMapping($attribute->pivotId);

                if ($shopify_value != '') {
                    if ($shopify_value == 'body_html' || $shopify_value == 'title' ) {
                        $attribute_array[$shopify_value] = $attribute->value;

                        if ($shopify_value == 'title') {
                            $attribute_array['handle'] = $this->getHandle($attribute->value);
                        }
                    }
                }
            }
        }
        return $attribute_array;
    }

    public function getSeoFamily(object $seo_family) : array {
        $attribute_array = array();
        if($seo_family){
            foreach ($seo_family['attributes'] as $attribute) {
                $shopify_value = $this->getProductShopifyMapping($attribute->pivotId);

                if ($shopify_value != '') {
                    if ( $shopify_value == 'metafields_global_title_tag' || $shopify_value == 'metafields_global_description_tag') {
                        $attribute_array[$shopify_value] = $attribute->value;
                    }
                }
            }
        }
        return $attribute_array;
    }

    public function getImages(object $images) : array{
        $count=1;
        $images=array();
        foreach ( $images as $file)
        {
            $images[] = [
                "src"=> $file->link,
                "width"=> $file->width,
                "height"=> $file->height,
                "position"=> $count,
                "created_at"=> $file->created_at,
                "updated_at"=> $file->updated_at,
            ];
            $count++;
        }

        return $images;
    }

    public function getThumbnailImage(object $images) : string {
          return $images[0]->link;
    }

    private function get_single_values(array $array) : string {
        $name = '';
        foreach($array as $value)
        {
            $name= $value->name;
            break;
        }
        return $name;
    }

    public function convert_to_shopify_payload(object $product) : array {

        $variants = array();
        $version = $product['versions']->first();
        $general_family = $version->families->where('name','General')->first();
        $seo_family = $version->families->where('name','SEO')->first();

        $general_family_attributes =  $this->getGeneralFamily($general_family);
        if(!sizeof($product['variants'])){
              $default_variant = $this->getDefaultVariant($general_family);
              $general_family = array_merge($default_variant, $general_family_attributes);
        }
        $seo_family = $this->getSeoFamily($seo_family);

        if(sizeof($product['variants']) > 0) {
           $variants = $this->getVariants($product['variants']);
            $options = $this->getOptions($product['variants']);
        }
        else{
            $options = $this->getDefaultOptions();
        }

        $array= [
            'vendor'=>(sizeof($product['brands']) > 0)?$this->get_single_values( $product['brands']):null,
            'product_type'=>(sizeof($product['categories']) > 0)?$this->get_single_values( $product['categories']):null,
            'status'=> $product->status?'active':'draft',
            "tags"=>[],
            'created_at'=> $product->created_at,
            'updated_at'=> $product->updated_at,
            'published_at'=> $product->updated_at,
            'options'=>$options,
            'images'=>(sizeof($product->files) > 0 )?$this->getImages($product->files):null,
            'image'=>(sizeof($product->files) > 0 )?$this->getThumbnailImage($product->files):null,
        ];

        return (array_merge($general_family,$seo_family,$variants,$array));
    }



}
