<?php

namespace App\Console\Commands;

use App\Classes\Plan\PlanClass;
use App\Models\Channel\Channel;
use App\Models\Product\Variant;
use Illuminate\Console\Command;
use App\Traits\Shopify\ShopifyAPI;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\Channel\ShopifyChannel;
use App\Models\Organization\Organization;
use App\Models\Billing\ShopifySubscription as SubscriptionShopify;

class ShopifySubscription extends Command
{
    use ShopifyAPI;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'organization:shopifySubscription';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'shopifySubscription Command';


    protected $price = 0;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $organizations = Organization::get();
            foreach($organizations as $org) {
                $subscription = SubscriptionShopify::query()->where("organization_id",$org->id)->latest("id")->first();
                if($subscription){
                    $items = PlanClass::$items;
                    foreach ($items as $item) {
                        switch ($item->handle) {
                            case 'sku':
                                $quantity = max(Variant::whereHas("product",function($query) use($org){
                                    $query->where("organization_id",$org->id);
                                })->count(), 1);
                                if ($quantity <= 1000) {
                                } elseif ($quantity <= 2500) {
                                    $price = ($quantity - 1000) * 0.05;
                                    $this->price = $this->price + $price;
                                } elseif ($quantity <= 10000) {
                                    $price = ($quantity - 1000) * 0.03;
                                    $this->price = $this->price + $price;
                                } elseif ($quantity <= 100000) {
                                    $price = ($quantity - 1000) * 0.02;
                                    $this->price = $this->price + $price;
                                } else {
                                    $price = ($quantity - 1000) * 0.01;
                                    $this->price = $this->price + $price;
                                }
                                break;
                            case 'channel':
                                $quantity = max(Channel::where("organization_id", $org->id)->count(), 1);
                                if ($quantity <= 1) {
                                } elseif ($quantity <= 4) {
                                    $price = ($quantity - 1) * 100;
                                    $this->price = $this->price + $price;
                                } elseif ($quantity <= 10) {
                                    $price = ($quantity - 1) * 70;
                                    $this->price = $this->price + $price;
                                } else {
                                    $price = ($quantity - 1) * 50;
                                    $this->price = $this->price + $price;
                                }
                                break;
                                // case 'brand':
                                //     $quantity = max(Brand::where("organization_id",$subscriptions->organization_id)->count(), 1);
                                //     $item->updateQuantity($quantity);
                                //     break;
                        }
                    }

                    if ($this->price > 0) {
                        $shop = ShopifyChannel::find($subscription->shopify_channel_id);
                        if ($shop) {
                            $this->setupCredentialsManually($shop->access_token, $shop->shop)->createBaseUrl();
                            $params = [
                                'usage_charge' => [
                                    'description' => 'Extra usage charge for exceeding limit of Sku and Stores',
                                    'price' => $this->price // Amount to charge
                                ]
                            ];
                            usleep(500000);
                            $response = Http::retry(1, 1000)->post($this->base_url . "recurring_application_charges/{$subscription->recurring_application_charge_id}/usage_charges.json",  $params);
                            if ($response->successful()) {
                                Log::info("Shopify Subscription Credits have been added successfully.");
                            } else {
                                Log::error($response->json());
                            }
                        }
                    }
                    $this->price = 0;
                }
            }
            $this->info('Shopify Subscription Credits have been added successfully.');
        } catch (\Exception $e) {
            $this->error('An error occurred: ' . $e->getMessage());
        }
    }
}
