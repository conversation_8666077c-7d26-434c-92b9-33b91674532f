<?php

namespace App\Services;

class AntDesignArrayConvertor
{
    /**
     * Convert an array to an Ant Design compatible format.
     *
     * @param array $array
     * @return array
     */
    public function FamilyAttributes(array $array = []): array
    {
        return array_map(function ($key, $node) {
            // Accessing the name (which is the group label)
            $groupName = $node['name'];

            // Accessing the attributes, which are the individual options
            $attributes = $node['attributes'];

            return [
                'label' => $groupName,  // Use 'name' as the group label
                'title' => $groupName,  // Group title
                'options' => array_map(function ($attribute_key, $attribute) use ($groupName) {
                    return [
                        'label' => $attribute, // Option label
                        'value' => $groupName . ',' . $attribute_key // Value will be a combination of group name and attribute
                    ];
                }, array_keys($attributes), $attributes)
            ];
        }, array_keys($array['nodes']), $array['nodes']);
    }

}
