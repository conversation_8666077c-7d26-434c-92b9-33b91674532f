<?php

namespace App\Services;

use App\Models\Product\Attribute;
use App\Models\Channel\AttributeChannel;
use App\Models\Channel\Channel;
use App\Models\Channel\ShopifyChannel;
use App\Models\Organization\OrganizationUser;
use App\Jobs\Shopify\CreateMetafieldDefinationJob;
use App\Classes\Product\Attribute as AttributeTypeClass;
use Illuminate\Support\Facades\Log;

class AttributeService
{

    public function setAttributeRules(Attribute $attribute, array $data)
    {
        $attributeTypeClass = new AttributeTypeClass();
        // Base parameters
        $params = [
            'value_type' => $data['value_type'] ?? 'single',
            'shopify_id' => $data['shopify_id'] ?? null,
            'is_required' => $data['is_required'] ?? null,
        ];

        switch ($attribute->attribute_type_id) {
            case 1: // Single line text
                $params['regular_expression'] = $data['regular_expression'] ?? null;
                $params['max'] = $data['max'] ?? null;
                $params['min'] = $data['min'] ?? null;
                $attribute->rules = $attributeTypeClass->singlelinetTextAttribute(...array_values($params));
                break;

            case 2: // Number
                $params['max'] = $data['max'] ?? null;
                $params['min'] = $data['min'] ?? null;
                $params['max_precision'] = $data['max_number_precision'] ?? null;
                $params['type'] = $data['type'] ?? 'integer';
                $attribute->rules = $attributeTypeClass->numberAttribute(...array_values($params));
                break;

            case 3: // Multiline text
                $params['regular_expression'] = $data['regular_expression'] ?? null;
                $params['max'] = $data['max'] ?? null;
                $params['min'] = $data['min'] ?? null;
                $attribute->rules = $attributeTypeClass->multilineTextAttribute(...array_values($params));
                break;

            case 4: // Multiselect
                $params['type'] = $data['type'] ?? 'choices';
                $attribute->rules = $attributeTypeClass->multiSelectAttribute(...array_values($params));
                break;

            case 5: // Date and Time
                $params['start_date'] = isset($data['start_date']) ? date('Y-m-d H:i:s', strtotime($data['start_date'])) : null;
                $params['end_date'] = isset($data['end_date']) ? date('Y-m-d H:i:s', strtotime($data['end_date'])) : null;
                $params['type'] = $data['type'] ?? null;
                $attribute->rules = $attributeTypeClass->dateAndTimeAttribute(...array_values($params));
                break;

            case 6: // File
                $params['file_type'] = $data['file_type'] ?? null;
                $params['file_choice'] = $data['file_choice'] ?? null;
                $attribute->rules = $attributeTypeClass->fileAttribute(...array_values($params));
                break;

            case 7: // Measurement
                $params['type'] = $data['type'] ?? 'weight';
                $params['max_unit'] = $data['max_unit'] ?? null;
                $params['min_unit'] = $data['min_unit'] ?? null;
                $params['max'] = $data['max'] ?? null;
                $params['min'] = $data['min'] ?? null;
                $attribute->rules = $attributeTypeClass->measurementAttribute(...array_values($params));
                break;

            case 8: // Rating
                $params['max'] = $data['max'] ?? null;
                $params['min'] = $data['min'] ?? null;
                $attribute->rules = $attributeTypeClass->ratingAttribute(...array_values($params));
                break;

            case 9: // JSON
                $attribute->rules = $attributeTypeClass->jsonAttribute(...array_values($params));
                break;

            case 10: // True/False
                $attribute->rules = $attributeTypeClass->booleanAttribute(...array_values($params));
                break;

            case 11: // URL
                $attribute->rules = $attributeTypeClass->urlAttribute(...array_values($params));
                break;

            case 12: // Color
                $attribute->rules = $attributeTypeClass->colorAttribute(...array_values($params));
                break;

            case 13: // Variant attributes
                $this->variantAttributes($data, $attribute);
                break;
        }

        $attribute->save();
    }

    public function syncAttributeFamilies(Attribute $attribute, array $families = [])
    {
        if (!empty($families)) {
            $attribute->families()->sync($families);
        }
    }

    public function handleAttributeChannel(Attribute $attribute, array $data)
    {
        if (isset($data['shopify_id'], $data['channel_id'])) {
            AttributeChannel::updateOrCreate(
                [
                    'channel_id' => $data['channel_id'],
                    'attribute_id' => $attribute->id,
                    'store_connect_type' => 'shopify',
                ],
                ['store_connect_id' => $data['shopify_id']]
            );
        }
    }

    public function processAttributeOptions(Attribute $attribute, array $options = [])
    {
        if (in_array($attribute->attribute_type_id, [4, 13])) {
            $options = $attribute->unique_attribute_duplication_options($options, $attribute->id);
            $attribute->attribute_options()->createMany($options);
        } else {
            $attribute->attribute_options()->delete();
        }
    }

    public function syncShopifyMetafield(Attribute $attribute)
    {
        $orgId = $attribute->organization_id;

        $userId = OrganizationUser::where('organization_id', $orgId)->value('user_id');
        $channelIds = Channel::where('organization_id', $orgId)->pluck('id')->toArray();

        if (ShopifyChannel::whereIn('channel_id', $channelIds)->exists()) {
            CreateMetafieldDefinationJob::dispatch([
                'organization_id' => $orgId,
                'channel_ids' => $channelIds,
                'attribute' => $attribute,
                'user_id' => $userId,
            ]);
        }
    }
}
