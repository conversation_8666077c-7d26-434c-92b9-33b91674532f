<?php ?>
@extends('layouts.app_new',['sidebar_display'=> request('user_type') ? false : true])
@section('titles','Checkout')
@section('content')

    <x-products.page-title name="{{trans('checkout.page_title')}}" description=""
                           links="false" button="false"/>
    <form id="checkout_form" action="{{route('subscribe.post')}}" class="formStyle" method="POST">
        @csrf
        <div class="row">
            <div class="col-xl-7 col-lg-7 col-md-12 col-sm-12 col-12 mb-4">
                <input type="hidden" name="stripe_id" value="{{$stripe_id}}">
                <div class="row mt-4">
                    <div class="col-12 col-md-3 d-flex align-items-start mt-3">
                        <h4 class="circle-sm bg-dark">{{ trans('checkout.heading_01') }}</h4>
                        <div class="ms-1 mt-2">{{ trans('checkout.residence') }}</div>
                    </div>

                    <div class="col-12 col-sm-9 col-md-9 col-xl-9 col-lg-9 ">
                        <div class="form-group">
                            <label>{{ trans('checkout.select_country') }}</label>
                            <input
                                id="country_selector" name="country" type="text" required
                                class="form-control"
                                value="{{isset($meta_data) ? $meta_data->country : old('country')}}">
                         </div>

                        <div class="row mt-3">
                            <div class="col-6">
                                <label>{{ trans('checkout.city') }}&nbsp;<span
                                        class="text-danger">*</span></label>
                                <input value="{{isset($meta_data) ? $meta_data->city : old('city')}}" type="text"
                                       class="form-control @error('city') is-invalid @enderror" name="city"
                                       placeholder=""
                                       required/>
                                @error('city')
                                <span class="text-danger">
                                   <small>{{$message}}</small>
                                </span>
                                @enderror
                            </div>
                            <div class="col-6">
                                <label>{{ trans('checkout.state') }}&nbsp;<span
                                        class="text-danger">*</span></label>
                                <input value="{{isset($meta_data) ? $meta_data->state : old('state')}}" type="text"
                                       class="form-control @error('state') is-invalid @enderror" name="state"
                                       placeholder=""
                                       required/>
                                @error('state')
                                <span class="text-danger">
                                   <small>{{$message}}</small>
                                </span>
                                @enderror

                            </div>
                        </div>

                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12 col-md-3 d-flex mt-3">
                        <h3 class="circle-sm bg-dark">{{ trans('checkout.heading_02') }}</h3>
                        <div class="ms-1">{{ trans('checkout.company_information') }}</div>
                    </div>

                    <div class="col-12 col-sm-9 col-md-9 col-xl-9 col-lg-9">
                        <div>
                            <label>{{ trans('checkout.company_address') }}&nbsp;<span class="text-danger">*</span></label>
                            <input value="{{isset($meta_data) ? $meta_data->address : old('address')}}" type="text"
                                   class="form-control @error('address') is-invalid @enderror"
                                   name="address" placeholder=""
                                   required/>
                            @error('address')
                            <span class="text-danger">
                                   <small>{{$message}}</small>
                                </span>
                            @enderror
                        </div>
                        <div class="mt-3">
                            <label>{{ trans('checkout.street_address') }}&nbsp;<span class="text-danger">*</span></label>
                            <input value="{{isset($meta_data) ? $meta_data->street_address : old('street_address')}}"
                                   type="text" class="form-control @error('street_address') is-invalid @enderror"
                                   name="street_address" placeholder=""
                                   required/>
                            @error('street_address')
                            <span class="text-danger">
                                   <small>{{$message}}</small>
                                </span>
                            @enderror
                        </div>
                        <div class="row mt-3">
                            <div class="col-6">
                                <label>{{ trans('checkout.suit_or_unit') }}</label>
                                <input value="{{isset($meta_data) ? $meta_data->unit :  old('unit')}}" type="text"
                                       class="form-control" name="unit"
                                       placeholder=""
                                />
                            </div>
                            <div class="col-6">
                                <label>{{ trans('checkout.postal_code') }}&nbsp;<span class="text-danger">*</span></label>
                                <input value="{{isset($meta_data) ? $meta_data->postal_code : old('postal_code')}}"
                                       type="text"
                                       class="form-control @error('postal_code') is-invalid @enderror"
                                       name="postal_code"
                                       placeholder=""
                                       required/>
                                @error('postal_code')
                                <span class="text-danger">
                                   <small>{{$message}}</small>
                                </span>
                                @enderror
                            </div>
                        </div>

                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12 col-md-3 d-flex align-items-start mt-3">
                        <h3 class="circle-sm bg-dark mb-0">{{ trans('checkout.heading_03') }}</h3>
                        <div class="ms-1 mt-2">
                            {{ trans('checkout.stripe_payment') }}
                        </div>
                    </div>
                    <div class="col-12 col-sm-9 col-md-9 col-xl-9 col-lg-9">
                        <div>
                            <label>{{ trans('checkout.card_name') }}&nbsp;<span
                                    class="text-danger">*</span></label>
                            <input value="" type="text" id="card-holder-name"
                                   class="form-control @error('name') is-invalid @enderror"
                                   name="name" placeholder=""
                                   required/>
                            @error('name')
                            <span class="text-danger">
                                   <small>{{$message}}</small>
                                </span>
                            @enderror
                        </div>
                        <div class="mt-3">
                            <label>{{ trans('checkout.card_number') }}&nbsp;<span
                                    class="text-danger">*</span></label>
                            <!-- Stripe Elements Placeholder -->
                            @if($card_details)
                                @foreach($card_details as $card_detail)
                                    <div class="form-control bg-white p-2 mb-1 d-flex align-items-center">
                                        <input class="h-60 existing_card cursor-pointer card_icon"
                                               name="card_id" type="radio" value="{{$card_detail->id}}">
                                        <label class="ms-3" {{--for="existing_card"--}}>
                                            <img src="{{asset('media/billing/visa-5.png')}}" alt=""> &nbsp;
                                            **** **** **** {{$card_detail->card->last4}}
                                        </label>
                                    </div>
                                @endforeach
                                <div class="form-check bg-white form-control add-card">
                                    <button type="button" class="btn btn-sm btn-outline-dark">
                                       <i class="fa fa-plus"></i>
                                        {{ trans('checkout.add_card_btn') }}
                                    </button>
                                </div>

                                <div class="d-none add-card-input">
                                    <div id="card-element"></div>
                                </div>
                            @else
                                <div id="card-element"></div>
                            @endif
                        </div>
                        <span class="text-danger" role="alert" id="error_alert">
                                    <small id="error_span"></small>
                                </span>
                        <button type="button"
                                class="mt-40 btn btn-primary w-100" id="card-button"
                                data-secret="{{ $intent->client_secret }}">
                            {{ trans('checkout.confirm_and_pay_btn') }}
                        </button>


                    </div>
                </div>
            </div>
            <div class="col-12 col-lg-5 col-md-5 col-sm-5 d-none d-sm-none d-md-none d-lg-block mt-4 pt-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h3 class="text-white mb-0">
                            {{ trans('checkout.checkout_details') }}
                        </h3>
                        <div class="mt-3 mb-2">
                            <span class="text-white fw-400 fs-14">{{ $plan->name }} - {{request('plans_type') == 'year' ? 'Yearly' : 'Monthly'}}</span>
                        </div>



                        <div class="mb-4">

                            <input type="text" class="form-control icon-input mb-1" id="coupon_input" name="coupon"
                                   placeholder="Enter discount code" value="">
                            <span class="coupon_error">
                                        {{ trans('checkout.coupon_error_message') }}
                            </span>
                            <div id="coupon_success" class="icon-container" style="display: none">
                                <i class="bi bi-check-circle-fill icon-success"></i>
                            </div>

                        </div>

                        <div class="border"></div>

                        <div class="row">
                            <div class="col-8">
                                <p class="text-white mt-3">
                                    {{ trans('checkout.subtotal') }}
                                    <br>
                                    @if(request('plans_type') == 'year')
                                        <small id="subtotal" class="text-white" style="position: absolute;">
                                            {{ trans('checkout.plan_per_year', ['plan_per_year' => $plan->price_per_year]) }}
                                        </small>
                                    @endif
                                </p>
                            </div>
                            <div class="col-4">
                                <p id="subtotal" class="text-white float-right mt-3">
                                    ${{request('plans_type') == 'year' ? $plan->price_per_year*12  : $plan->price_per_month}}</p>
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-8">
                                <p class="mb-0 text-white">
                                    {{ trans('checkout.discount') }}
                                </p>
                            </div>
                            <div class="col-4">
                                <p id="discount" class="Roboto bold mb-0 text-white float-right">
                                    <span id="coupon_price">
                                                {{ trans('checkout.coupon_price') }}
                                    </span>
                                </p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-8">
                                <p class="mb-0 text-white">
                                    {{ trans('checkout.total') }}
                                </p>
                            </div>
                            <div class="col-4">
                                <p id="total" class="mb-0 float-right text-white">$<span
                                        id="total_price">{{request('plans_type') == 'year' ? $plan->price_per_year*12 : $plan->price_per_month}}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
@endsection
@push('footer_scripts')
    <script src="https://js.stripe.com/v3/"></script>
    <script>
        var stripe_input = $(".add-card-input")
        $(".add-card").on('click',function () {
            $(".add-card").addClass("d-none").removeClass("d-block");
            stripe_input.addClass("d-block").removeClass('d-none');
            if ($('.existing_card').prop('checked', true)) {
                $('.existing_card').prop('checked', false);
            }
        });
        $(".card_icon").click(function () {
            if (stripe_input.hasClass("d-block")) {
                $(".add-card").removeClass("d-none").addClass("d-block");
                stripe_input.removeClass("d-block").addClass("d-none");
            }
        })
    </script>
    <script>
        const stripe = Stripe('{{ env('STRIPE_KEY') }}');
        const elements = stripe.elements();
        const cardElement = elements.create('card');
        cardElement.mount('#card-element');
        const cardHolderName = document.getElementById('card-holder-name');
        const cardButton = document.getElementById('card-button');
        const clientSecret = cardButton.dataset.secret;

        $("#card-button").on('click', function (e) {
            $(this).prop('disabled', true); //disable further clicks
            $(this).html('Processing ....');
        })

        cardButton.addEventListener('click', async (e) => {
            //if user selects existing card
            if ($('.existing_card').length > 0 && $('.existing_card').is(':checked')) {
                $("#checkout_form").submit();
            } else {
                // if user inputs new card
                const {setupIntent, error} = await stripe.confirmCardSetup(
                    clientSecret, {
                        payment_method: {
                            card: cardElement,
                            billing_details: {name: cardHolderName.value}
                        }
                    }
                );

                if (error) {
                    // Display "error.message" to the user...
                    $("#error_alert").css('display', 'block');
                    $("#error_span").html(error.message);
                    $("#card-button").prop('disabled', false); //enabling the button on the error
                    $("#card-button").html('Confirm and Pay')
                } else {
                    // The card has been verified successfully...
                    // $(cardButton).html('Processing ....');
                    // The card has been verified successfully...
                    $.ajax({
                        type: 'POST',
                        url: '{{ route('billing.checkout.add_card') }}',
                        data: {
                            "payment_method": setupIntent.payment_method,
                            "_token": '{{ csrf_token() }}'
                        },
                        success: function (data) {
                            //submit form and pay
                            $("#checkout_form").submit();
                        },
                        error: function (error) {
                            console.log("error", error.message)
                        }
                    });
                }
            }
        });

        // apply coupon and retrieve it
        $("#coupon_input").keyup(function () {
            let coupon = $(this).val();
            if (coupon == null || coupon == '' || coupon.length < 8) {
                return 0;
            }
            if (coupon.length > 8) {
                alert("Coupon cannot be more than 8 characters.");
                return 0;
            }

            $.ajax({
                type: 'GET',
                url: '{{ route('payment.coupon.retrieve') }}',
                data: {
                    "coupon": coupon
                },
                success: function (data) {
                    console.log(data);

                    if (!$("#coupon_input").prop('readonly')) {

                        let total = parseFloat($("#total_price").text());

                        if (data.amount_off != null) {
                            $("#coupon_price").text("$" + (data.amount_off / 100).toFixed(2));
                            if ((total - (data.amount_off / 100)) >= 0)
                                $("#total_price").text((total - (data.amount_off / 100)).toFixed(2));
                            else
                                $("#total_price").text('0');
                        } else {
                            $("#coupon_price").text(data.percent_off + "%");
                            if ((total - (total * data.percent_off / 100)) >= 0)
                                $("#total_price").text((total - (total * data.percent_off / 100)).toFixed(2));
                            else
                                $("#total_price").text('0');
                        }
                        $("#coupon_success").show(100);
                    }
                    $("#coupon_input").prop('readonly', true);
                },
                error: function (error) {
                    alert(error.responseText);
                }
            });
        });
    </script>

    <script>
        $("#country_selector").countrySelect();

        $(".close").click(function () {
            $("#success").hide();
        });
    </script>
    <!--Phone Flags-->
    <script>
        let input = document.querySelector("#phone");
        ipLookUp();

        // get current user country
        function ipLookUp() {
            $.ajax({
                dataType: 'json',
                url: 'https://api.hostip.info/get_json.php',
                success: function (data) {
                    let $ip = data['ip'],
                        $city = data['city'],
                        $countryCode = data['country_code'],
                        $countryName = data['country_name'];
                    /* window.intlTelInput(input, ({
                         separateDialCode: false,
                         preferredCountries: ["us", "ca"],
                         utilsScript: "{{asset('public/js/utils.js')}}",
                        nationalMode: false,
                        initialCountry: $countryCode
                    }));*/
                },
                error: function (jqXHR, textStatus) {
                    window.intlTelInput(input, ({
                        separateDialCode: false,
                        preferredCountries: ["us", "ca"],
                        utilsScript: "{{asset('public/js/utils.js')}}",
                        nationalMode: false,
                    }));
                }
            });
            return 0;
        }

        function searchKeyPress(e) {
            e = e || window.event;
            if (e.keyCode === 13) {
                document.getElementById('registeration_next_btn_id').click();
                return false;
            }
            return true;
        }
    </script>
@endpush
