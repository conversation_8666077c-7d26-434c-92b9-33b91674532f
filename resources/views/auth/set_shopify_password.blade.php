@extends('layouts.app_new',['sidebar_display'=>false])
@section('titles','Set Password')
@section('content')
    <div class="row h-100">

        <div class="col-12 justify-content-center center-align mt-5 pt-5">
            <!--Logo-->
            <div class="d-flex flex-column flex-column-auto text-center">
                <a href="{{env('APIMIO_URL')}}">
                    <img src="{{asset('media/logo.png')}}" alt="Logo">
                </a>
            </div>

            <div class="row justify-content-center align-items-center">
                <div class="col-12 col-sm-10 col-md-10 col-lg-5 col-xl-4 px-3 mt-3">
                    <form id="shopify_set_pass_form" method="POST" action="{{route('save.shopify.product')}}" class="formStyle">
                        @csrf
                        <div class="pb-5">
                            <div class="mb-4">
                                <h1 class="text-center text-dark ">
                                    {{ trans('set_shopify_pass.page_title') }}
                                </h1>
                                <p class="Roboto text-center text-dark">
                                    {{ trans('set_shopify_pass.page_description') }}
                                </p>
                            </div>

                            <div class="form-group mb-3">
                                <label for="organization">
                                    {{ trans('set_shopify_pass.password') }}&nbsp;<span style="color: #ff8178">*</span>
                                </label>
                                <input type="password" class="form-control @error('password') is-invalid @enderror"
                                       id="password" name="password" value="" autofocus required/>
                                        @error('password')
                                        <span class="text-danger" role="alert">
                                            <small>{{ $message }}</small>
                                        </span>
                                        @enderror
                            </div>
                            <div class="form-group mb-3">
                                <label for="timezone">{{ trans('set_shopify_pass.import_products') }}</label>
                                <select class="form-control @error('sync_product') is-invalid @enderror" id="sync_product"
                                        name="sync_product" required>
                                    <option value="" disabled selected>{{__("select")}}</option>
                                    <option value="yes">{{__("Yes")}}</option>
                                    <option value="no">{{__("No")}}</option>
                                </select>
                                @error('sync_product')
                                <span class="text-danger" role="alert">
                                    <small>{{ $message }}</small>
                                </span>
                                @enderror
                            </div>

                        </div>
                        <div class="form-group mb-3">
                            <button id="org_create_cont_btn" type="submit"
                                    class=" btn btn-primary w-100">
                                {{ trans('set_shopify_pass.continue_btn') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('footer_scripts')
    <script>

    </script>
@endpush

