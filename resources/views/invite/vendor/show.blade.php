@extends('layouts.app_new')
@section('titles','View Vendor')
@section('content')
    {{--    <x-invite.assets.disconnect-modal text="All catalogs shared with the vendor will be paused. You can connect again anytime." button="Disconnect Vendor" title="Disconnect vendor"/>--}}
    <div class="row">
        <div class="col-12 col-sm-12 col-md-6 col-lg-6">
            <div>
                <h2>
                    <a href="{{route('vendor.index')}}" class="mb-0 clr-grey text-decoration-none">
                        {{trans('vendors_show.page_title')}}
                    </a>
                    <span class="clr-grey"> / </span>
                    <span class="mb-0">
                       {{$vendors->fname  . " " . $vendors->lname}}
                    </span>
                </h2>
                <div class="my-3">{{__($data["vendor"]->designation)}}</div>
            </div>
        </div>
        <div class="col-12 col-sm-12 col-md-6 col-lg-6">
            @switch($data["vendor"]->get_status())
                @case(1)
                <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">
                    <div class="p-2">
                        <a href="#" data-id="{{$data['vendor']->id}}" data-retailer-name=""
                           data-toggle="modal" data-target="#disconnect-modal"
                           class="btn btn-outline-danger px-4">
                            {{trans('vendors_show.disconnect_btn')}}
                        </a>
                    </div>
                </div>
                @break

                @case(2)
                @if($data['vendor']->email == auth()->user()->email)
                    <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">
                        <div class="p-2">
                            <a href="{{ route("invite.accept",$data["vendor"]->id) }}"
                               class="btn btn-outline-success px-4">
                                {{trans('vendors_show.connect_btn')}}
                            </a>
                        </div>
                    </div>
                @else
                    <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">
                        <div class="p-2">
                            <a href="{{ route("invite.resend",$data["vendor"]->id) }}"
                               class="btn btn-outline-success px-4">
                                {{trans('vendors_show.resend_invite_btn')}}
                            </a>
                        </div>
                    </div>
                @endif
                @break

                @case(3)
                <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">
                    <div class="p-2">
                        <a href="{{ route("invite.accept", $data["vendor"]->id) }}"
                           class="btn btn-outline-success px-4">{{trans('vendors_show.accept_btn')}}</a>
                    </div>
                    <div class="p-2">
                        <a href="{{ route("invite.decline", $data["vendor"]->id) }}"
                           class="btn btn-outline-danger px-4">{{trans('vendors_show.decline_btn')}}</a>
                    </div>
                </div>

                @break
                @case(4)
                <div class="d-flex flex-column align-items-lg-end">
                    {{--If Vendor email exists show token          --}}
                    @if($data["vendor"]->token)
                       <div>
                           <label class="mb-1">{{trans('vendors_show.copy_link_description')}}</label>
                           <div class="input-group formStyle">
                               <input type="text" class="form-control" id="myInput"
                                      value="{{ route("invite", $data["vendor"]->token??'') }}"
                                      placeholder="{{ route("invite", $data["vendor"]->token??'') }}">
                               <span class="tooltip1">
                                                <span class="tooltiptext" id="myTooltip">Copy to clipboard</span>
                                                <div class="input-group-append ">
                                                        <button type="button"
                                                                class="btn btn-dark shadow-0"
                                                                onclick="copyText()"
                                                                onmouseout="outFunc()"
                                                                style="z-index: 0">
                                                            {{trans('vendors_show.copy_btn')}}
                                                        </button>
                                                </div>
                                     @endif
                                    </span>
                           </div>
                       </div>
                </div>
                @break
                @case(5)
                @if($data['vendor']->email == auth()->user()->email)
                    <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">
                        <div class="p-2">
                            <a href="{{ route("invite.accept",$data["vendor"]->id) }}"
                               class="btn btn-outline-success">
                                {{trans('vendors_show.connect_btn')}}
                            </a>
                        </div>
                    </div>
                @else
                    <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">
                        <div class="p-2">
                            <a href="{{ route("invite.resend",$data["vendor"]->id) }}"
                               class="btn btn-outline-success">
                                {{trans('vendors_show.resend_invite_btn')}}
                            </a>
                        </div>
                    </div>
                @endif
            @endswitch
        </div>
    </div>
    <div class="card border-radius shadow-none border-color mb-2 mt-3">
        <div class="card-body">
            <div class="row">
                <div class="col-12 col-md-6">
                    <div class="mt-1 mb-3">
                        <h3 class=m-0">{{trans('vendors_show.vendor_detail')}}</h3>
                    </div>
                </div>
                <div class="col-12 col-md-6">
                </div>
                <div class="col-12">
                    <div class="table-responsive">
                        <table class="table">
                            <tbody>
                            <tr>
                                <td>{{trans('vendors_show.first_name')}}</td>
                                <td>
                                    {{$vendors->fname }}
                                </td>

                                <td>{{trans('vendors_show.last_name')}}</td>
                                <td>
                                    {{$vendors->lname }}
                                </td>

                            </tr>
                            <tr>
                                <td>{{trans('vendors_show.phone_number')}}</td>
                                <td>
                                    {{$vendors->phone }}
                                </td>
                                <td>{{trans('vendors_show.shared_catalogs')}}</td>
                                <td>{{ $data["vendor"]->get_catalogs() }}</td>
                            </tr>
                            <tr>
                                <td>{{trans('vendors_show.email')}}</td>
                                <td>{{ $vendors->email }}</td>

                                <td>{{trans('vendors_show.status')}}</td>
                                <td>{!! $data["vendor"]->get_status_badge() !!}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @if(count($data["products"]) > 0)
        @if($data["vendor"]->get_status() != 2 )
            @foreach($data["products"] as $product)
                <div class="card border-radius border-color shadow-none">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                <ul class="nav nav-tabs float-left" id="myTab" role="tablist">
                                    {{--<li class="px-3 py-2">
                                    <label for="exampleCheck1"></label><input type="checkbox" class="form-check-input" id="exampleCheck1">
                                    </li>--}}
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link active Roboto"
                                           id="general-tab"
                                           data-toggle="tab"
                                           href="#general_{{$product->id}}"
                                           role="tab"
                                           aria-controls="general"
                                           aria-selected="true">
                                            {{trans('vendors_show.general_info')}}
                                        </a>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link Roboto"
                                           id="images-tab"
                                           data-toggle="tab"
                                           href="#images_{{$product->id}}"
                                           role="tab"
                                           aria-controls="images"
                                           aria-selected="false">
                                            {{trans('vendors_show.images', ['image_count' => $product->files->count()])}}
                                        </a>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link Roboto"
                                           style="border-bottom: 0!important;"
                                           id="brands-tab"
                                           data-toggle="tab"
                                           href="#brands_{{$product->id}}"
                                           role="tab"
                                           aria-controls="brands"
                                           aria-selected="false">
                                            {{trans('vendors_show.brands', ['brand_count' => $product->brands->count()])}}
                                            {{--{{__("Brands (".$product->brands->count().")")}}--}}
                                        </a>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link Roboto"
                                           style="border-bottom: 0!important;"
                                           id="variations-tab"
                                           data-toggle="tab"
                                           href="#variations_{{$product->id}}"
                                           role="tab"
                                           aria-controls="variations"
                                           aria-selected="false">
                                            {{trans('vendors_show.variations', ['variants_count' => $product->variants->count()])}}
                                            {{--{{__("Variations (".$product->variants->count().")")}}--}}
                                        </a>
                                    </li>
                                </ul>
                                <ul class="float-left float-md-right mt-3 m-md-0 pl-0">
                                    <li class="list-group-item p-0 mb-2">
                                        @php
                                            $status =  ($product->channels()->cloneAction($product->channels[0]->id,$product))
                                        @endphp
                                        @if($status == 'cloned')
                                            @if($data["vendor"]->get_status() == 5 || $data["vendor"]->get_status() == 3 )
                                                <a class="btn btn-light ripplelink" style="width: 130px">
                                                    {{trans('vendors_show.clone_btn')}}
                                                </a>
                                            @else
                                                <a href="{{ route("vendor.product.clone", [$data["vendor"]->id, $product->id]) }}"
                                                   class="btn btn-success ripplelink" style="width: 130px">
                                                    {{trans('vendors_show.clone_btn')}}
                                                </a>
                                            @endif
                                        @else
                                            @if($data["vendor"]->get_status() == 5  || $data["vendor"]->get_status() == 3)
                                                <a class="btn btn-light ripplelink text-capitalize"
                                                   style="width: 130px">
                                                    {{__($status)}}
                                                </a>
                                            @else
                                                <a href="{{ route("vendor.product.clone", [$data["vendor"]->id, $product->id]) }}"
                                                   class="btn btn-primary ripplelink text-capitalize"
                                                   style="width: 130px">
                                                    {{__($status)}}
                                                </a>
                                            @endif
                                        @endif
                                    </li>
                                </ul>
                                <hr class="my-3 divider">

                                <div class="tab-content" id="myTabContent">
                                    <div class="tab-pane fade show active" id="general_{{$product->id}}" role="tabpanel"
                                         aria-labelledby="general-tab">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="media">
                                                    <img
                                                        src="{{ isset($product->files[0]) ? $product->files[0]->link : asset("img/apimio_default.jpg")}}"
                                                        class="align-self-start mr-3" alt="image" width="160"
                                                        height="160">
                                                    <div class="media-body">
                                                        <p class="Roboto bold text-dark">{{ $product->get_name() }}</p>
                                                        <p class="Roboto bold text-dark">{{ $product->sku }}</p>
                                                        <p class="Roboto text-grey">
                                                            @foreach($product->categories as $cat)
                                                                {{ $cat-> name}} @if($loop->last) , @endif
                                                            @endforeach
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade"
                                         id="images_{{$product->id}}"
                                         role="tabpanel"
                                         aria-labelledby="images-tab">
                                        <div class="row">
                                            <div class="col-12">
                                                <h4 class="Roboto bold m-0">{{trans('vendors_show.product_images')}}</h4>
                                            </div>
                                            <div class="col-12">
                                                <div class="row">
                                                    @foreach($product->files as $file)
                                                        <div class="col-6 col-sm-4 col-md-6 col-lg-2 p-2 pl-3">
                                                            <div class="border-radius">
                                                                <img src="{{ $file->link }}"
                                                                     width="100%"
                                                                     class="border-radius vendor-img">
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade"
                                         id="vendors_{{$product->id}}"
                                         role="tabpanel"
                                         aria-labelledby="vendors-tab">
                                        <div class="row">
                                            <div class="col-12">
                                                <ul>
                                                    @foreach($product->vendors as $v)
                                                        <li>
                                                            {{$v->name}}
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade"
                                         id="brands_{{$product->id}}"
                                         role="tabpanel"
                                         aria-labelledby="brands-tab">
                                        <div class="row">
                                            <div class="col-12">
                                                <ul>
                                                    @foreach($product->brands as $brand)
                                                        <li>
                                                            {{$brand->name}}
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="variations_{{$product->id}}" role="tabpanel"
                                         aria-labelledby="variations-tab">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="table-responsive">
                                                    <table class="table table-borderless text-center">
                                                        <thead class="thead-light">
                                                        <tr>
                                                            <th scope="col">
                                                                {{__("IMAGE")}}
                                                            </th>
                                                            <th scope="col">{{__("SKU")}}
                                                            </th>
                                                            <th scope="col">{{__("NAME")}}
                                                            </th>
                                                            <th scope="col">{{__("PRICE")}}
                                                            </th>
                                                            <th scope="col">{{__("QUANTITY")}}
                                                            </th>
                                                            <th scope="col">
                                                                {{__("BARCODE")}}
                                                            </th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        @foreach($product->variants as $variant)
                                                            <tr>
                                                                <td>
                                                                    <img
                                                                        src="{{$variant->file?$variant->file->link:''}}"
                                                                        alt="image" width="70" height="70">
                                                                </td>
                                                                <td>{{$variant->sku}}</td>
                                                                <td>
                                                                    {{$variant->name}}
                                                                </td>
                                                                <td>$ {{$variant->price}}</td>
                                                                <td>{{$variant->quantity}}</td>
                                                                <td>
                                                                    {{$variant->barcode}}
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
                {{(sizeof($data["products"]) >0)?$data["products"]->links():'' }}
                </div>
                @endif
                @else
                    <x-general.empty-page description="{{trans('vendors_show.Products_empty')}}"/>
                @endif
                <!-- Modal DELETE-->
                <div class="modal fade" id="delete-modal" tabindex="-1" aria-labelledby="deleteModalLabel"
                     aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title Poppins semibold"
                                    id="exampleModalLabel">{{trans('vendors_show.modal_title')}}</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <p class="Poppins regular">
                                    {{trans('vendors_show.modal_description')}}
                                </p>

                                <div class="modal-footer">
                                    <button type="button" data-dismiss="modal" id="delete-cancel-btn"
                                            class="btn Roboto bold float-left btn-black"
                                            style="color: #101010!important;width: 120px;">
                                        {{trans('vendors_show.cancel_btn')}}
                                    </button>
                                    <a href=""
                                       style="width: 196px;" id="delete-vendor"
                                       class="add-btn btn del-btn">
                                        {{trans('vendors_show.delete_btn')}}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <x-invite.disconnect-modal text="Are you sure you want to disconnect vendor?" button="Disconnect"
                                           title="Disconnect Vendor" :account="$data['vendor']"/>
@endsection
@push('footer_scripts')
    <script>
        function copyText() {
            var copyText = document.getElementById("myInput");
            /* Select the text field */
            copyText.select();
            copyText.setSelectionRange(0, 99999); /* For mobile devices */

            /* Copy the text inside the text field */
            navigator.clipboard.writeText(copyText.value);
            var tooltip = document.getElementById("myTooltip");
            tooltip.innerHTML = "Copied Link ";
        }
        function outFunc() {
            var tooltip = document.getElementById("myTooltip");
            tooltip.innerHTML = "Copy to clipboard";
        }
    </script>
@endpush
