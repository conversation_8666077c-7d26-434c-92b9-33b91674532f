<?php ?>
@extends('layouts.app_new')
@section('titles','Catalog')
@section('content')
<div>
    <x-products.page-title name="{{trans('channel.page_title')}}" description="{{trans('channel.page_description')}}" links="false" button="false" buttonname="null" />
    <div class="row text-center">
        <div class="col-12">
            <img class="my-5" src="{{ asset('media/<EMAIL>') }}" alt="">
            <br>
            <img class="mb-4" src="{{ asset('media/apimio-shopify-link.png') }}" width="20%" alt="">
            <form method="get" action="{{ route("channel.shopify.install") }}" id="chan_shop_creat_form">
                <div class="col-sm-8 col-lg-4 col-10 mx-auto">
                        <div class="d-flex">
                            <div class="flex-grow-1">
                                <input name="should_not_billed" type="hidden" value="1">
                                <input name="sync_metafields" type="hidden" value="1">
                                <input name="channel_id" type="hidden" value="{{$id}}">
                                <!-- <input name="add_new_channel" type="hidden" value="1"> -->
                                <input name="shop" type="text" class="form-control" style="border-top-right-radius: 0; border-bottom-right-radius: 0;" placeholder="e.g., shopname.myshopify.com" aria-label="Recipient's username" aria-describedby="button-addon2" required>
                            </div>
                            <div>
                                <button class="ripplelink btn btn-primary pb-4" style="border-top-left-radius: 0; border-bottom-left-radius: 0;" type="submit" id="chan_shop_creat_btn button-addon2">
                                    <i class="fa fa-plus-circle "></i>
                                    {{trans('channel_shopify.connect_shop_btn')}}
                                </button>
                            </div>
                        </div>
                        <div class="mb-3 form-check d-flex align-items-center mt-2">
                            <input type="checkbox" class="form-check-input me-2" checked id="sync_product_check" name="add_new_channel" value="1">
                            <label class="form-check-label mt-1" for="sync_product_check">Fetch products from shopify store.</label>
                        </div>
                        <!-- <div class="form-check mt-3">
                            <input type="checkbox" class="form-check-input" checked name="add_new_channel" value="1" id="sync_product_check">
                            <div>
                                <label class="form-check-label" for="sync_product_check">Fetch Product</label>
                            </div>
                        </div> -->
                    </div>

            </form>
        </div>
    </div>

</div>
@endsection
