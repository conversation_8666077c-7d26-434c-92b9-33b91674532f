<div class="card mb-0 border-radius shadow-none border position-relative">

    <!--TODO Loading-->
    {{--<div class="spinner-border position-absolute loaderpopover-hover" role="status"
         style="color: #007BFF;top: 5%; right: 3%;width: 24px;height: 24px"
         data-toggle="popover"
         data-placement="top" data-html="true"
         data-content="Syncing...">
        <span class="sr-only">Loading...</span>
    </div>--}}

    <div class="row no-gutters">
        <div class="col-xl-7 col-lg-6 col-md-7 col-sm-6 col-12">
            <div class="card-body">
                <h5 class="Poppins semibold mb-lg-4 mb-xl-5 mb-5 text-dark">{{__('PRODUCTS STATUS')}}
                    <span class="popover-hover-2"
                          style="font-size: 16px;font-weight: normal;"
                          data-toggle="popover" data-placement="right" data-html="true"
                          data-content="<ul class='list-group ul-popover border-0'>
                                            <li class='list-group-item border-0 px-0 pb-1 pt-2 '>
                                                <strong class='Roboto bold'>{{__('Unassigned')}}</strong>:
                                                <span class='Roboto regular text-break' style='width:50px'>
                                                                       {{__('No vendors are assigned')}}
                              </span>
                          </li>
    <li class='list-group-item border-0 px-0 py-1 '>
        <strong class='Roboto bold'>{{__('Assigned')}}</strong>:
                                                              <span class='Roboto regular text-break' style='width:50px'>
                                                                    {{__('Vendors are assigned')}}
                              </span>
                          </li>
                          <li class='list-group-item border-0 px-0 py-1 '>
                              <strong class='Roboto bold'>{{__('Updated')}}</strong>:
                                                              <span class='Roboto regular text-break' style='width:50px'>
                                                                    {{__('Products are updated')}}
                              </span>
                          </li>
                          <li class='list-group-item border-0 px-0 pt-1 pb-2 '>
                              <strong class='Roboto bold'>{{__('Synced')}}</strong>:
                                                              <span class='Roboto regular text-break' style='width:50px'>
                                                                    {{__('Products are synced with Shopify')}}
                              </span>
                          </li>

                      </ul>">
                                                    <img src="{{asset('media/retailer-dashboard/info.png')}}" alt="">
                                               </span>
                </h5>

                <ul class="pl-0 custom-list mt-1">
                    <li class="card-text mb-2 li-unassign Roboto text-dark">
                        {{__('Published: ')}} {{$data['publish_product']??0}}
                    </li>
                    <li class="card-text mb-2 li-assign Roboto text-dark">
                        {{__('Draft:')}} {{$data['draft_product']??0}}
                    </li>
                    <li class="card-text mb-2 li-retired Roboto text-dark">
                        {{__('Synced:')}} {{ $data['sync_product']??0 }}
                    </li>
                </ul>
            </div>
        </div>
        @if(isset($data) ? ($data['publish_product'] == 0 && $data['draft_product'] ==0 && $data['sync_product']== 0) : true)

            <x-dashboard.empty-doughnut-chart/>
        @else
            <div class="col-xl-5 col-lg-6 col-md-5 col-sm-6 col-12 mt-4 mb-4 pr-sm-3">
                <div>
                    <canvas id="doughnutChart1" width="160" height="260"></canvas>
                </div>
            </div>
        @endif
    </div>
</div>

@push('footer_scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        @if(isset($data))
        let data2 = {
            datasets: [{
                data:{{$data['graph'] ?? 0}},
                backgroundColor: [
                    "#72CC60",
                    "#FF5454",
                    "#A5A5A5",

                ],
            }],
        };
        let ctx = $("#doughnutChart");

        new Chart("doughnutChart1", {
            data: data2,
            type: 'doughnut',
            options: {
                cutout: 80,
                // percentageInnerCutout : 80,
                // cutoutPercentage: 100,
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    display: false
                }
            }
        });
            @endif

    </script>
@endpush
