<div>
    <div class="row">
        <div class="col-12 col-md-6 border-right">
            <h3 class="Roboto mt-0 semibold text-dark" style="font-size: 20px">{{ trans('apimio_dashboard.vendor_title') }}</h3>
{{--            <h3 class="Roboto mt-0 semibold text-dark" style="font-size: 20px">{{__("Vendor Added!")}}</h3>--}}
            <p class="Roboto regular">{{ trans('apimio_dashboard.vendor_description') }}</p>
{{--            <p class="Roboto regular">{{__("Invitation has been sent to <PERSON>. 345 products will be shared with her when she accepts.")}}</p>--}}

            <div class="d-flex flex-column mb-3">
                <div class="p-2 align-self-center">
                    <img src="{{asset('media/guide/no_vendors.png')}}" alt="">
                </div>
                <div class="p-2 align-self-center">
                    <a href="{{route('vendor.create')}}" style="width: 194px" id="add-vendor" class="btn btn-primary ripplelink disabled-with-text">
                        {{ trans('apimio_dashboard.invite_vendor_btn') }}
                    </a>
                </div>
            </div>

            {{--<div>
                <canvas id="VendorDoughnutChart" width="160" height="180"></canvas>
            </div>--}}
        </div>

        <div class="col-12 col-md-6">
            <h3 class="Roboto mt-0 semibold text-dark" style="font-size: 20px">{{ trans('apimio_dashboard.retailer_title') }}</h3>
            <p class="Roboto regular">{{ trans('apimio_dashboard.retailer_description') }}</p>

            <div class="d-flex flex-column mb-3">
                <div class="p-2 align-self-center">
                    <img src="{{asset('media/guide/no_retailers_found.png')}}" alt="">
                </div>
                <div class="p-2 align-self-center">
                    <a href="{{route('retailer.create')}}" style="width: 194px" id="add-retailer" class="btn btn-primary ripplelink disabled-with-text">
                        {{ trans('apimio_dashboard.invite_retailer_btn') }}
                    </a>
                </div>
            </div>

            {{--<div>
                <canvas id="RetailerDoughnutChart" width="160" height="180"></canvas>
            </div>--}}

        </div>
    </div>
</div>
@push('footer_scripts')
    <script>
        let data_vendor = {
            labels: ['Pending', 'Declined', 'Connected'],
            datasets: [{
                label: ['Pending', 'Declined', 'Connected'],
                data:[
                    20,
                    20,
                    50
                ],
                backgroundColor: [
                    "#F7C130",
                    "#F72B2B",
                    "#6ECE65",

                ],
            }],
        };

        new Chart("VendorDoughnutChart", {
            data: data_vendor,
            type: 'doughnut',
            options: {
                cutout: 65,
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        let data_retailer = {
            labels: ['Pending', 'Declined', 'Connected'],
            datasets: [{
                label: ['Pending', 'Declined', 'Connected'],
                data:[
                    20,
                    20,
                    50
                ],
                backgroundColor: [
                    "#F7C130",
                    "#F72B2B",
                    "#6ECE65",


                ],
            }],
        };

        new Chart("RetailerDoughnutChart", {
            data: data_retailer,
            type: 'doughnut',
            options: {
                cutout: 65,
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

    </script>
@endpush
