<div class="row">
    <div class="col-12 d-flex align-items-center justify-content-between">
        <div class="col-6>
        <h2 class="clr-grey mb-0">
            <a href="{{ $routes ?? '' }}" class="clr-grey text-decoration-none">
                {{ $name ?? '' }}
                {{-- @dd($name) --}}
            </a>
            {{ __(' /') }}
            <sapn class="text-dark"> Create a New @isset($name)
                @switch($name)
                    @case('Manage Categories')
                        Category
                        @break
                    @case('Manage Attribute Sets')
                        Attribute Set
                        @break
                    @case('Manage Attributes')
                        Attribute
                        @break
                    @case('Manage Your Brands')
                        Brand
                        @break
                    @case('Manage Languages')
                        Language
                        @break
                    @case('Manage Locations')
                        Location
                        @break
                    @case('Manage Stores')
                        Store
                        @break
                    @case('Manage Vendors')
                        Vendor
                        @break
                    @default
                        {{ $name }}
                @endswitch
            @endisset
        </sapn>
            </h2>
            @if ($name == 'Manage Categories')
                <p class="mb-0"> Define a new category to better organize your product offerings. </p>
            @elseif($name == 'Manage Attribute Sets')
                <p class="mb-0"> Define a new set of attributes to streamline product management. </p>
            @elseif($name == 'Manage Attributes')
                <p class="mb-0"> Choose the attribute type and enter the details to add a new product attribute. </p>
            @elseif($name == 'Manage Your Brands')
                <p class="mb-0"> Fill in the details below to add a new brand to your product catalog. </p>
            @elseif($name == 'Manage Languages')
                <p class="mb-0"> Enter the details to add a new language to your platform. </p>
            @elseif($name == 'Manage Locations')
                <p class="mb-0"> Fill in the details below to add a new location to your platform. </p>
            @elseif($name == 'Manage Stores')
                <p class="mb-0"> Enter the store details to expand your retail presence. </p>
            @elseif($name == 'Manage Vendors')
                <p class="mb-0"> Fill in the vendor details to expand your supplier network. </p>
            @else
                <p class="mb-0"> </p>
            @endif

        </div>

        <div class="col-6">
            @if ((request()->segment(2) == 'family' && request()->segment(3)) == 'create')
                <div class="d-flex justify-content-end">
                    <a href="{{ route('attributes.create') }}" class="btn btn-primary">Add Attribute</a>
                </div>
            @endif
        </div>

    </div>

</div>
