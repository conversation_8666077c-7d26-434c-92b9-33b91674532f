{{--
    @deprecated This file is deprecated and will be removed in the future.
--}}
<div class="col-6 col-md-7 col-lg-7 assign_formula" data-count="{{$row_count}}">
    <div class="row">
        @if($formula == "assign" || $formula == "slug")
            <div class="col-6 col-md-3 col-lg-3 p-0">
                <div class="form-group">
                    <label for="">Export Column</label>
                    @if(isset($row_node['to'][0]))
                        <input name="nodes[data][{{$row_count}}][to][]" style="width: 215px" class="form-control Poppins regular text-color export-column" value="{{$row_node['to'][0]}}">
                    @else
                        @if($row_count == 0)
                            <input name="nodes[data][{{$row_count}}][to][]" style="width: 215px" class="form-control Poppins regular text-color export-column" value="SKU">
                        @else
                            <input name="nodes[data][{{$row_count}}][to][]" style="width: 215px" class="form-control Poppins regular text-color export-column" value="">
                        @endif
                    @endif
                </div>
            </div>
            <div class="col-6 col-md-4 col-lg-4 p-0">

            </div>
            <div class="col-6 col-md-4 col-lg-4 p-0">

            </div>

        @elseif($formula == "split")
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">Separator</label>
                    @if(isset($row_node['with']))
                        <input type="text" name="nodes[data][{{$row_count}}][with]" value="{{$row_node['with']}}" style="width: 100%" class="form-control">
                    @else
                        <input type="text" name="nodes[data][{{$row_count}}][with]" style="width: 100%" class="form-control">
                    @endif
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">Export Column (1)</label>
                    @if(isset($row_node['to'][0]))
                        <input name="nodes[data][{{$row_count}}][to][]" style="width: 100%" class="form-control Poppins regular text-color export-column" value="{{$row_node['to'][0]}}">
                    @else
                        <input name="nodes[data][{{$row_count}}][to][]" style="width: 100%" class="form-control Poppins regular text-color export-column" value="">
                    @endif
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">Export Column (2)</label>

                    @if(isset($row_node['to'][1]))
                        <input name="nodes[data][{{$row_count}}][to][]" style="width: 100%" class="form-control Poppins regular text-color" value="{{$row_node['to'][1]}}">
                    @else
                        <input name="nodes[data][{{$row_count}}][to][]" style="width: 100%" class="form-control Poppins regular text-color" value="">
                    @endif
                </div>
            </div>

        @elseif($formula == "merge")
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">Glue</label>
                    @if(isset($row_node['with']))
                        <input type="text" name="nodes[data][{{$row_count}}][with]" value="{{$row_node['with']}}" style="width: 100%" class="form-control">
                    @else
                        <input type="text" name="nodes[data][{{$row_count}}][with]" style="width: 100%" class="form-control">
                    @endif

                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">Apimio column (2) </label>
                    <select name="nodes[data][{{$row_count}}][from][]" style="width: 182px"
                            class="form-control">
                        <option value="" class="Poppins regular text-color">Select Column</option>
                        <optgroup label="Default">
                            @foreach($apimio_attributes as $ap_attr_key => $ap_attr)
                                @if(isset($row_node['from'][1]))
                                    <option value="{{$ap_attr_key}}" {{($row_node['from'][1] == $ap_attr_key || $row_node['from'][1] == $ap_attr) ? 'selected' : null}} >{{$ap_attr}}</option>
                                @else
                                    <option value="{{$ap_attr_key}}">{{$ap_attr}}</option>
                                @endif
                            @endforeach
                        </optgroup>
                        @foreach($heading_attributes as $family)
                            <optgroup label="{{$family->name}}">
                                @foreach($family->attributes as $h_attribute)
                                    @if(isset($row_node['from'][1]))
                                        <option value="{{$h_attribute->pivotId}}" {{($row_node['from'][1] == $h_attribute->pivotId || $row_node['from'][1] == $h_attribute->handle) ? 'selected' : null}} >{{$h_attribute->name}}</option>
                                    @else
                                        <option value="{{$h_attribute->pivotId}}">{{$h_attribute->name}}</option>
                                    @endif
                                @endforeach
                            </optgroup>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">Export Column</label>
                    @if(isset($row_node['to'][0]))
                        <input name="nodes[data][{{$row_count}}][to][]" style="width: 100%" class="form-control Poppins regular text-color export-column" value="{{$row_node['to'][0]}}">
                    @else
                        <input name="nodes[data][{{$row_count}}][to][]" style="width: 100%" class="form-control Poppins regular text-color export-column" value="">
                    @endif
                </div>
            </div>

        @elseif($formula == 'replace')
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">Search</label>
                    @if(isset($row_node['replace']))
                        <input type="text" name="nodes[data][{{$row_count}}][replace]" value="{{$row_node['replace']}}" style="width: 100%" class="form-control">
                    @else
                        <input type="text" name="nodes[data][{{$row_count}}][replace]" style="width: 100%" class="form-control">
                    @endif
                </div>
            </div>
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">With </label>
                    @if(isset($row_node['with']))
                        <input type="text" name="nodes[data][{{$row_count}}][with]" value="{{$row_node['with']}}" style="width: 100%" class="form-control">

                    @else
                        <input type="text" name="nodes[data][{{$row_count}}][with]" style="width: 100%" class="form-control">
                    @endif
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">Export Column</label>
                    @if(isset($row_node['to'][0]))
                        <input name="nodes[data][{{$row_count}}][to][]" style="width: 100%" class="form-control Poppins regular text-color export-column" value="{{$row_node['to'][0]}}">
                    @else
                        <input name="nodes[data][{{$row_count}}][to][]" style="width: 100%" class="form-control Poppins regular text-color export-column" value="">
                    @endif
                </div>
            </div>

        @else
            <div class="col-lg-4">
                <b class="align-middle">Coming soon</b>
            </div>
        @endif

        @if($row_count != 0)
            <div class="col-6 col-md-1 col-lg-1 ml-auto">
                <div class="d-flex align-items-center pt-4 ml-auto float-right">
                    <a class="btn btn-delete" onclick="delete_row(this)">
                        <img src="{{asset('media/retailer-dashboard/delete.png')}}" alt="">
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
