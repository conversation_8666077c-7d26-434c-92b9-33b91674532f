<?php ?>
@extends('layouts.app_new')
@if(request()->has('id'))
    @section('titles','Edit Products')
@else
    @section('titles','Update Variants')
@endif
@section('content')
    @push('header_scripts')
        <style>
            .tox-statusbar {
                display: none !important;
            }
            .tox .tox-notification--in {
                display: none !important;
                opacity: 0 !important;
            }

            .filepond--item {
                width: calc(50% - 0.5em);
            }
            @media (min-width: 30em) {
                .filepond--item {
                    width: calc(50% - 0.5em);
                    width:200px;
                }
            }

            @media (min-width: 50em) {
                .filepond--item {
                    width: calc(33.33% - 0.5em);
                }
            }

            input[type="file"] {
                visibility: hidden;
            }
            .filepond_preview_bg_img {
                flex-shrink: 0;
                min-width: 100%;
                min-height: 100%
            }

            .filepond--file .filepond--file-action-button {
                position: absolute;
                left: 123px !important;
                top: -7px;
            }
            .filepond--file-info {
                z-index: 100;
                position: absolute;
                left: -152px;
                top: -4px;
            }
            [data-filepond-item-state*='error'] .filepond--item-panel,
            [data-filepond-item-state*='invalid'] .filepond--item-panel {
                background-color: red;
            }

            .image-uploader .uploaded .uploaded-image {
                display: inline-block;
                width: calc(16.6666667% - 1rem);
                padding-bottom: calc(16.6666667% - 1rem);
                min-height: 40px;
                max-height: 40px;
                min-width: 40px;
                max-width: 40px;
                position: relative;
                margin: 0rem !important;
                background: #f3f3f3;
                position:absolute;
                left:0px;
                cursor: default;
            }
            .image-uploader {
                min-height: 5.5rem !important;
                border: 1px solid #d9d9d9;
                position: relative;
            }
            .image-uploader .uploaded .uploaded-image img {
                width: 100%;
                height: 100%;
                object-fit: contain;
                position: absolute;
                left: 0px;
            }
            .image-uploader .upload-text i {
                display: block;
                font-size: 2rem !important;
                margin-bottom: 0.5rem;
            }
            .image-uploader .uploaded {
                padding: 0rem !important;
                line-height: 0;
            }
            .image-uploader .uploaded .uploaded-image .delete-image {
                display: none;
                cursor: pointer;
                position: absolute;
                top: 0rem !important;
                right: 0rem !important;
                border-radius: 50%;
                padding: 0.1rem !important;
                line-height: 1;
                background-color: rgba(0,0,0,.5);
                -webkit-appearance: none;
                border: none;
            }
            .image-uploader .uploaded .uploaded-image .delete-image i {
                display: block;
                color: #fff;
                width: 1.4rem;
                height: 1.4rem;
                font-size: 1rem !important;
                line-height: 1.4rem;
            }
            .image-uploader .upload-text span {
                display: none !important;
            }
            .image-uploader {
                min-height: 42px;
                border: 1px solid #fff !important;
                position: relative;
            }

            element.style {
            }
            .image-uploader.has-files .upload-text {
                display: none;
            }
            .image-uploader .upload-text {
                position: absolute;
                height: 42px;
                top: 0;
                right: 0;
                left: 0;
                bottom: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
            }
            .image-uploader .upload-text i {
                display: block;
                font-size: 1.5rem !important;
                margin-bottom: 0.5rem;
            }
            .variants-image-delete{
                position: absolute;
                right: 0px;
                width: 20px;
                height: 20px;
                border-radius: 50px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #000;
                color: #fff;
                opacity: 0;
            }
            .variants-image-delete:hover{
                opacity: 1;
            }

        </style>
        <link rel="stylesheet" href="{{asset('image-upload-library/image-uploader.min.css')}}">

    @endpush

    <x-products.edit-product-header :product="$product" :buttons="false" :version="$current_version"/>
    <x-products.edit-product-header-navs :product="$product" :version="$current_version"/>

    {{--  step 3 new code start  --}}
    <div class="row">
        <div class="col-12">
        @if($variants->count() > 0 && $variants->count() == 1)
        <div class="d-flex flex-column">
            <div>
                <!-- {{-- <h3 class="mb-0">{{trans('products_variants_step3.step3_title')}}</h3> --}} -->
                <!-- <p class="mb-0">{{trans('products_variants_step3.max_variants')}}</p> -->
                <p class="mb-0">Total variants: <b>{{$variants->count() == 1 ?
                        $variants->count() - 1 : $variants->count() }}</b></p>
                <!-- <p class="mb-0">All combinations of your variants are listed below. </p> -->
            </div>
            <div class="border rounded mt-3 p-3" style="width:70%">
                <div class="p-3 ">
                    <a href="{{ route('variants.step.one', ['id' => $product->id, 'version_id' => $current_version->id]) }}"
                        style="width:139px" class="mb-1">
                        + Add options like size or color
                    </a>
                </div>
            </div>
        </div>

        @else
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <!-- {{-- <h3 class="mb-0">{{trans('products_variants_step3.step3_title')}}</h3> --}} -->
                <!-- <p class="mb-0">{{trans('products_variants_step3.max_variants')}}</p> -->
                <p class="mb-0">{{trans('products_variants_step3.already_added_variants')}} <b>{{$variants->count() == 1 ?
                        $variants->count() - 1 : $variants->count() }}</b></p>
                <p class="mb-0">All combinations of your variants are listed below. </p>
            </div>
            <div class="gap-2">
                <a href="#" data-id="{{$product->id}}" data-version_id="{{$current_version->id}}" data-retailer-name=""
                    data-bs-toggle="modal" data-bs-target="#bulk-delete-modal-{{$product->id}}" class="btn btn-outline-danger me-2">
                    {{ trans('products_variants_step1.delete_all_variants') }}
                </a>
                <div class="modal fade" id="bulk-delete-modal-{{$product->id}}" tabindex="-1" aria-labelledby="deleteModalLabel"
                    aria-modal="true" role="dialog">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h3 class="modal-title" id="exampleModalLabel">Delete Product</h3>
                                <button type="button" class="btn-sm border-0 bg-white fs-24 px-0 pt-0" data-bs-dismiss="modal"
                                    aria-label="Close">
                                    <span aria-hidden="true">×</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <p class="mb-0 pb-3">Are you sure you want to delete all variants for this product?</p>
                                <div class="modal-footer pt-3">
                                    <button type="button" data-bs-dismiss="modal" id="bulkdelete-cancel-btn"
                                        class="btn btn-light float-left shadow-sm border">
                                        Cancel
                                    </button>
                                    <a href="{{ route('variants.all.delete', ['productId' => $product->id , 'version_id' => $current_version->id] ) }}"
                                        class="btn btn-danger">
                                        Delete All Variants
                                    </a>


                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- <x-assets.bulk-variant-modal :productId="$product->id" /> --}}
                <a href="{{ route('variants.step.one', ['id' => $product->id, 'version_id' => $current_version->id]) }}"
                    style="width:139px" class="btn btn-outline-primary">
                    Add option </a>
            </div>
        </div>
        @endif

            @if($variants->count() > 1)
            {{--  table start   --}}
            <form id="pro_var_add_var_form" method="post" action="{{route('variants.update',$id)}}" enctype="multipart/form-data" class="formStyle">
                @csrf
                @method('PUT')
                <input type="hidden" value="{{$current_version->id}}" name="version_id">
                <table class="table mt-3">
                    <thead>
                    @php
                        $count=0
                    @endphp
                    <tr>
                        <th scope="col">{{ __('Image') }}</th>
                        <th scope="col">{{ __('SKU') }}</th>
                        <th scope="col">{{ __('Name') }}</th>
                        {{-- <th scope="col">{{ __('Quantity') }}</th> --}}
                        <th scope="col">{{ __('UPC/EAN/ISBN') }}</th>
                        <th scope="col">{{ __('Price') }}</th>
                        <th scope="col">{{ __('Compare At Price') }}</th>
                        <th scope="col">{{ __('Cost Price') }}</th>
                        <th scope="col" style='min-width:180px;max-width:250px'>{{ __('Weight') }}</th>
                        <th scope="col" class="text-end">Action</th>
                    </tr>
                    </thead>
                    <tbody>
                    @forelse($variants as $key=>$variant)
                   {{--{{dd($variants);}} --}}

                    @if($variants->count() > 1)
                        <tr class="row_{{$variant->id}}">
                            <td>
                                @if($variant->file_id)
                                    <div class="img-div-height my-1 ">
                                        <div class="background-image-css position-relative"
                                             style="background-image: url('{{ isset($variant->file) ? $variant->file->link : asset("img/apimio_default.jpg")}}');">
                                             <a href="{{route('variants.image.delete',$variant->id)}}" onclick="return confirm('Are you sure you want to delete this Image?');">
                                                <span class="variants-image-delete">x</span>
                                            </a>
                                        </div>
                                    </div>
                                @else
                                    <div style="width:42px;height:42px">
                                        <div class="input-images-{{$key}}"></div>
                                    </div>
                                    @if ($errors->has('file.'.$count))
                                        <span class="text-danger"><small> {{ $errors->first('file.'.$count) }}</small></span>

                                    @endif
                                @endif
                            </td>
                            <td>
                                <input type="hidden" value="{{$variant->id}}" data-confirm-before-leave="true" name="id[]">
                                <input type="text"
                                class="form-control form-control-sm w-75 {{ $variant_settings['sku'] == '1' ? (old('sku.'.$count, $variant->sku) != '' ? 'border-success' : 'border-danger') : '' }}"
                                value="{{ old('sku.'.$count, $variant->sku) }}"
                                name="sku[]">
                                @if ($errors->has('sku.'.$count))
                                    <span class="text-danger"><small> {{ $errors->first('sku.'.$count) }}</small></span>

                                @endif
                            </td>
                            <td>
                                <input type="text" class="form-control form-control-sm w-75 {{ $variant_settings['name'] == '1' ? (old('name.'.$count, $variant->name) != '' ? 'border-success' : 'border-danger') : '' }}"  value="{{ old('name.'.$count, $variant->name) }}" data-confirm-before-leave="true" name="name[]">
                                @if ($errors->has('name.'.$count))
                                    <span class="text-danger"><small> {{ $errors->first('name.'.$count) }}</small></span>

                                @endif
                            </td>
                            {{-- <td>
                                <input type="number" min="0" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');" class="form-control form-control-sm w-75" value="{{ old('quantity.'.$count, $variant->quantity) }}" placeholder="e.g 10" name="quantity[]">
                                @if ($errors->has('quantity.'.$count))
                                    <span class="text-danger"><small> {{ $errors->first('quantity.'.$count) }}</small></span>

                                @endif
                            </td> --}}
                            <td>
                                <input type="text" class="form-control form-control-sm w-75 {{ $variant_settings['barcode'] == '1' ? (old('barcode.'.$count, $variant->barcode) != '' ? 'border-success' : 'border-danger') : '' }}" value="{{ old('barcode.'.$count,$variant->barcode)}}" data-confirm-before-leave="true" name="barcode[]">
                                @if ($errors->has('barcode.'.$count))
                                    <span class="text-danger"><small> {{ $errors->first('barcode.'.$count) }}</small></span>

                                @endif
                            </td>
                            <td>
                                <input type="text" min="0" oninput="this.value = this.value.replace(/[^0-9.]+|(?<=\..*)\./g, '');"  class="form-control form-control-sm w-75 {{ $variant_settings['price'] == '1' ? (old('price.'.$count, $variant->price) != '' ? 'border-success' : 'border-danger') : '' }}" value="{{ old('price.'.$count,$variant->price)}}" data-confirm-before-leave="true" name="price[]">
                                @if ($errors->has('price.'.$count))
                                    <span class="text-danger">
                                        <small> {{ $errors->first('price.'.$count) }}</small>
                                    </span>
                                @endif
                            </td>
                            <td>
                                <input type="text" min="0" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"  class="form-control form-control-sm w-75 {{ $variant_settings['compare_at_price'] == '1' ? (old('compare_at-price.'.$count, $variant->compare_at_price) != '' ? 'border-success' : 'border-danger') : '' }}" value="{{ old('compare_at_price.'.$count,$variant->compare_at_price)}}"  data-confirm-before-leave="true" name="compare_at_price[]">
                                @if ($errors->has('compare_at_price.'.$count))
                                    <span class="text-danger"><small> {{ $errors->first('compare_at_price.'.$count) }}</small></span>
                                @endif
                            </td>
                            <td>
                                <input type="text" min="0" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"  class="form-control form-control-sm w-75 {{ $variant_settings['cost_price'] == '1' ? (old('cost_price.'.$count, $variant->cost_price) != '' ? 'border-success' : 'border-danger') : '' }}" value="{{ old('cost_price.'.$count,$variant->cost_price)}}" data-confirm-before-leave="true" name="cost_price[]">
                                @if ($errors->has('cost_price.'.$count))
                                    <span class="text-danger"><small> {{ $errors->first('cost_price.'.$count) }}</small></span>

                                @endif
                            </td>
                            <td>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <select name="weight_unit[]"
                                                data-confirm-before-leave="true"
                                                class="form-control input-prepend-select pr-2 bg-white-smoke"
                                                style="width:75px;margin-right:-1px;height:36px">
                                            <option value="oz"
                                                {{(old('weight_unit.'.$count, $variant->weight_unit) =='oz')?'selected':''}}
                                            >
                                                {{__("oz")}}
                                            </option>
                                            <option value="lb"
                                                {{(old('weight_unit.'.$count, $variant->weight_unit) =='lb')?'selected':''}}
                                            >
                                                {{__("lb")}}
                                            </option>
                                            <option {{(old('weight_unit.'.$count, $variant->weight_unit) =='kg')?'selected':''}}
                                                    value="kg" >
                                                {{__("kg")}}
                                            </option>
                                            <option {{(old('weight_unit.'.$count, $variant->weight_unit) =='g')?'selected':''}}
                                                    value="g">
                                                {{__("g")}}
                                            </option>
                                        </select>
                                    </div>
                                    <input type="text" name="weight[]"
                                           value="{{ old('weight.'.$count, $variant->weight)}}"
                                           data-confirm-before-leave="true"
                                           oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                                           class="form-control form-control-sm  {{ $variant_settings['weight'] == '1' ? (old('weight.'.$count, $variant->weight) != '' ? 'border-success' : 'border-danger') : '' }}" style="height:36px">
                                </div>
                            </td>
                            <td class="text-end">
                                <a href="#" data-id="{{$variant->id}}" data-retailer-name=""
                                   data-bs-toggle="modal" data-bs-target="#delete-modal-{{$variant->id}}" class="delete-btn-image">
                                   <i class="fa-regular fa-trash-can fs-20 text-danger"></i>
                                </a>
                            </td>
                        </tr>
                    @endif
                        <x-assets.delete-modal text="Are you sure you want to delete variant?"
                                               button="Delete Variant" title="Delete Variant"
                                               id="{{$variant->id}}"
                                               url="{{route('variants.delete',$variant->id)}}" type="variant"/>
                        @php $count++ @endphp
                    @empty

                    @endforelse
                    </tbody>
                </table>
                @if(sizeof($variants) == 0)
                    <tr>
                        <p class="Roboto text-center">{{trans('products_variants_step1.empty_table_description')}}</p>
                    </tr>
                @endif
                @if(sizeof($variants) > 0)
                    <div class="col-12 mt-40 d-flex justify-content-end">
                        <button id="pro_var_add_var_btn" type="submit" value="Submit" class="btn btn-primary">
                            {{trans('products_variants_step3.save_btn')}}
                        </button>
                    </div>
                @endif
            </form>
            @else

            @endif
            {{-- table end   --}}
        </div>
    </div>
    {{--  step 3 new code end  --}}


@endsection
@push('footer_scripts')
    <script type="text/javascript" src="{{asset('image-upload-library/image-uploader.min.js')}}"></script>

    <script type="text/javascript">
        @error('name')
        $("#add-modal").modal("show");
        @enderror
    </script>



    <script src="{{asset('js/delete_product.js')}}" ></script>

    <script>

        $( document ).ready(function() {
            @foreach($variants as $key=>$v)
            $('.input-images-{{$key}}').imageUploader({
                imagesInputName: 'file[{{$key}}]',
                maxSize: 3 * 1024 * 1024,
                maxFiles: 1
            });
            $('input[type="file"]').removeAttr('multiple');
            @endforeach
        });


    </script>
@endpush





{{--Below code is old functionality of delete--}}


<!-- Modal DELETE-->
{{--    <div class="modal fade"--}}
{{--         id="delete-modal"--}}
{{--         tabindex="-1"--}}
{{--         aria-labelledby="deleteModalLabel"--}}
{{--         aria-hidden="true">--}}
{{--        <div class="modal-dialog modal-dialog-centered">--}}
{{--            <div class="modal-content">--}}
{{--                <div class="modal-header">--}}
{{--                    <h5 class="modal-title Poppins semibold"--}}
{{--                        id="exampleModalLabel">{{trans('variant.modal_title')}}</h5>--}}
{{--                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">--}}
{{--                        <span aria-hidden="true">&times;</span>--}}
{{--                    </button>--}}
{{--                </div>--}}
{{--                <div class="modal-body">--}}
{{--                    <p class="Roboto regular">{{trans('variant.modal_description')}}--}}
{{--                        --}}{{--                        <strong><span id="name">{{__('name')}}</span></strong>--}}
{{--                    </p>--}}

{{--                    <div class="modal-footer p-0">--}}
{{--                        <button type="button"--}}
{{--                                data-dismiss="modal"--}}
{{--                                id="delete-cancel-btn"--}}
{{--                                class="btn btn-dark-tertiary float-left shadow"--}}
{{--                                style="width: 120px;">--}}
{{--                            {{trans('variant.cancel_btn')}}--}}
{{--                        </button>--}}
{{--                        <form action="#" id="delete-brand" method="post">--}}
{{--                            @csrf--}}
{{--                            @method('DELETE')--}}
{{--                            <button id="pro_brand_del_btn"--}}
{{--                                    class=" btn btn-danger ripplelink shadow"--}}
{{--                                    onclick=deleteVariant({{$variant->id}})>--}}
{{--                                {{trans('variant.delete_btn')}}--}}
{{--                            </button>--}}
{{--                        </form>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--    </div>--}}

{{--    <script type="text/javascript">--}}
{{--        let attrid;--}}
{{--        let attrname;--}}

{{--        $(".btn-delete").click(function () {--}}
{{--            attrid = $(this).attr("data-id");--}}
{{--            attrname = $(this).attr("data-retailer-name");--}}
{{--            document.getElementById("name").innerHTML = attrname;--}}
{{--        });--}}

{{--        function deleteVariant(id)--}}
{{--        {--}}
{{--            $.ajax({--}}
{{--                url: "/products/variants/"+id,--}}
{{--                type: "DELETE",--}}
{{--                data: {"_token": "{{ csrf_token() }}",id: id},--}}
{{--                success: function(data){--}}
{{--                    console.log('here');--}}
{{--                }--}}
{{--            }).done(function(){--}}
{{--                console.log("Success.");--}}
{{--                $('.row_'+id).remove();--}}
{{--            }).fail(function(){--}}
{{--                console.log("An error has occurred.");--}}
{{--            }).always(function(){--}}
{{--                console.log("Complete.");--}}
{{--            });--}}
{{--        }--}}
{{--    </script>--}}
