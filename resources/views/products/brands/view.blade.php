<?php ?>
@extends('layouts.app_new')
@section('titles','Brands')
@section('content')
    <div>
        <x-products.page-title name="{{trans('products_brands.page_title')}}"
                               description="{{trans('products_brands.page_description')}}"
                               links="true"
                               button="true">
            <x-slot name="addbutton">
                <a href="{{route('brands.create')}}"
                   id="add-brands"
                   class="btn btn-primary float-lg-right float-md-right only-disabled">
                {{trans('products_brands.add_vendor_btn')}}
                </a>
            </x-slot>
        </x-products.page-title>
        <div class="row">
            <div class="col-12 col-md-6 col-xl-3">
                @if(count($data["brands"]) > 0)
                    <x-general.search-bar placeholder="{{trans('products_brands.search_placeholder')}}"/>
                @endif
            </div>
        </div>
                <div class="row mt-2">
                    <div class="col-12">
                        @if(count($data['brands']) > 0)
                                <table class="table">
                                    <caption style="visibility: hidden"></caption>
                                    <thead class="thead">
                                    <tr>
                                        <th scope="col">{{ __('Name') }}</th>
                                        <th class="text-end">{{ __('Actions') }}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($data['brands'] as $brand)
                                        <tr>
                                            <td>{{substr($brand->name,0,50)}}</td>
                                            <td class="text-end">
                                                <a href="{{route('brands.edit', $brand->id)}}"
                                                   class="pro_brand_edit mr-3 edit-btn text-decoration-none">
                                                    <i class="fa-regular fa-pen-to-square fs-20"></i>
                                                </a>
                                                <a href="#" data-id="{{$brand->id}}" data-retailer-name=""
                                                   data-bs-toggle="modal" data-bs-target="#delete-modal-{{$brand->id}}" class="btn-delete text-decoration-none">
                                                    <i class="fa-regular fa-trash-can fs-20 text-danger"></i>
                                                </a>
                                            </td>
                                            <x-assets.delete-modal id="{{$brand->id}}" text="Are you sure you want to delete this brand?" button="Delete Brand" title="Delete Brand" url="{{route('brands.destroy',$brand->id)}}" type="brand"/>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                                {!! $data['brands']->appends($request->all())->links() !!}
                        @else
                            <x-general.empty-page description="{{trans('products_brands.page_empty')}}"/>
                        @endif
                    </div>

        </div>
    </div>
@endsection
@push('footer_scripts')
    <script type="text/javascript">
        @error('name')
        $("#add-modal").modal("show");
        @enderror
    </script>
@endpush
