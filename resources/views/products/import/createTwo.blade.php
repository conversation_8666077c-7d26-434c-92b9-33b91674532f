<?php ?>
@extends('layouts.app_new')
@section('titles','Import CSV')
@section('content')
    @push('header_scripts')
        <style>
            .line {
                width: 80%;
                top: 24px;
                position: absolute;
                border-bottom: 1px solid #e9e6e6;
            }
        </style>
    @endpush
    <div>
        <x-products.page-title name="{{trans('products_import_step1.page_title')}}" description="{{trans('products_import_step1.page_description')}}" links="false" button="false" buttonname="null"/>
        <div class="card border-radius shadow-none mb-4">
            <div class="card-body p-0">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-around">
                            <div class="line"></div>
                            <!--Tick-->
                            <div id="greenOne" class="py-2 px-3">
                                <div class="row text-center">
                                    <div class="col">
                                        <span class="circle circle-success text-center">{{__("✔")}}</span>
                                        <p class="Roboto bold mt-3 mb-0" >{{trans('general.step1_title')}}</p>
                                        <p class="Roboto regular m-0" >{{trans('products_import_step1.step1_description')}}</p>
                                    </div>
                                </div>
                            </div>
                            <!--blue2-->
                            <div id="blueTwo" class="py-2 px-3">
                                <div class="row text-center">
                                    <div class="col">
                                        <span class="circle circle-primary text-center Roboto">{{__("2")}}</span>
                                        <p class="Roboto bold mt-3 mb-0" >{{trans('general.step2_title')}}</p>
                                        <p class="Roboto regular m-0" >{{trans('products_import_step1.step2_description')}}</p>
                                    </div>
                                </div>
                            </div>
                            <!--grey3-->
                            <div id="greyThree" class="py-2 px-3">
                                <div class="row text-center">
                                    <div class="col">
                                        <span class="circle circle-outline-primary text-center Roboto">{{__("3")}}</span>
                                        <p class="Roboto bold mt-3 mb-0" >{{trans('general.step3_title')}}</p>
                                        <p class="Roboto regular m-0" >{{trans('products_import_step1.step3_description')}}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>


        <form action="{{ route("import.csv.step3") }}" method="post">
            @csrf
            <input type="hidden" value="{{$path_temp}}" name="file_path">
            <div class="card border-radius shadow-none mt-4">
                <div class="card-body p-0">
                    <div class="row">
                        <div class="col-12 my-3">
                            <ul class="list-group list-group-horizontal-lg bg-light-grey-2">
                                <li class="list-group-item bg-light-grey-2">
                                    <div class="media">
                                        <img class="mt-1" src="{{asset('media/new-flow/tick.png')}}" alt="danger">
                                        <div class="media-body">
                                            <h5 class="m-0 p-1 Roboto regular">{{trans('products_import_step2.upload_csv_file')}}</h5>
                                        </div>
                                    </div>
                                </li>
                                <li class="list-group-item bg-light-grey-2">
                                    <div class="media">
                                        <img class="mt-1" src="{{asset('media/new-flow/tick.png')}}" alt="danger">
                                        <div class="media-body">
                                            @if($attributes_count>0)
                                                <h5 class="m-0 p-1 Roboto regular">
                                                    {{trans('products_import_step2.attributes_count', ['attributes_count'=> $attributes_count])}}
                                                </h5>
                                            @endif
                                        </div>
                                    </div>
                                </li>
                                <li class="list-group-item bg-light-grey-2">
                                    <div class="media">
                                        <img class="mt-1" src="{{asset('media/new-flow/tick.png')}}" alt="danger">
                                        <div class="media-body">
                                            @if($attributes_count>0)
                                                <h5 class="m-0 p-1 Roboto regular">
                                                    {{trans('products_import_step2.rows_count', ['rows_count'=> $rows_count])}}
                                                </h5>
                                            @endif
                                        </div>
                                    </div>
                                </li>
                                <li class="list-group-item bg-light-grey-2">
                                    <div class="media">
                                        @if($duplicates != 0)
                                            <img src="{{asset('media/new-flow/linear-danger.png')}}" alt="save">
                                            <div class="media-body">
                                                <h5 class="m-0 p-1 Roboto regular">{{trans('products_import_step2.duplicate_attributes_found')}}</h5>
                                            </div>
                                        @else
                                            <img class="mt-1" src="{{asset('media/new-flow/tick.png')}}" alt="save">
                                            <div class="media-body">
                                                <h5 class="m-0 p-1 Roboto regular">{{trans('products_import_step2.no_duplicate_attributes_found')}}</h5>
                                            </div>
                                        @endif

                                    </div>
                                </li>

                            </ul>
                        </div>
                    </div>

                </div>
            </div>

            {{--display data of first five rows--}}
            <div class="card border-radius shadow-none mb-4">
                <div class="card-body p-0">
                    <div class="row">
                        <div class="col-12">
                            <table class="table table-responsive table-striped ">
                                <thead class="text-dark ">
                                <tr>
                                    @foreach($first_five_rows[0] as $keys => $head)
                                        <th>{{$head}}</th>
                                    @endforeach
                                </tr>
                                </thead>
                                <tbody>
                                @php array_shift($first_five_rows) @endphp
                                @foreach($first_five_rows as $keys => $values )
                                    <tr>
                                        @foreach($values as $value)
                                            <td style="overflow-x: auto;white-space: nowrap;max-width: 190px">{{$value}}</td>
                                        @endforeach

                                    </tr>
                                @endforeach
                                </tbody>
                            </table>

                        </div>
                    </div>

                </div>
            </div>

            <div class="row">
                <div class="col-12" >
                    <div class="d-flex flex-row-reverse mt-5">
                        <div class="p-2" style="width: 159px">
                            <button type="submit" id="next-btn"
                               class="form-control btn btn-primary ripplelink">
                                {{trans('products_import_step1.next_btn')}}
                            </button>
                        </div>
                        <div class="p-2" style="width: 159px">
                            <a href="{{route('products.index')}}" id="cancel-btn"
                                    class="form-control btn btn-outline-primary hovereffect ripplelink">
                                {{trans('products_import_step1.cancel_btn')}}

                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>

    </div>
@endsection
@push('footer_scripts')
    <script>
    </script>
@endpush
