<?php ?>
@extends('layouts.app_new')
@section('titles','Import CSV')
@section('content')
    @push('header_scripts')

        <link href="https://unpkg.com/filepond@^4/dist/filepond.css" rel="stylesheet"/>
        <link href="https://unpkg.com/filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css"
              rel="stylesheet">
        <style>
            /*css for footer buttons*/
            .file_viewer_td {
                /*overflow-x: auto;*/
                white-space: nowrap;
                min-width: 120px;
                max-width: 190px;
                padding-right: 30px;
            }

            input[type="file"] {
                position: absolute;
                width: 1px;
                height: 1px;
                padding: 0;
                margin: -1px;
                overflow: hidden;
                clip: rect(0, 0, 0, 0);
                border: 0;
            }
            .info-css {
                font-size: 14px;
                color: #1e1c1c
            }

            .icon-css {
                margin-bottom: 10px;
                margin-left: 5px;
            }

            .guide a {
                color: #2c4bffbd;
                font-weight: 500;
            }

            .guide a:hover {
                color: #2c4bffbd;
                font-weight: 500;
                text-decoration: underline;
            }

            .tooltip-inner {
                max-width: 500px;
                width: 500px;

            }

            #file_box_hover:hover, .fileHover {
                background: rgba(0, 0, 0, 0.03);
            }

            input[type="radio"]:checked + label{  /*select all parent divs where radio button is checked*/
                border:1px solid #2c4bff78;
                background: #f2f2f387;
            }
            .import_input_css{
                position: absolute;
                top: 10px;
                right: 16px;
            }
            .list-group-item {
                position: relative;
                display: block;
                padding: 1.3rem 0rem 0rem 0.5rem;
                color: #212529;
                text-decoration: none;
                background-color: #fff;
                 border: none;
            }
            @media screen and (min-width: 1400px) and (max-width: 1700px) {
                .table-style-css{
                    max-width: calc(100vw - 21.5vw);
                }
            }
            @media screen and (min-width: 2000px) {
                .table-style-css{
                    max-width: calc(100vw - 15.1vw);
                }
            }
            @media screen and (min-width: 2300px) {
                .table-style-css{
                    max-width: calc(100vw - 11.1vw);
                }
            }
            @media screen and (min-width: 3000px) {
                .table-style-css{
                    max-width: calc(100vw - 8.1vw);
                }
            }
        </style>
    @endpush

        <x-products.page-title name="{{trans('products_import_step1.page_title')}}"
                               description="{{trans('products_import_step1.page_description')}}" links="false"
                               button="false" buttonname="null"/>
        <form id="form_upload_step1" action="{{ route("import.csv.step3") }}" method="post"
              enctype="multipart/form-data" accept-charset="UTF-8">
            @csrf
           <div class="row">
               <div class="col-12 col-lg-12">
                   <div class="border-radius shadow-none mb-4">
                       <div class="card-body p-0">
                           <div class="row">
                               <div class="col-lg-12 col-xl-8 col-xxl-6  mx-auto" id="csv-file">
                                   <div>
                                           @foreach ($errors->all() as $error)

                                               <span class="text-danger" role="alert">
                                                <small>{{ $error }}</small>
                                            </span>
                                               {{--                                            <p class="mb-0 text-danger">{{ $error }}</p>--}}
                                           @endforeach

                                           <div class="box w-100" style="border: 2px dashed #A5A5A5;cursor: pointer!important;"
                                                id="file_box_hover"
                                                ondragover="overrideDefault(event); fileHover()"
                                                ondragenter="overrideDefault(event); fileHover()"
                                                ondragleave="overrideDefault(event); fileHoverEnd()"
                                                ondrop="overrideDefault(event); addFiles(event); fileHoverEnd()"
                                                onclick="document.getElementById('file').click();">
                                               <div class="box__input text-center">
                                                   <img style="margin-bottom: 8px;" src="{{asset('media/new-flow/csv.png')}}"
                                                        alt="">
                                                   <br>
                                                   <input accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" type="file" name="file" id="file"
                                                          style="cursor: pointer!important" required/>
                                                   <label class="d-flex flex-column">
                                                    <span class="text-decoration-none fs-20 fw-700"
                                                          style="cursor: pointer!important;"
                                                          tabindex="0">
                                                        {{trans('products_import_step1.click_to_browse_btn')}}
                                                    </span>
                                                       <span class="regular text-grey"
                                                             style="text-decoration: none;cursor: pointer!important;"
                                                             tabindex="0">
                                                        {{trans('products_import_step1.click_to_browse_btn_text')}}
                                                    </span>
                                                   </label>
                                               </div>

                                           </div>
                                   </div>
                                   <div class="row guide mt-4">
                                       <div class="col-lg-6 mb-2 d-flex">
                                           <div class="icon-css">
                                               <svg width="32" height="33" viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path d="M29.3307 13.8337V20.5003C29.3307 27.167 26.6641 29.8337 19.9974 29.8337H11.9974C5.33073 29.8337 2.66406 27.167 2.66406 20.5003V12.5003C2.66406 5.83366 5.33073 3.16699 11.9974 3.16699H18.6641" stroke="#A5A5A5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                   <path d="M29.3307 13.8337H23.9974C19.9974 13.8337 18.6641 12.5003 18.6641 8.50033V3.16699L29.3307 13.8337Z" stroke="#A5A5A5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                   <path d="M9.33594 17.833H17.3359" stroke="#A5A5A5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                   <path d="M9.33594 23.167H14.6693" stroke="#A5A5A5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                               </svg>
                                           </div>

                                           <div class="d-flex flex-column ms-2">
                                               <a href="#" class="mt-0 ms-1 text-decoration-none clr-black">How to prepare your spreadsheet <sup><i class="fa fa-external-link" aria-hidden="true"></i></sup></a>
                                               <p class="mt-0 ms-1 mb-2 info-css">This guide will show you all the requirements and recommendations for successful import</p>
                                           </div>
                                       </div>
                                       <div class="col-lg-6 d-flex">
                                           <div class="icon-css">
                                               <svg width="32" height="33" viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path d="M12 15.167V23.167L14.6667 20.5003" stroke="#A5A5A5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                   <path d="M12.0026 23.1667L9.33594 20.5" stroke="#A5A5A5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                   <path d="M29.3307 13.8337V20.5003C29.3307 27.167 26.6641 29.8337 19.9974 29.8337H11.9974C5.33073 29.8337 2.66406 27.167 2.66406 20.5003V12.5003C2.66406 5.83366 5.33073 3.16699 11.9974 3.16699H18.6641" stroke="#A5A5A5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                   <path d="M29.3307 13.8337H23.9974C19.9974 13.8337 18.6641 12.5003 18.6641 8.50033V3.16699L29.3307 13.8337Z" stroke="#A5A5A5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                               </svg>
                                           </div>
                                           <div class="d-flex flex-column ms-2">
                                               <a href="{{ asset('csv_template/apimio_default.csv') }}" class="mt-0 ms-1 mb-0 text-decoration-none">Download
                                                   a Sample CSV</a>
                                               <p class="mt-0 ms-1 mb-2 info-css">Try it out using a sample csv file to see how it works</p>
                                           </div>
                                       </div>


                                   </div>
                               </div>
                               <div id="loaderId" class=""></div>
                               <div class="col-12 d-none" id="csv-table-data" style="margin-bottom: 100px">
                                   @if(\App\Models\Product\Product::count() > 0)
                                       <h3 class="mb-0 mt-0">Import Products</h3>
                                       <div class="d-flex flex-column flex-md-row align-items-start align-content-md-center mt-2">
                                       <div class="position-relative mt-3 mt-md-0 ms-3 ms-md-0">
                                               <input name="import_action" type="radio" value="3" checked id="opt3" class="import_input_css"/>
                                               <label class="custom-options-css px-3 py-2" for="opt3">
                                                   <div class="d-flex justify-content-between align-items-center">
                                                <span class="d-inline-flex align-items-center">
                                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                                         xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M19.625 5.1875C19.625 2.53418 17.4662 0.375 14.8125 0.375C12.8705 0.375 11.1976 1.53365 10.4378 3.19386L8.90933 2.49927C8.72873 2.41736 8.52127 2.41736 8.34067 2.49927L0.778168 5.93677C0.53244 6.04822 0.375 6.2926 0.375 6.5625V15.5C0.375 15.7699 0.53244 16.0143 0.778168 16.1257L8.34067 19.5632C8.43097 19.6042 8.52798 19.625 8.625 19.625C8.72202 19.625 8.81903 19.6042 8.90933 19.5632L16.4718 16.1257C16.7176 16.0143 16.875 15.7699 16.875 15.5V9.52898C18.498 8.75458 19.625 7.10217 19.625 5.1875ZM8.625 3.88031L10.0506 4.5279C10.0208 4.74413 10 4.96317 10 5.1875C10 6.30599 10.3869 7.33401 11.0295 8.15213L8.625 9.24469L2.72385 6.5625L8.625 3.88031ZM1.75 7.62996L7.9375 10.4425V17.8697L1.75 15.0576V7.62996ZM15.5 15.0576L9.3125 17.8697V10.4425L12.1137 9.16941C12.8839 9.69318 13.8129 10 14.8125 10C15.0465 10 15.2748 9.97751 15.5 9.94507V15.0576ZM14.8125 8.625C12.9172 8.625 11.375 7.08282 11.375 5.1875C11.375 3.29218 12.9172 1.75 14.8125 1.75C16.7078 1.75 18.25 3.29218 18.25 5.1875C18.25 7.08282 16.7078 8.625 14.8125 8.625Z" fill="black"/>
                                                    </svg>
                                                    &nbsp; Import New Products Only</span>
                                                   </div>
                                                   <div>
                                                       <p class="cards-description-text mt-2">This option will ignore existing SKUs and will import new SKUs only.</p>
                                                   </div>
                                               </label>
                                           </div>
                                           
                                           <div class="position-relative ms-3 ms-xl-4 mt-3 mt-md-0">
                                               <input name="import_action" type="radio" value="2" id="opt2" class="import_input_css"/>
                                               <label class="custom-options-css px-3 py-2" for="opt2">
                                                   <div class="d-flex justify-content-between align-items-center">
                                                <span class="d-inline-flex align-items-center"><img
                                                        src="{{asset('img/update_icon_img.svg')}}" alt="icon">&nbsp; Update Existing Products</span>
                                                   </div>
                                                   <div>
                                                       <p class="cards-description-text mt-2">This option will update only existing SKUs and ignore new SKUs only.</p>
                                                   </div>
                                               </label>
                                           </div>
                                           <div class="position-relative ms-3 ms-xl-4 mt-3 mt-md-0">
                                               <input name="import_action" value="1" type="radio"  id="opt1" class="import_input_css"/>
                                               <label class="custom-options-css px-3 py-2" for="opt1">
                                                   <div class="d-flex justify-content-between align-items-center">
                                              <span class="d-inline-flex align-items-center">
                                                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                                       xmlns="http://www.w3.org/2000/svg">
                                                       <path fill-rule="evenodd" clip-rule="evenodd"
                                                             d="M18.195 9.02408C18.6212 12.5991 16.6536 16.1693 13.1666 17.6185C12.5348 17.8805 11.8892 18.0585 11.2416 18.1575C10.8662 18.2146 10.6084 18.5659 10.6655 18.9406C10.7226 19.316 11.0739 19.5738 11.4486 19.5167C12.2041 19.4019 12.9576 19.1943 13.6946 18.8883C17.7626 17.1978 20.0581 13.0322 19.5604 8.86114C19.5157 8.48439 19.1733 8.21489 18.7966 8.26027C18.4198 8.30495 18.1503 8.64733 18.195 9.02408V9.02408ZM8.71436 18.1486C5.97743 17.7127 3.52305 15.9094 2.38249 13.1649C1.81255 11.7926 1.64068 10.3537 1.81461 8.97733C1.86205 8.60058 1.5953 8.25614 1.21855 8.2087C0.84249 8.16127 0.498052 8.4287 0.450615 8.80477C0.247802 10.4115 0.447865 12.091 1.11268 13.6929C2.44299 16.8939 5.30574 18.9976 8.4978 19.5064C8.87249 19.5662 9.22518 19.3105 9.28499 18.9358C9.3448 18.5611 9.08905 18.2084 8.71436 18.1486ZM7.93886 5.87464V5.18714C7.93886 3.69733 8.9873 2.98439 10.0014 2.98439C11.0154 2.98439 12.0639 3.69733 12.0639 5.18714V5.87464H12.1739C13.2464 5.87464 14.1401 6.69689 14.2288 7.76595L14.6874 13.266C14.7355 13.8407 14.5402 14.4093 14.1497 14.8335C13.7592 15.2583 13.2086 15.4996 12.6317 15.4996H7.37099C6.79418 15.4996 6.24349 15.2583 5.85299 14.8335C5.46249 14.4093 5.26724 13.8407 5.31536 13.266L5.77393 7.76595C5.86261 6.69689 6.75636 5.87464 7.82886 5.87464H7.93886ZM7.93886 7.24964V7.93714C7.93886 8.94777 9.31386 8.94777 9.31386 7.93714V7.24964H10.6889V7.93714C10.6889 8.94433 12.0639 8.94433 12.0639 7.93714V7.24964H12.1739C12.5314 7.24964 12.8291 7.52395 12.8586 7.88008L13.3172 13.3801C13.333 13.5719 13.2684 13.761 13.1377 13.9026C13.0078 14.0442 12.8242 14.1246 12.6317 14.1246H7.37099C7.17849 14.1246 6.99493 14.0442 6.86499 13.9026C6.73436 13.761 6.66974 13.5719 6.68555 13.3801L7.14411 7.88008C7.17368 7.52395 7.47136 7.24964 7.82886 7.24964H7.93886ZM6.30811 1.11095C3.98505 2.07689 2.23949 3.84995 1.26255 5.9702C1.10374 6.31464 1.2543 6.72302 1.59943 6.88183C1.94386 7.04064 2.35224 6.89008 2.51105 6.54495C3.34843 4.72789 4.84443 3.20852 6.83611 2.38077L6.93305 2.34158C11.0319 0.697767 15.6973 2.58358 17.5137 6.58895C17.6697 6.93408 18.0781 7.08808 18.4232 6.93133C18.7691 6.77458 18.9224 6.36689 18.7656 6.02108C16.6302 1.3117 11.1179 -0.886921 6.30811 1.11095V1.11095ZM10.6889 5.87464H9.31386V5.18714C9.31386 4.65639 9.63974 4.35939 10.0014 4.35939C10.363 4.35939 10.6889 4.65639 10.6889 5.18714V5.87464Z"
                                                             fill="black"/>
                                                    </svg>
                                                  &nbsp;  New and Update Existing Products </span>

                                                   </div>
                                                   <div>
                                                       <p class="cards-description-text mt-2">This option will update existing SKUs and will create new SKUs.</p>
                                                   </div>
                                               </label>
                                           </div>

                                       </div>
                                   @endif
                                   <div class="row">
                                       <div class="col-12 mt-40 mb-3">
                                           <div class="bg-light-grey-2 d-flex flex-column flex-md-row justify-content-between">
                                               <div class="d-flex  align-items-center pl-4">
                                                   <h3 class="mb-0 d-inline-flex">
                                                       <span id="csv-file-name" class="me-1"></span> (<span id="file-size"></span>kb)</h3>
                                               </div>
                                               <ul class="list-group list-group-horizontal-sm">

                                                   <li class="list-group-item bg-light-grey-2">
                                                       <div class="media" style="align-items: center;">
                                                           {{--                                                            <img src="{{asset('img/circle-tick.svg')}}" alt="danger">--}}
                                                           <div class="media-body p-1">
                                                               <span id="columns-found"></span>
                                                           </div>
                                                       </div>
                                                   </li>
                                                   <li class="list-group-item bg-light-grey-2">
                                                       <div class="media" style="align-items: center;">
                                                           <div class="media-body p-1">
                                                               <span id="rows-found"></span>
                                                           </div>
                                                       </div>
                                                   </li>
                                                   <li class="list-group-item bg-light-grey-2">
                                                       <div class="media" style="align-items: center;">
                                                           <div id="duplicate_yes" style="display: none">
                                                               <img src="{{asset('media/new-flow/linear-danger.png')}}"
                                                                    style="width:22px;height:22px" alt="save">
                                                               <div class="media-body d-inline-block p-1">
                                                                   <span id="rows-found">{{trans('products_import_step2.duplicate_attributes_found')}}</span>
                                                               </div>
                                                           </div>
                                                           <div id="duplicate_no" style="display: none">
                                                               <img src="{{asset('img/circle-tick.svg')}}" alt="save">
                                                               <div class="media-body d-inline-block py-1">
                                                                    <span id="rows-found"
                                                                          class=" text-capitalize">{{trans('products_import_step2.no_duplicate_attributes_found')}}</span>
                                                               </div>
                                                           </div>
                                                       </div>
                                                   </li>

                                               </ul>
                                           </div>
                                       </div>
                                   </div>

                                 <div class="row">
                                     <div class="col-12">
                                         <div class="border-radius shadow-none mb-4">
                                             <div class="table-responsive table-style-css">
                                                 <table class="table table-bordered horizontal-scroll mb-2" id="file_preview_table">
                                                     <thead class="text-dark ">
                                                     <th class="custom-border-bottom-css text-center">
                                                     </th>
                                                     </thead>
                                                     <tbody>
                                                     </tbody>
                                                 </table>
                                                 <div>
                                                 </div>
                                             </div>
                                             <span id="file_row_preview_count" class="h3 mt-3"></span>
                                             <div class="row mapping_footer">

                                                 <div class="col-lg-12 text-right ml-auto">
                                                     <div class="row">
                                                         <div class="col-lg-12 ">
                                                             <div class="d-flex flex-row-reverse mt-4">
                                                                 <div class="p-2 d-flex justify-content-between w-100">
                                                                     <a href=""
                                                                        class="btn btn-primary">
                                                                         Re-Upload file
                                                                     </a>
                                                                     <button type="button" id="next-btn"
                                                                             class="btn btn-primary">
                                                                         {{trans('products_import_step1.next_btn')}}
                                                                     </button>
                                                                 </div>
                                                             </div>
                                                         </div>

                                                     </div>

                                                 </div>
                                             </div>

                                         </div>
                                     </div>
                                 </div>
                               </div>
                           </div>
                       </div>
                   </div>
               </div>
           </div>
        </form>



    @push('footer_scripts')
        <script src="https://unpkg.com/filepond@^4/dist/filepond.js"></script>
        <script src="https://unpkg.com/filepond-plugin-image-preview/dist/filepond-plugin-image-preview.js"></script>
        <script
            src="https://unpkg.com/filepond-plugin-image-exif-orientation/dist/filepond-plugin-image-exif-orientation.js"></script>
        {{-- <script src="https://cdn.tiny.cloud/1/oh0o94omie4zyuas0tk96qk319s6ltqf8eq9er20jhn3d7r7/tinymce/5/tinymce.min.js"
                referrerpolicy="origin"></script> --}}
        <script>

            // feature detection for drag&drop upload
            let isAdvancedUpload = function () {
                let div = document.createElement('div');
                return (('draggable' in div) || ('ondragstart' in div && 'ondrop' in div)) && 'FormData' in window && 'FileReader' in window;
            }();

            // applying the effect for every form
            let forms = document.querySelectorAll('.box');
            Array.prototype.forEach.call(forms, function (form) {
                let input = form.querySelector('input[type="file"]'),
                    label = form.querySelector('label'),
                    errorMsg = form.querySelector('.box__error span'),
                    restart = form.querySelectorAll('.box__restart'),
                    droppedFiles = false,
                    showFiles = function (files) {
                        label.textContent = files.length > 1 ? (input.getAttribute('data-multiple-caption') || '').replace('{count}', files.length) : files[0].name;
                    },
                    triggerFormSubmit = function () {
                        let event = document.createEvent('HTMLEvents');
                        event.initEvent('submit', true, false);
                        form.dispatchEvent(event);
                    };

                // letting the server side to know we are going to make an Ajax request
                let ajaxFlag = document.createElement('input');
                ajaxFlag.setAttribute('type', 'hidden');
                ajaxFlag.setAttribute('name', 'ajax');
                ajaxFlag.setAttribute('value', 1);
                form.appendChild(ajaxFlag);

                // automatically submit the form on file select
                input.addEventListener('change', function (e) {
                    showFiles(e.target.files);
                });

                // if the form was submitted
                form.addEventListener('submit', function (e) {
                    // preventing the duplicate submissions if the current one is in progress
                    if (form.classList.contains('is-uploading')) return false;

                    form.classList.add('is-uploading');
                    form.classList.remove('is-error');
                });


                // restart the form if has a state of error/success
                Array.prototype.forEach.call(restart, function (entry) {
                    entry.addEventListener('click', function (e) {
                        e.preventDefault();
                        form.classList.remove('is-error', 'is-success');
                        input.click();
                    });
                });

                // Firefox focus bug fix for file input
                input.addEventListener('focus', function () {
                    input.classList.add('has-focus');
                });
                input.addEventListener('blur', function () {
                    input.classList.remove('has-focus');
                });

            });
        </script>
        <script>

        </script>

        <script>
            var droppedFiles,file_data;


            function overrideDefault(event) {
                event.preventDefault();
                event.stopPropagation();
            }

            function addFiles(event) {
                droppedFiles = event.target.files || event.dataTransfer.files;

                document.getElementById("file").files = droppedFiles;
                console.log(droppedFiles);
                $('#file').trigger('input');
                console.log("add file event");
            }

            function fileHover() {
                $("#file_box_hover").addClass("fileHover");
            }

            function fileHoverEnd() {
                $("#file_box_hover").removeClass("fileHover");
            }

            $('#file').on('input', function (e) {
                var filepath = this.value;
                var m = filepath.match(/([^\/\\]+)$/);
                var filename = m[1];
                $('#csv-file').addClass('d-none');
                $('#csv-table-data').removeClass('d-none');
                $('#csv-table-data').addClass('d-block');
                const kbsize = e.target?.files[0].size / 1024;
                const kbsizeto = kbsize.toFixed(1);
                $("#file-size").text(kbsizeto);
                $('#csv-file-name').text(filename)
                fetch_file_rows();
            });

            /* LARAVEL META CSRF REQUIREMENT */
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            function fetch_file_rows() {
                var form = $('#form_upload_step1')[0];
                var formData = new FormData(form);
                var element = document.getElementById("loaderId");
                element.classList.add("loading");

                $.ajax({
                    url: "{{ route("import.csv.step2") }}",
                    method: "POST",
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (data) {
                        console.log(data);
                        file_data=data;
                        var element = document.getElementById("loaderId");
                        setTimeout(function () {
                            element.classList.remove("loading");
                        }, 1000);


                        // for columns checker
                        let columns_found = '';
                        if(typeof (data['attributes_count']) != "undefined"){

                            columns_found = `<img src="{{asset('img/circle-tick.svg')}}" alt="danger"> ` + data['attributes_count'];
                        }
                        else{
                            columns_found = '<img src="{{asset('img/circle-tick.svg')}}" alt="danger"> ' + '0';
                        }
                        if(data['attributes_count'] > 1){
                            columns_found = columns_found + ` Columns Found`;
                        }
                        else{
                            columns_found = columns_found + ` Column Found`;
                        }
                        $('#columns-found').html( columns_found);


                        // for rows checker
                        let rows_found = '';
                        if(typeof (data['rows_count']) != "undefined"){

                            rows_found = `<img src="{{asset('img/circle-tick.svg')}}" alt="danger"> ` + data['rows_count'];
                        }
                        else{
                            rows_found = '<img src="{{asset('img/circle-tick.svg')}}" alt="danger"> ' + '0';
                        }
                        if(data['rows_count'] > 1){
                            rows_found = rows_found + ` Rows Found`;
                        }
                        else{
                            rows_found = rows_found + ` Row Found`;
                        }

                        $('#rows-found').html(rows_found);



                        if(data['rows_count'] > 6){
                            $('#file_row_preview_count').html(`Preview of <b>5</b> out of <b>${data['rows_count']}</b> rows from CSV`);
                        }
                        else{
                            $('#file_row_preview_count').html(`Preview of <b>${data['rows_count']}</b> rows from CSV`);
                        }



                        if (typeof (data['duplicates']) != "undefined") {
                            if (data['duplicates'] != 0) {
                                $('#duplicate_yes').show();
                            } else {
                                $('#duplicate_no').show();
                            }
                        }
                        let columns = data['first_five_rows'][0];
                        data['first_five_rows'].shift();
                        let rows = data['first_five_rows'];
                        $("#file_preview_table thead tr").html("");
                        columns.forEach(function (index) {
                            var column_th = $("<th class='text-center'></th>");
                            column_th.text(index);
                            $("#file_preview_table thead tr").append(column_th);
                        });
                        rows.forEach(function (index) {
                            var row_tr = $("<tr class='text-center'></tr>");
                            index.forEach(function (td) {
                                if(td?.length>23){
                                    var row_td = $(`<td class='file_viewer_td text-truncate' data-toggle='tooltip' data-placement='bottom' title='${td}' ></td>`);
                                } else {
                                    var row_td = $(`<td class='file_viewer_td'></td>`);
                                }
                                row_td.text(td);
                                row_tr.append(row_td);
                            })
                            $("#file_preview_table tbody").append(row_tr);
                        });

                    },
                    error: function (error) {
                        console.log(error);
                    }
                });
            }

        </script>
{{--        <script>--}}
{{--            const element = document.querySelector(".horizontal-scroll");--}}
{{--            element.addEventListener('wheel', (event) => {--}}
{{--                event.preventDefault();--}}
{{--                element.scrollBy({--}}
{{--                    left: event.deltaY < 0 ? -30 : 30,--}}
{{--                });--}}
{{--            });--}}
{{--        </script>--}}
        <script>
            $( document ).ready(function() {
                $("#next-btn").click(function(event){
                    event.preventDefault();
                    if (file_data['rows_count'] > file_data['product_limit']){
                        // 1 = new and update | 2 = update only | 3 = add new only
                        if ($('input[name="import_action"]:checked').val() != 2){
                            if (file_data['subscribed_plan'].length > 0){
                                $("#billing_plan_name").text(file_data['subscribed_plan'][0].name);
                            }
                            else{
                                $("#billing_plan_name").text('Free Plan');
                            }
                            $(".import_remaining_product_limit").text(file_data['product_limit']);
                            $("#import_alert").modal('show');
                        }
                        else{
                            $('#form_upload_step1').submit();
                        }
                    }
                    else{
                        $('#form_upload_step1').submit();
                    }
                });

                $("#import_proceed").click(function(event){
                    event.preventDefault();
                    $('#form_upload_step1').submit();
                });



            });

        </script>
        {{--        <script>--}}
        {{--            $( document ).ready(function() {--}}
        {{--                $('input[type=radio]').on('change', function() {--}}
        {{--                    console.log($(this).val())--}}
        {{--                $('input[type=radio]').each(function() {--}}

        {{--                        if(this.val()==1){--}}
        {{--                            $(this).parent().parent().css('border','1px solid #2c4bff78');--}}
        {{--                            $(this).parent().parent().css('background','#f2f2f387');--}}
        {{--                        }else if(this.val()==2){--}}
        {{--                            $(this).parent().parent().css('border','1px solid #E2E2E3');--}}
        {{--                            $(this).parent().parent().css('background','');--}}
        {{--                        }--}}
        {{--                    });--}}
        {{--                });--}}

        {{--            });--}}
        {{--        </script>--}}
    @endpush
@endsection
