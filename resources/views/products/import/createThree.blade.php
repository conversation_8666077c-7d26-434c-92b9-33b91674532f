<?php ?>
@extends('layouts.app_new')
@section('titles','Import CSV')
@section('content')
    @push('header_scripts')
        <style>
            .line {
                width: 80%;
                top: 24px;
                position: absolute;
                border-bottom: 1px solid #e9e6e6;
            }
        </style>
    @endpush
    <div>
        <x-products.page-title name="{{trans('products_import_step1.page_title')}}" description="{{trans('products_import_step1.page_description')}}" links="false"
                               button="false" buttonname="null"/>

        <div class="card border-radius shadow-none mb-4">
            <div class="card-body p-0">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-around">
                            <div class="line"></div>
                            <!--Tick-->
                            <div id="greenOne" class="py-2 px-3">
                                <div class="row text-center">
                                    <div class="col">
                                        <span class="circle circle-success text-center Roboto ">{{__("✔")}}</span>
                                        <p class="Roboto bold mt-3 mb-0">{{trans('general.step1_title')}}</p>
                                        <p class="Roboto regular m-0">{{trans('products_import_step1.step1_description')}}</p>
                                    </div>
                                </div>
                            </div>
                            <!--blue2-->
                            <div id="blueTwo" class="py-2 px-3">
                                <div class="row text-center">
                                    <div class="col">
                                        <span class="circle circle-success text-center Roboto">{{__("✔")}}</span>
                                        <p class="Roboto bold mt-3 mb-0">{{trans('general.step2_title')}}</p>
                                        <p class="Roboto regular m-0">{{trans('products_import_step1.step2_description')}}</p>
                                    </div>
                                </div>
                            </div>
                            <!--grey3-->
                            <div id="greyThree" class="py-2 px-3">
                                <div class="row text-center">
                                    <div class="col">
                                        <span
                                            class="circle circle-outline-primary text-center Roboto">{{__("3")}}</span>
                                        <p class="Roboto bold mt-3 mb-0">{{trans('general.step3_title')}}</p>
                                        <p class="Roboto regular m-0">{{trans('products_import_step1.step3_description')}}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <form id="pro_imp_form" method="POST" action="{{ route("products.import") }}">

            @csrf
            <input type="hidden" name="temp_type" value="import">
            <input name="file_path" type="hidden" value="{{ $path }}">
            <div class="card border-radius shadow-none">
                <ul class="nav nav-tabs mb-4" id="myTab" role="tablist">

                    <li class="nav-item">
                        <a class="nav-link text-dark active Roboto bold" id="product-tab" data-toggle="tab" href="#product"
                           role="tab" aria-controls="profile" aria-selected="false">{{trans('products_import_step3.products')}}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-dark Roboto bold" id="category-tab" data-toggle="tab" href="#category" role="tab"
                           aria-controls="home" aria-selected="true">{{trans('products_import_step3.categories')}}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-dark Roboto bold" id="variant-tab" data-toggle="tab" href="#variant" role="tab"
                           aria-controls="contact" aria-selected="false">{{trans('products_import_step3.variants')}}</a>
                    </li>
                </ul>
                <div class="tab-content" id="myTabContent">

                    <!--products tab panel-->
                    <div class="tab-pane fade show active" id="product" role="tabpanel" aria-labelledby="profile-tab">
                        <div id="row_container" class="card-body p-0">
                            <div style="">
                                {{--dynamic rows fetch from ajax call--}}

                                <div id="add_row"></div>
                            </div>
                        </div>
                        <div id="">
                            <div class="d-flex justify-content-start">
                                <div class="p-2">
                                    <button id="add_operation" type="button" class="btn btn-primary-tertiary">
                                        <div class="media">
                                            <img src="{{asset('media/new-flow/add another.png')}}" class="mr-1" alt="">
                                            <div class="media-body">
                                                <h5 class="m-0 p-2 Roboto regular">{{trans('products_import_step3.add_operation_btn')}}</h5>
                                            </div>
                                        </div>

                                    </button>
                                </div>

                            </div>
                        </div>
                    </div>


                    <!--categories tab panel-->
                    <div class="tab-pane fade" id="category" role="tabpanel" aria-labelledby="home-tab">
                        <div id="category_row_container" class="card-body p-0">
                            <div>
                                <div id="category_row">

                                </div>

                            </div>
                        </div>
                    </div>
                    <!--variants tab panel-->
                    <div class="tab-pane fade" id="variant" role="tabpanel" aria-labelledby="contact-tab">
                        <div>
                            <div>
                                <div class="d-flex justify-content-start">
                                    <div class="p-2">
                                        <div class="form-group">
                                            <label for="">{{trans('products_import_step3.unique_column_for_variants')}}</label>
                                            <select name="nodes[variant][id]" style="width: 182px"
                                                    class="form-control @error('variant') is-invalid @enderror">
                                                <option value="" class="Poppins regular text-color">Select Column
                                                </option>
                                                @foreach($heading_attributes as $h_attribute)
                                                    @if(isset($template['variant']['id']))
                                                        <option value="{{$h_attribute}}" {{($template['variant']['id'] == $h_attribute) ? 'selected' : null}}>{{$h_attribute}}</option>

                                                    @else
                                                        <option value="{{$h_attribute}}">{{$h_attribute}}</option>
                                                    @endif
                                                @endforeach
                                            </select>
                                        </div>
                                        @error('variant')
                                        <div class="text-danger">
                                            {{$message}}
                                        </div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="p-2">
                                    <label for="">Variant Options</label>
                                    <div class="mb-4">
                                        <div id="var_options" class="row p-0">

                                        </div>

                                    </div>
                                    <div >
                                        <button type="button" id="add-variant-option-btn" class="btn btn-primary ripplelink mb-2">
                                            Add New Option
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--Import CSV Modal -->
                <x-products.import-c-s-v  :templateAttributes="$template_attributes" />

                <div class="row">
                    <div class="col-12">
                        <div class="d-flex flex-row-reverse mt-4">
                            <div class="p-2" style="width: 159px">
                                <button id="pro_imp_btn" type="button"
                                        class="form-control btn btn-primary ripplelink">
                                    {{trans('products_import_step1.next_btn')}}
                                </button>
                            </div>
                            <div class="p-2">
                                <a href="{{route('products.index')}}" id="cancel-btn"
                                   class="px-5 btn btn-outline-primary hovereffect ripplelink">
                                    {{trans('products_import_step1.cancel_btn')}}

                                </a>
                            </div>
                        </div>
                    </div>
                </div>
        </form>

    </div>
    <!--VLookup Modal -->
    <x-products.vlookup/>
@endsection
@push('footer_scripts')

    <script>

        var global_row_count = 0;

        var row_node = '';

        var row_node_flag = false;

        var template_option = null;


        /* LARAVEL META CSRF REQUIREMENT */
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });


        function delete_row(e)
        {
            ($(e).parents(':eq(4)').remove());
        }

        function delete_opt(e)
        {
            ($(e).parents(':eq(2)').remove());
            $('#add-variant-option-btn').show();
        }


        $('#add_operation').on('click',function(){
            // Ajax Call
            let row_count = global_row_count;
            global_row_count++;

            $.ajax({
                url: "{{ route('import.add.row') }}",
                method: "POST",
                async : false,
                data: {
                    heading_attributes: '{!! json_encode($heading_attributes) !!}',
                    apimio_attributes: '{!! json_encode($attributes) !!}',
                    formula : 'assign',
                    row_count : row_count,
                    row_node : (row_node_flag == true) ?
                        row_node : null

                },
                success: function (data) {
                    if (row_count == 0){
                        $('#category_row').append(data.output);
                    }
                    else {
                        $('#add_row').append(data.output);
                    }
                    $('#product-listing-row').html(data);
                },
                error: function (error){
                    console.log(error);
                }
            });

        });


        $('#add-variant-option-btn').on('click',function(){
            // Ajax Call
            options_count = $("#var_options").find(".col-lg-3").length;
            if(options_count >= 2){
                $('#add-variant-option-btn').hide();
            }

            $.ajax({
                url: "{{ route('import.add.variant.option') }}",
                method: "POST",
                async : false,
                data: {
                    heading_attributes: '{!! json_encode($heading_attributes) !!}',
                    template_option : (template_option != null) ? template_option : null,

                },
                success: function (data) {
                    $('#var_options').append(data.output);
                },
                error: function (error){
                    console.log(error);
                }
            });

        });





        $(document).on('change','.formula_field',function(){
            let self = this;
            formula_row = $(self).parents(':eq(2)').find(".assign_formula");
            row_count = formula_row.data('count');
            var formula = $(this).val();

            // Ajax Call
            $.ajax({
                url: "{{ route('import.fetch.formula.fields') }}",
                method: "POST",
                data: {
                    heading_attributes: '{!! json_encode($heading_attributes) !!}',
                    apimio_attributes: '{!! json_encode($attributes) !!}',
                    formula : formula,
                    row_count : row_count
                },
                success: function (data) {
                    // console.log(data.output);
                    formula_row.replaceWith(data.output);
                    // $('#product-listing-row').html(data);
                },
                error: function (error){
                    console.log(error);
                }
            });


        });
        $(document).ready(function () {

            @if(isset($template['variant']['variant_options']))
            @foreach ($template['variant']['variant_options'] as $temp_opt){
                 template_option = @json($temp_opt);
                $("#add-variant-option-btn").click();
            }
            @endforeach
                @endif


            @php
                if(empty($template['data'])){
                    $rows = 5;
                    $nodes = "";
                }
                else{
                    $rows = count($template['data']);
                }


            @endphp
                @for ($i = 0 ; $i < $rows; $i++)
                @if(isset($template['data'][$i]))
                row_node = @json($template['data'][$i]);
            @endif
                row_node_flag = true;

            $( "#add_operation" ).promise().done(function() {
                $("#add_operation").click();
            });

            @endfor

                row_node_flag = false;

        });

        $( "#pro_imp_btn" ).click(function(event) {
            event.preventDefault();
            passValidation = true;
            $($("#add_row").find("select[required]").get().reverse()).each(function (i, obj) {

                if (!obj.checkValidity()) {

                    obj.reportValidity();

                    passValidation = false;

                    return;
                }
            });
            if (passValidation){
                $('#import_product').modal('show');
            }
        });


    </script>
@endpush
