<?php ?>
@extends('layouts.app_new')
@section('titles','Import CSV')
@section('content')
    @push('header_scripts')

        <link href="https://unpkg.com/filepond@^4/dist/filepond.css" rel="stylesheet" />
        <link href="https://unpkg.com/filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css" rel="stylesheet">

        <style>
            button.filepond--file-action-button.filepond--action-revert-item-processing {
                /*display: none;*/
            }
            .filepond--file-action-button.filepond--file-action-button {
                height: 1.625em !important;
            }
            .filepond--root {
                margin-bottom: 0!important;
            }
            .filepond--root .filepond--credits{
                display: none!important;
            }
            .filepond--panel-root {
                background-color: #FFFFFF!important;
            }
            .filepond--label-action {
                text-decoration: none;
            }
            .line {
                width: 80%;
                top: 24px;
                position: absolute;
                border-bottom: 1px solid #e9e6e6;
            }
        </style>
    @endpush

    <div>
        <x-products.page-title name="Import CSV Output" description="Import CSV Result" links="false" button="false" buttonname="null"/>

        <div class="card border-radius shadow-none mb-4">
            <div class="card-body p-0">
                <div class="row">
                    <div class="col-12">
                        <table class="table table-responsive-md table-striped ">
                            <thead class="text-dark ">
                                <tr>
                                    @foreach(array_keys($array_to_view[0]) as $key)
                                        <th>{{$key}}</th>
                                        @endforeach
                                </tr>
                            </thead>
                            <tbody>
                            @foreach($array_to_view as $values)
                            <tr >
                                @foreach($values as $value)
                                    <td>{{$value}}</td>
                                @endforeach

                            </tr>
                            @endforeach
                            </tbody>
                        </table>

                    </div>
                </div>

            </div>
        </div>
    </div>

    @push('footer_scripts')
        <script src="https://unpkg.com/filepond@^4/dist/filepond.js"></script>
        <script src="https://unpkg.com/filepond-plugin-image-preview/dist/filepond-plugin-image-preview.js"></script>
        <script src="https://unpkg.com/filepond-plugin-image-exif-orientation/dist/filepond-plugin-image-exif-orientation.js"></script>
        {{-- <script src="https://cdn.tiny.cloud/1/oh0o94omie4zyuas0tk96qk319s6ltqf8eq9er20jhn3d7r7/tinymce/5/tinymce.min.js" referrerpolicy="origin"></script> --}}
    @endpush
@endsection
