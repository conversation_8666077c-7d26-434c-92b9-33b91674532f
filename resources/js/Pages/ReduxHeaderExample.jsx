import React from "react";
import AppHeader from "./layout/Header";
import { useSelector } from "react-redux";
import { selectUser, selectIsAuthenticated, selectAuthLoading, selectAuthError } from "../store/slices/authSlice";

export default function ReduxHeaderExample() {
    const user = useSelector(selectUser);
    const isAuthenticated = useSelector(selectIsAuthenticated);
    const loading = useSelector(selectAuthLoading);
    const error = useSelector(selectAuthError);

    return (
        <div className="min-h-screen bg-gray-100">
            <AppHeader />

            <div className="max-w-4xl mx-auto mt-8 p-6 bg-white rounded-lg shadow-md">
                <h1 className="text-3xl font-bold mb-8 text-center">Redux Header with API Example</h1>

                <div className="mb-6">
                    <h2 className="text-xl font-semibold mb-4">Current Redux State</h2>

                    <div className="bg-gray-100 p-4 rounded-lg">
                        <p className="mb-2">
                            <strong>Authentication Status:</strong> {isAuthenticated ? "Logged In" : "No Data"}
                        </p>
                        <p className="mb-2">
                            <strong>Loading State:</strong> {loading ? "Loading..." : "Idle"}
                        </p>
                        {error && (
                            <p className="mb-2 text-red-500">
                                <strong>Error:</strong> {error}
                            </p>
                        )}

                        {isAuthenticated && user ? (
                            <div>
                                <h3 className="font-bold mt-4 mb-2">User Details:</h3>
                                {user.id && (
                                    <p className="mb-2">
                                        <strong>User ID:</strong> {user.id}
                                    </p>
                                )}
                                {user.name && (
                                    <p className="mb-2">
                                        <strong>Name:</strong> {user.name}
                                    </p>
                                )}
                                {user.fname && (
                                    <p className="mb-2">
                                        <strong>First Name:</strong> {user.fname}
                                    </p>
                                )}
                                {user.lname && (
                                    <p className="mb-2">
                                        <strong>Last Name:</strong> {user.lname}
                                    </p>
                                )}
                                {user.email && (
                                    <p className="mb-2">
                                        <strong>Email:</strong> {user.email}
                                    </p>
                                )}
                                {user.username && (
                                    <p className="mb-2">
                                        <strong>Username:</strong> {user.username}
                                    </p>
                                )}
                            </div>
                        ) : (
                            <p className="mt-4">No user information available. Data is fetched automatically when the page loads.</p>
                        )}
                    </div>
                </div>

                <div className="mt-8">
                    <h2 className="text-xl font-semibold mb-4">Implementation Notes</h2>
                    <p className="mb-3">
                        This example uses Redux Thunk to fetch data from a real API endpoint. The
                        <code className="bg-gray-200 px-1 mx-1 rounded">/api/2024-12/dashboard/1</code>
                        endpoint is called automatically when:
                    </p>
                    <ul className="list-disc list-inside space-y-1 ml-4">
                        <li>The header component mounts</li>
                    </ul>
                    <p className="mt-3">
                        The original <code className="bg-gray-200 px-1 mx-1 rounded">fetchUser</code> function has been moved into a Redux
                        async thunk, which handles the API call and updates the global state automatically on page load.
                    </p>
                    <p className="mt-3">
                        Both the header and this component read from the same Redux store, so they always display consistent data.
                    </p>
                </div>
            </div>
        </div>
    );
}
