import React, { useState } from "react";
import { Table, Button, Tag } from "antd";

const Stores = ({ product, onDataChange }) => {
    const [stores, setStores] = useState([{ key: "1", name: "Store 1", language: "EN-US", status: "update_available" }]);

    const handleConnectShopify = (key) => {
        // Add Shopify connection logic here
        console.log("Connecting to Shopify for store with key:", key);
        onDataChange?.();
    };

    const columns = [
        {
            title: "Store Name",
            dataIndex: "name",
            key: "name",
        },
        {
            title: "Language",
            dataIndex: "language",
            key: "language",
        },
        {
            title: "Status",
            dataIndex: "status",
            key: "status",
            render: (status) => {
                if (status === "update_available") {
                    return <div className="px-3 py-1 bg-blue-100 text-blue-500 rounded-md inline-block">Update Available</div>;
                }
                return <span>{status}</span>;
            },
        },
        {
            title: "Sync product",
            key: "sync",
            render: (_, record) => (
                <Button
                    type="primary"
                    onClick={() => handleConnectShopify(record.key)}
                    className="bg-[#740898] hover:bg-[#5F0A7B] border-none"
                >
                    Connect to Shopify
                </Button>
            ),
        },
    ];

    return (
        <div>
            <div className="stores-container">
                <div className="stores-header h-[60px] border border-[#DBDBDB] border-b-0 rounded-t-md px-4 flex items-center bg-white">
                    <span className="text-[#252525] font-[600] text-[18px]">Stores</span>
                </div>
                <Table
                    columns={columns}
                    dataSource={stores}
                    pagination={false}
                    className="border border-[#DBDBDB] rounded-b-md stores-table"
                    rowClassName="border-b border-[#DBDBDB]"
                />
            </div>
            <style jsx global>{`
                .stores-table .ant-table-thead > tr > th {
                    border-right: 1px solid #dbdbdb;
                    background: #f9fafb;
                }
                .stores-table .ant-table-thead > tr > th:last-child {
                    border-right: none;
                }
                .stores-table .ant-table-tbody > tr > td {
                    border-right: 1px solid #dbdbdb;
                }
                .stores-table .ant-table-tbody > tr > td:last-child {
                    border-right: none;
                }
                .stores-table .ant-table-cell {
                    padding: 12px;
                }
            `}</style>
        </div>
    );
};

export default Stores;
