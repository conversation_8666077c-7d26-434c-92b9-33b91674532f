import React, { useState, useEffect } from "react";
import { Table, Progress, Tooltip, Row, Col, Button, Input, Drawer, Popover, message } from "antd";
import {
    EditOutlined,
    DeleteOutlined,
    InboxOutlined,
    FileImageOutlined,
    SearchOutlined,
    FilterOutlined,
    ClearOutlined,
} from "@ant-design/icons";
import { useSelector, useDispatch } from "react-redux";
import { setFilteredData, setPagination, setActiveFilters, selectFilteredData, selectActiveFilters } from "../../store/slices/productSlice";
import ProductFilters from "./ProductFilters";
import TickIcon from "../../../../public/v2/icons/tick-icon.svg";
import DeleteIcon from "../../../../public/v2/icons/delete-icon.svg";
import EditIcon from "../../../../public/v2/icons/edit-icon.svg";
import BulkassignIcon from "../../../../public/v2/icons/bulkassign-icon.svg";
import BulkuploadIcon from "../../../../public/v2/icons/Bulkupload-icon.svg";
import { router } from "@inertiajs/react";

const ProductTable = ({ products, onProductSelect, loading = false, pagination, onPaginationChange }) => {
    console.log("products in ProductTable:", products);
    const dispatch = useDispatch();
    const filteredData = useSelector(selectFilteredData);
    const activeFilters = useSelector(selectActiveFilters);

    // Sample data
    const data = [
        {
            key: "1",
            image: "https://via.placeholder.com/50",
            name: "Product A",
            status: "Active",
            variants: 3,
            qualityScore: 85,
            stores: ["A", "BC", "EF", "EF"],
        },
        {
            key: "2",
            image: "https://via.placeholder.com/50",
            name: "Product B",
            status: "Inactive",
            variants: 5,
            qualityScore: 50,
            stores: ["A", "BC"],
        },
        {
            key: "3",
            image: "https://via.placeholder.com/50",
            name: "Product C",
            status: "Draft",
            variants: 2,
            qualityScore: 40,
            stores: ["EF", "EF"],
        },
        {
            key: "4",
            image: "https://via.placeholder.com/50",
            name: "Product D",
            status: "Active",
            variants: 4,
            qualityScore: 95,
            stores: ["EF"],
        },
        {
            key: "5",
            image: "https://via.placeholder.com/50",
            name: "Product E",
            status: "Inactive",
            variants: 3,
            qualityScore: 65,
            stores: ["A"],
        },
        {
            key: "6",
            image: "https://via.placeholder.com/50",
            name: "Product F",
            status: "Draft",
            variants: 6,
            qualityScore: 20,
            stores: ["A", "BC", "EF"],
        },
        {
            key: "7",
            image: "https://via.placeholder.com/50",
            name: "Product G",
            status: "Active",
            variants: 7,
            qualityScore: 70,
            stores: ["A", "BC", "EF", "EF"],
        },
        {
            key: "8",
            image: "https://via.placeholder.com/50",
            name: "Product H",
            status: "Inactive",
            variants: 1,
            qualityScore: 50,
            stores: ["A", "BC"],
        },
        {
            key: "9",
            image: "https://via.placeholder.com/50",
            name: "Product I",
            status: "Draft",
            variants: 5,
            qualityScore: 45,
            stores: ["EF", "EF"],
        },
        {
            key: "10",
            image: "https://via.placeholder.com/50",
            name: "Product J",
            status: "Active",
            variants: 3,
            qualityScore: 80,
            stores: ["EF"],
        },
    ];

    // Add filter state
    const [filterVisible, setFilterVisible] = useState(false);
    const [filterParams, setFilterParams] = useState(null);

    // Add effect to update filterParams when activeFilters changes
    useEffect(() => {
        setFilterParams(activeFilters);
    }, [activeFilters]);

    const columns = [
        {
            title: <span className="text-[#626262] text-[14px] font-normal">Image</span>,
            dataIndex: "file",
            key: "file",
            render: (text) => {
                if (text && text !== "") {
                    return <img src={text} alt="product" className="w-[30px] h-[30px] rounded object-cover" />;
                } else {
                    // Return a placeholder when no image is available
                    return (
                        <div className="w-[30px] h-[30px] rounded bg-gray-100 flex items-center justify-center border border-gray-200">
                            <FileImageOutlined className="text-gray-400 text-sm" />
                        </div>
                    );
                }
            },
        },
        {
            title: <span className="text-[#626262] text-[14px] font-normal">Product Name</span>,
            dataIndex: "handle",
            key: "handle",
        },
        {
            title: <span className="text-[#626262] text-[14px] font-normal">Status</span>,
            dataIndex: "status",
            key: "status",
            render: (text) => (
                <span
                    className={`${text === "Active" ? "text-[#599D00]" : text === "Draft" ? "text-red-500" : "text-gray-500"} font-[400]`}
                >
                    {text}
                </span>
            ),
        },
        {
            title: <span className="text-[#626262] text-[14px] font-normal">No. of Variants</span>,
            dataIndex: "variants_count",
            key: "variants_count",
        },
        {
            title: <span className="text-[#626262] text-[14px] font-normal">Product Quality Score</span>,
            dataIndex: "version_score",
            key: "version_score",
            render: (text) => {
                let strokeColor = "#FE1F23"; // Default: less than 50%
                if (text > 50) strokeColor = "#15D476"; // Above 50%
                if (text === 50) strokeColor = "#FF9C3E"; // Equal to 50%

                return (
                    <div className="flex flex-col items-start">
                        <span className="text-[#252525] text-[12px] font-medium">{text}% completed</span>
                        <Progress percent={text} showInfo={false} strokeColor={strokeColor} className="w-full" />
                    </div>
                );
            },
        },
        {
            title: <span className="text-[#626262] text-[14px] font-normal">Stores</span>,
            key: "stores",
            render: (_, record) => (
                <div className="flex flex-row">
                    {record?.stores?.map((store, index) => (
                        <span
                            key={index}
                            className="text-[#252525] text-[12px] font-medium w-[32px] h-[32px] rounded-full  bg-[#F1E6F5] flex items-center justify-center border border-white mr-[-5px]"
                        >
                            <span className="text-[#740898] text-[14px] font-normal">{store}</span>
                        </span>
                    ))}
                </div>
            ),
        },
        {
            title: <span className="text-[#626262] text-[14px] font-normal">Actions</span>,
            key: "actions",
            render: (_, record) => (
                <div className="flex space-x-3">
                    <Tooltip title="Edit">
                        <EditOutlined className="text-blue-500 cursor-pointer hover:scale-110 transition" />
                    </Tooltip>
                    <Tooltip title="Delete">
                        <DeleteOutlined className="text-red-500 cursor-pointer hover:scale-110 transition" />
                    </Tooltip>
                </div>
            ),
        },
    ];
    const [selectedRows, setSelectedRows] = useState([]);

    // Function to handle row click
    const handleRowClick = (record) => {
        if (onProductSelect) {
            // Use the onProductSelect function provided by the parent component
            onProductSelect(record);
        } else {
            // If no onProductSelect is provided, directly navigate to the product page
            router.visit(`/v2/products/single/${record.id}`);
        }
    };

    // Function to handle pagination change
    const handleTableChange = (paginate) => {
        if (onPaginationChange) {
            onPaginationChange(paginate.current, paginate.pageSize);
        }
        dispatch(
            setPagination({
                current: paginate.current,
                pageSize: paginate.pageSize,
            })
        );
    };

    // Handle filter open/close
    const showFilter = () => {
        setFilterVisible(true);
    };

    const closeFilter = () => {
        setFilterVisible(false);
    };

    // Handle filtered data
    const handleFilteredData = (data, paginationData, filters) => {
        dispatch(setFilteredData(data));
        dispatch(setPagination(paginationData));
        dispatch(setActiveFilters(filters));
        setFilterParams(filters);

        if (onPaginationChange) {
            onPaginationChange(paginationData.current, paginationData.pageSize);
        }
    };

    // Handle clear filters
    const clearFilters = () => {
        // Clear filters from Redux - do this first to trigger the useEffect in ProductFilters
        dispatch(setActiveFilters(null));
        dispatch(setFilteredData(null));
        dispatch(
            setPagination({
                current: 1,
                pageSize: pagination?.pageSize || 10,
                total: products?.pagination?.total || 0,
            })
        );

        // Clear from localStorage
        localStorage.removeItem("filters");
        localStorage.removeItem("orgId");

        // Reset filter params
        setFilterParams(null);

        // Clear filter DOM elements if they exist
        const countElement = document.getElementById("filter_total_product_count");
        if (countElement) countElement.value = "";

        const arrayElement = document.getElementById("filter_total_product_array");
        if (arrayElement) arrayElement.value = "";

        // Refresh the ProductTable if needed
        if (onPaginationChange) {
            onPaginationChange(1, pagination?.pageSize || 10);
        }

        // Close filter drawer if open
        setFilterVisible(false);

        message.success("Filters cleared");
    };

    return (
        <div>
            <div className="bg-white rounded-[12px] border border-[#DBDBDB] mb-0">
                <Row>
                    <Col span={12}>
                        <p className="text-[#252525] font-[600] text-[18px] p-5">Products</p>
                    </Col>
                    <Col span={12}>
                        <div className="flex justify-end items-center p-5 gap-[14px]">
                            <Input placeholder="Search Product" allowClear style={{ width: 200 }} prefix={<SearchOutlined />} />
                            {filterParams && (
                                <Button icon={<ClearOutlined />} onClick={clearFilters} className="text-[#FE1F23]">
                                    Clear Filters
                                </Button>
                            )}
                            <Popover
                                content={<ProductFilters onFilteredData={handleFilteredData} onClose={closeFilter} />}
                                title={null}
                                trigger="click"
                                open={filterVisible}
                                onOpenChange={setFilterVisible}
                                placement="bottomRight"
                                overlayStyle={{ width: "424px" }}
                                overlayInnerStyle={{ padding: 0 }}
                            >
                                <Button icon={<FilterOutlined />} onClick={showFilter}>
                                    Filter
                                </Button>
                            </Popover>
                        </div>
                    </Col>
                </Row>

                {/* Filter indicator bar */}
                {filterParams && (
                    <div className="px-5 py-2 border-t bg-[#F9FAFB] border-[#DBDBDB] flex justify-start items-center">
                        <span className="text-[#626262] text-[14px]">
                            Showing filtered results
                            {pagination && ` (${pagination.total} products)`}
                        </span>
                        <Button type="link" onClick={clearFilters} className="text-[#740898] font-semibold px-2">
                            Clear Filters
                        </Button>
                    </div>
                )}

                <Table
                    loading={loading}
                    rowSelection={{
                        type: "checkbox",
                        onChange: (_, selectedRowsData) => setSelectedRows(selectedRowsData), // Track selected rows
                    }}
                    columns={columns}
                    rowKey={(record) => record.id}
                    dataSource={filteredData || products?.products}
                    pagination={
                        pagination
                            ? {
                                  current: pagination.current,
                                  pageSize: pagination.pageSize,
                                  total: pagination.total,
                                  showSizeChanger: true,
                                  pageSizeOptions: ["10", "20", "50", "100"],
                                  showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} products`,
                              }
                            : { pageSize: 10 }
                    }
                    onChange={handleTableChange}
                    bordered
                    size="small"
                    onRow={(record) => ({
                        onClick: () => handleRowClick(record),
                        style: { cursor: "pointer" },
                    })}
                    style={{ minHeight: "calc(100vh - 420px)" }}
                />
            </div>

            {/* Bottom bar using sticky positioning */}
            {selectedRows.length > 0 && (
                <div className="sticky h-[92px] bottom-0 border-t border-l border-r border-[#740898] p-5 bg-[#ECDDF1] rounded-t-[12px] mt-2">
                    <Row>
                        <Col span={12}>
                            <div className="flex">
                                <img src={TickIcon} alt="tick icon" />
                                <p className="pl-2 font-normal text-[14px] text-[#252525]">{selectedRows.length} Selected on this page</p>
                            </div>
                            <p className="pl-7 text-[#740898] text-[14px] font-[700]">Select all {products?.pagination.total} results</p>
                        </Col>
                        <Col span={12}>
                            <div className="flex gap-[16px] items-end justify-end">
                                <Button className="border border-[#D9D9D900] rounded-[4px] h-[32px] text-[14px] font-normal text-[#FE1F23]">
                                    <img src={DeleteIcon} alt="delete icon" />
                                    Delete
                                </Button>
                                <Button className="border border-[#D9D9D900] rounded-[4px] h-[32px] text-[14px] font-normal">
                                    <img src={EditIcon} alt="edit icon" />
                                    Bulk editing
                                </Button>
                                <Button className="border border-[#D9D9D900] rounded-[4px] h-[32px] text-[14px] font-normal">
                                    <img src={BulkassignIcon} alt="bulk assign icon" />
                                    Bulk assign
                                </Button>
                                <Button className="border border-[#D9D9D900] rounded-[4px] h-[32px] text-[14px] font-normal">
                                    <img src={BulkuploadIcon} alt="bulk upload icon" />
                                    Bulk price upload
                                </Button>
                            </div>
                        </Col>
                    </Row>
                </div>
            )}
        </div>
    );
};

export default ProductTable;
