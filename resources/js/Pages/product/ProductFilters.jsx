import React, { useState, useEffect } from "react";
import { CloseOutlined, PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import axios from "axios";
import { Button, message, Select, Divider, Input, TreeSelect, Spin } from "antd";
import { useSelector, useDispatch } from "react-redux";
import { setFilteredData, setPagination, setActiveFilters, selectActiveFilters } from "../../store/slices/productSlice";
import DeleteIcon from "../../../../public/v2/icons/delete-icon.svg";
import CloseIcon from "../../../../public/v2/icons/close-icon.svg";
import { v4 as uuidv4 } from "uuid"; // Import uuid for generating unique keys
const { TextArea } = Input;
import { get, post } from "../../axios";

// Helper function to transform category tree data to use name as title and id as value
const transformCategoryTree = (categories) => {
    if (!categories) return [];

    return categories.map((category) => ({
        ...category,
        title: category.name,
        value: category.id,
        key: category.id,
        children: category.children ? transformCategoryTree(category.children) : [],
    }));
};

const ProductFilters = ({ onFilteredData, onClose }) => {
    const dispatch = useDispatch();
    const reduxActiveFilters = useSelector(selectActiveFilters);

    const [filterGroups, setFilterGroups] = useState([]);
    const [attributes, setAttributes] = useState([]);
    const [formulas, setFormulas] = useState({});
    const [showOrDivider, setShowOrDivider] = useState(false);
    const [loading, setLoading] = useState(true);
    const organizationId = document.getElementById("listingTable")?.getAttribute("data-orgId");

    useEffect(() => {
        // Fetch attributes and formulas for filter dropdown
        fetchFilterData();
    }, []);

    useEffect(() => {
        // Load saved filters from localStorage if they exist
        if (attributes.length > 0 && Object.keys(formulas).length > 0) {
            loadSavedFilters();
        }
    }, [attributes, formulas]);

    // Add effect to reset local state when Redux filters are cleared
    useEffect(() => {
        // When reduxActiveFilters becomes null, reset the local filter state
        if (reduxActiveFilters === null) {
            setFilterGroups([]);
            setShowOrDivider(false);
        }
    }, [reduxActiveFilters]);

    const fetchFilterData = () => {
        setLoading(true);
        get("filters")
            .then((response) => {
                setAttributes(response.filters.attributes);
                setFormulas(response.filters.formulas);
                console.log("Filter data loaded:", response.filters);
            })
            .catch((error) => {
                console.error("Failed to fetch filter data:", error);
                message.error("Failed to load filter data. Please try again later.");
            })
            .finally(() => {
                setLoading(false);
            });
    };

    const loadSavedFilters = async () => {
        // First check if we have filters in Redux state
        if (reduxActiveFilters) {
            try {
                // Format saved filters from Redux for our component structure
                const formattedGroups = await Promise.all(
                    reduxActiveFilters.map(async (group) => {
                        const formattedRows = await Promise.all(
                            group.map(async (filter) => {
                                // Find the matching attribute from our attributes list
                                let matchedAttribute = null;

                                for (const attrGroup of attributes) {
                                    for (const option of attrGroup.options || []) {
                                        try {
                                            const optionValue = JSON.parse(option.value);
                                            if (optionValue.value === filter.attribute) {
                                                matchedAttribute = optionValue;
                                                break;
                                            }
                                        } catch (error) {
                                            console.error("Error parsing option value:", error);
                                        }
                                    }
                                    if (matchedAttribute) break;
                                }

                                if (!matchedAttribute) return null;

                                // Create new row with the saved data
                                const newRow = {
                                    id: uuidv4(),
                                    selectedAttribute: matchedAttribute,
                                    selectedFormula: filter.formula,
                                    logicOperator: "AND",
                                    treeData: [],
                                };

                                // Handle different value types based on attribute type
                                if (matchedAttribute.type === "multi_select") {
                                    newRow.selectedTreeValue = Array.isArray(filter.value) ? filter.value : [];

                                    // Fetch tree data if needed
                                    if (matchedAttribute.api_url) {
                                        try {
                                            const response = await get(`${matchedAttribute.api_url}`);
                                            // Strip anything after the "?" in api_url when accessing response data
                                            const apiKey = matchedAttribute.api_url.split("?")[0];

                                            // Transform data for categories specifically
                                            if (matchedAttribute.value === "categories" && response[apiKey]) {
                                                newRow.treeData = transformCategoryTree(response[apiKey]);
                                            } else {
                                                newRow.treeData = response[apiKey];
                                            }
                                        } catch (error) {
                                            console.error(`Error fetching data for ${matchedAttribute.api_url}:`, error);
                                        }
                                    }
                                } else if (matchedAttribute.type === "dropdown") {
                                    newRow.selectedDropdownValue = filter.value;

                                    // Fetch dropdown options if needed
                                    if (matchedAttribute.api_url) {
                                        try {
                                            const response = await get(`${matchedAttribute.api_url}`);
                                            // Strip anything after the "?" in api_url when accessing response data
                                            const apiKey = matchedAttribute.api_url.split("?")[0];
                                            newRow.treeData = response[apiKey];
                                        } catch (error) {
                                            console.error(`Error fetching data for ${matchedAttribute.api_url}:`, error);
                                        }
                                    }
                                } else {
                                    // For text, number, and long_text
                                    newRow.inputValue = filter.value;
                                }

                                // Add any additional data
                                if (filter.json_data) newRow.json_data = filter.json_data;
                                if (filter.family) newRow.family = filter.family;

                                return newRow;
                            })
                        );

                        return formattedRows.filter(Boolean);
                    })
                );

                setFilterGroups(formattedGroups);
                setShowOrDivider(formattedGroups.length > 1);
                return;
            } catch (error) {
                console.error("Error loading saved filters from Redux:", error);
            }
        }

        // If no Redux filters, fallback to localStorage
        const savedFilters = localStorage.getItem("filters");
        const savedOrgId = localStorage.getItem("orgId");

        if (savedFilters && savedOrgId === organizationId) {
            try {
                const parsedFilters = JSON.parse(savedFilters);

                // Format saved filters for our component structure
                const formattedGroups = await Promise.all(
                    parsedFilters.map(async (group) => {
                        const formattedRows = await Promise.all(
                            group.map(async (filter) => {
                                // Find the matching attribute from our attributes list
                                let matchedAttribute = null;

                                for (const attrGroup of attributes) {
                                    for (const option of attrGroup.options || []) {
                                        try {
                                            const optionValue = JSON.parse(option.value);
                                            if (optionValue.value === filter.attribute) {
                                                matchedAttribute = optionValue;
                                                break;
                                            }
                                        } catch (error) {
                                            console.error("Error parsing option value:", error);
                                        }
                                    }
                                    if (matchedAttribute) break;
                                }

                                if (!matchedAttribute) return null;

                                // Create new row with the saved data
                                const newRow = {
                                    id: uuidv4(),
                                    selectedAttribute: matchedAttribute,
                                    selectedFormula: filter.formula,
                                    logicOperator: "AND",
                                    treeData: [],
                                };

                                // Handle different value types based on attribute type
                                if (matchedAttribute.type === "multi_select") {
                                    newRow.selectedTreeValue = Array.isArray(filter.value) ? filter.value : [];

                                    // Fetch tree data if needed
                                    if (matchedAttribute.api_url) {
                                        try {
                                            const response = await get(`${matchedAttribute.api_url}`);
                                            // Strip anything after the "?" in api_url when accessing response data
                                            const apiKey = matchedAttribute.api_url.split("?")[0];

                                            // Transform data for categories specifically
                                            if (matchedAttribute.value === "categories" && response[apiKey]) {
                                                newRow.treeData = transformCategoryTree(response[apiKey]);
                                            } else {
                                                newRow.treeData = response[apiKey];
                                            }
                                        } catch (error) {
                                            console.error(`Error fetching data for ${matchedAttribute.api_url}:`, error);
                                        }
                                    }
                                } else if (matchedAttribute.type === "dropdown") {
                                    newRow.selectedDropdownValue = filter.value;

                                    // Fetch dropdown options if needed
                                    if (matchedAttribute.api_url) {
                                        try {
                                            const response = await get(`${matchedAttribute.api_url}`);
                                            // Strip anything after the "?" in api_url when accessing response data
                                            const apiKey = matchedAttribute.api_url.split("?")[0];
                                            newRow.treeData = response[apiKey];
                                        } catch (error) {
                                            console.error(`Error fetching data for ${matchedAttribute.api_url}:`, error);
                                        }
                                    }
                                } else {
                                    // For text, number, and long_text
                                    newRow.inputValue = filter.value;
                                }

                                // Add any additional data
                                if (filter.json_data) newRow.json_data = filter.json_data;
                                if (filter.family) newRow.family = filter.family;

                                return newRow;
                            })
                        );

                        return formattedRows.filter(Boolean);
                    })
                );

                setFilterGroups(formattedGroups);
                setShowOrDivider(formattedGroups.length > 1);
            } catch (error) {
                console.error("Error loading saved filters from localStorage:", error);
            }
        }
    };

    const handleAttributeChange = async (value, groupIndex, rowIndex) => {
        try {
            const selectedAttribute = JSON.parse(value);
            const { type, api_url } = selectedAttribute;

            const updatedGroups = [...filterGroups];
            updatedGroups[groupIndex][rowIndex] = {
                ...updatedGroups[groupIndex][rowIndex],
                selectedAttribute,
                selectedFormula: null,
                selectedTreeValue: [],
                inputValue: "",
                selectedDropdownValue: null,
                treeData: [],
            };

            // Add family: "variants" if the attribute is SKU
            if (selectedAttribute.value === "sku") {
                updatedGroups[groupIndex][rowIndex].family = "variants";
            }

            // Fetch data for tree select or dropdown if needed
            if (api_url) {
                try {
                    setLoading(true);
                    const response = await get(`${api_url}`);
                    // api_url is also the response
                    // so we need to get the data from the response like response.api_url

                    console.log("Response of options:", response, response[api_url], api_url);
                    // Strip anything after the "?" in api_url when accessing response data
                    const apiKey = api_url.split("?")[0];

                    // Transform data for categories specifically - change title to name and value to id
                    if (selectedAttribute.value === "categories" && response[apiKey]) {
                        updatedGroups[groupIndex][rowIndex].treeData = transformCategoryTree(response[apiKey]);
                    } else {
                        updatedGroups[groupIndex][rowIndex].treeData = response[apiKey];
                    }
                } catch (error) {
                    console.error(`Error fetching data for ${api_url}:`, error);
                    message.error("Failed to load options. Please try again.");
                } finally {
                    setLoading(false);
                }
            }

            setFilterGroups(updatedGroups);
        } catch (error) {
            console.error("Error handling attribute change:", error);
        }
    };

    const handleFormulaChange = (value, groupIndex, rowIndex) => {
        const updatedGroups = [...filterGroups];
        updatedGroups[groupIndex][rowIndex].selectedFormula = value;
        setFilterGroups(updatedGroups);
    };

    const handleLogicOperatorChange = (logicType, groupIndex, rowIndex) => {
        const updatedGroups = [...filterGroups];
        updatedGroups[groupIndex][rowIndex].logicOperator = logicType;
        setFilterGroups(updatedGroups);
    };

    const handleInputChange = (value, groupIndex, rowIndex) => {
        const updatedGroups = [...filterGroups];
        updatedGroups[groupIndex][rowIndex].inputValue = typeof value === "object" && value.target ? value.target.value : value;
        setFilterGroups(updatedGroups);
    };

    // Helper function to get all child node values recursively
    const getAllChildrenValues = (node, treeData) => {
        if (!node || !treeData || treeData.length === 0) return [];

        // Use a recursive function to collect all children values
        const collectChildren = (nodeId, data) => {
            let childValues = [];

            // Find node with matching value
            const targetNode = data.find((item) => item.value === nodeId);

            if (targetNode && targetNode.children && targetNode.children.length > 0) {
                // Add all direct children values
                for (const child of targetNode.children) {
                    childValues.push(child.value);
                    // Recursively add all descendants
                    const descendants = collectChildren(child.value, targetNode.children);
                    childValues = [...childValues, ...descendants];
                }
            }

            return childValues;
        };

        return collectChildren(node, treeData);
    };

    const handleTreeSelectChange = (value, groupIndex, rowIndex) => {
        const updatedGroups = [...filterGroups];
        const currentRow = updatedGroups[groupIndex][rowIndex];

        // If this is a category attribute, we need to handle child selection
        if (currentRow.selectedAttribute && currentRow.selectedAttribute.value === "categories") {
            const previousValues = new Set(currentRow.selectedTreeValue || []);
            const newValues = new Set(value);

            // Find newly selected values (values in newValues but not in previousValues)
            const newlySelected = [...newValues].filter((v) => !previousValues.has(v));

            // Find deselected values (values in previousValues but not in newValues)
            const deselected = [...previousValues].filter((v) => !newValues.has(v));

            // For newly selected values, add all their children
            let childrenToAdd = [];
            for (const newValue of newlySelected) {
                const children = getAllChildrenValues(newValue, currentRow.treeData);
                childrenToAdd = [...childrenToAdd, ...children];
            }

            // Add children to the selected values if they aren't already included
            let finalValues = [...new Set([...value, ...childrenToAdd])];

            // Handle the case where a parent is deselected but its children might be in the selection
            // We don't need to explicitly handle this because:
            // 1. If checkStrictly is true, children will remain selected when parent is deselected
            // 2. If a parent is manually deselected, we respect the user's choice and don't reselect it

            updatedGroups[groupIndex][rowIndex].selectedTreeValue = finalValues;
        } else {
            // For non-category attributes, just set the value as is
            updatedGroups[groupIndex][rowIndex].selectedTreeValue = value;
        }

        setFilterGroups(updatedGroups);
    };

    const handleDropdownChange = (value, groupIndex, rowIndex) => {
        const updatedGroups = [...filterGroups];
        updatedGroups[groupIndex][rowIndex].selectedDropdownValue = value;
        setFilterGroups(updatedGroups);
    };

    const addFilterRow = (groupIndex) => {
        const updatedGroups = [...filterGroups];
        updatedGroups[groupIndex].push({
            id: uuidv4(),
            selectedAttribute: null,
            selectedFormula: null,
            inputValue: "",
            selectedTreeValue: [],
            selectedDropdownValue: null,
            treeData: [],
            logicOperator: "AND",
        });
        setFilterGroups(updatedGroups);
    };

    const removeFilterRow = (groupIndex, rowIndex) => {
        const updatedGroups = [...filterGroups];
        if (updatedGroups[groupIndex].length > 1) {
            updatedGroups[groupIndex].splice(rowIndex, 1);
        } else if (filterGroups.length > 1) {
            updatedGroups.splice(groupIndex, 1);
            setShowOrDivider(updatedGroups.length > 1);
        } else {
            // Last row in the only group, clear it instead of removing
            updatedGroups[groupIndex] = [
                {
                    id: uuidv4(),
                    selectedAttribute: null,
                    selectedFormula: null,
                    inputValue: "",
                    selectedTreeValue: [],
                    selectedDropdownValue: null,
                    treeData: [],
                    logicOperator: "AND",
                },
            ];
        }
        setFilterGroups(updatedGroups);
    };

    const addFilterGroup = () => {
        setFilterGroups([
            ...filterGroups,
            [
                {
                    id: uuidv4(),
                    selectedAttribute: null,
                    selectedFormula: null,
                    inputValue: "",
                    selectedTreeValue: [],
                    selectedDropdownValue: null,
                    treeData: [],
                    logicOperator: "AND",
                },
            ],
        ]);
        setShowOrDivider(true);
    };

    const convertFiltersForApi = () => {
        return filterGroups.map((group) => {
            return group
                .map((row) => {
                    const attribute = row.selectedAttribute?.value ?? null;
                    const formula = row.selectedFormula ?? null;
                    let value = "";

                    // Handle different value types based on attribute type
                    if (row.selectedAttribute) {
                        switch (row.selectedAttribute.type) {
                            case "multi_select":
                                // Special handling for categories - use the selected IDs
                                if (attribute === "categories") {
                                    value = row.selectedTreeValue || [];
                                    // Ensure we're using the IDs which are now stored in the 'value' property
                                } else {
                                    value = row.selectedTreeValue || [];
                                }
                                break;
                            case "dropdown":
                                value = row.selectedDropdownValue || "";
                                break;
                            case "short_text":
                            case "number":
                            case "long_text":
                                value = row.inputValue || "";
                                break;
                            default:
                                value = row.inputValue || "";
                                break;
                        }
                    }

                    if (!attribute || !formula) return null;

                    const filterData = {
                        attribute: attribute,
                        formula: formula,
                        value: value,
                    };

                    // Add additional data if present
                    if (row.json_data) filterData.json_data = row.json_data;
                    if (row.family) filterData.family = row.family;

                    return filterData;
                })
                .filter((row) => row !== null);
        });
    };

    const handleSubmit = () => {
        const filtersForApi = convertFiltersForApi();

        // Check if any filters are invalid
        const isInvalidFilter = filtersForApi.some((group) => {
            return (
                group.length === 0 ||
                group.some((filter) => {
                    // Skip value validation for certain formulas or attribute types
                    const isValueOptional =
                        filter.formula === "is_defined" || filter.formula === "is_not_defined" || filter.attribute === "status";

                    // Check if value array is empty for certain formulas (only when value is required)
                    return !isValueOptional && Array.isArray(filter.value) && filter.value.length === 0;
                })
            );
        });

        // if (isInvalidFilter) {
        //     message.warning("Please select attribute, formula, and value for all filters.");
        //     return;
        // }

        // If no valid filters, notify the user
        if (filtersForApi.length === 0) {
            message.warning("No filters selected. Please choose at least one filter.");
            return;
        }

        post(`products/index`, { filters: filtersForApi })
            .then((response) => {
                const { data } = response;
                const pagination = {
                    current: response.pagination.current_page,
                    pageSize: 30,
                    total: response.pagination.total,
                };

                // Store filters in localStorage
                localStorage.setItem("filters", JSON.stringify(filtersForApi));
                localStorage.setItem("orgId", JSON.stringify(organizationId));

                // Update Redux state
                dispatch(setFilteredData(data));
                dispatch(setPagination(pagination));
                dispatch(setActiveFilters(filtersForApi));

                // Call the callback if provided
                if (onFilteredData) {
                    onFilteredData(data, pagination, filtersForApi);
                }

                // Replace jQuery calls with vanilla JavaScript
                const countElement = document.getElementById("filter_total_product_count");
                if (countElement) countElement.value = pagination.total;

                const arrayElement = document.getElementById("filter_total_product_array");
                if (arrayElement) arrayElement.value = localStorage.getItem("filters");

                message.success("Filter Applied!");

                if (onClose) onClose();
            })
            .catch((error) => {
                console.log("Error in filter:", error);
                message.error("Server Busy, Try again Later");
            });
    };

    // Function to check if there are valid filters configured
    const hasValidFilters = () => {
        if (filterGroups.length === 0) return false;

        // Check if any group has at least one complete filter
        return filterGroups.some((group) =>
            group.some(
                (row) =>
                    row.selectedAttribute &&
                    row.selectedFormula &&
                    // For is_defined and is_not_defined, no value is needed
                    (row.selectedFormula === "is_defined" ||
                        row.selectedFormula === "is_not_defined" ||
                        // For other formulas, check appropriate value based on type
                        (row.selectedAttribute.type === "multi_select" &&
                            Array.isArray(row.selectedTreeValue) &&
                            row.selectedTreeValue.length > 0) ||
                        (row.selectedAttribute.type === "dropdown" && row.selectedDropdownValue) ||
                        (["short_text", "number", "long_text"].includes(row.selectedAttribute.type) && row.inputValue))
            )
        );
    };

    const clearAllFilters = () => {
        // Reset local state
        setFilterGroups([]);
        setShowOrDivider(false);

        // Clear from localStorage
        localStorage.removeItem("filters");
        localStorage.removeItem("orgId");

        // Clear from Redux state
        dispatch(setActiveFilters(null));
        dispatch(setFilteredData(null));

        // Reset DOM elements if they exist
        const countElement = document.getElementById("filter_total_product_count");
        if (countElement) countElement.value = "";

        const arrayElement = document.getElementById("filter_total_product_array");
        if (arrayElement) arrayElement.value = "";

        message.success("Filters cleared");
    };

    return (
        <div className="w-full min-h-[690px] max-h-[690px] flex flex-col shadow-lg border-0 rounded-lg">
            {/* Header Section */}
            <div className="bg-[#740898] p-4 flex justify-between items-center rounded-t-lg">
                <h5 className="m-0 text-white font-[600] text-[18px]">Filter</h5>
                <Button type="text" icon={<img src={CloseIcon} alt="close icon" />} onClick={onClose} className="cursor-pointer" />
            </div>

            {/* Filter Content Section */}
            <div className="flex-1 overflow-y-auto p-4">
                {loading ? (
                    <div className="flex justify-center items-center h-full">
                        <Spin size="large" />
                    </div>
                ) : (
                    <>
                        {/* Always show the + Attribute button, but disable it when filters exist */}
                        <div
                            className={`border border-[#e8e8e8] rounded h-8 px-4 flex items-center mb-4 ${
                                filterGroups.length === 0
                                    ? "cursor-pointer opacity-100 bg-white"
                                    : "cursor-not-allowed opacity-60 bg-[#F5F5F5]"
                            }`}
                            onClick={() => {
                                if (filterGroups.length === 0) {
                                    setFilterGroups([
                                        [
                                            {
                                                id: uuidv4(),
                                                selectedAttribute: null,
                                                selectedFormula: null,
                                                inputValue: "",
                                                selectedTreeValue: [],
                                                selectedDropdownValue: null,
                                                treeData: [],
                                                logicOperator: "AND",
                                            },
                                        ],
                                    ]);
                                }
                            }}
                        >
                            <PlusOutlined className="mr-2 text-[#740898]" />
                            <span className="text-[#464646]">Attribute</span>
                        </div>

                        {filterGroups.length > 0 &&
                            filterGroups.map((group, groupIndex) => (
                                <div key={`group-${groupIndex}`}>
                                    {/* Show OR divider before each group except the first one */}
                                    {showOrDivider && groupIndex > 0 && (
                                        <div className="text-center my-3 relative">
                                            <div className="border-b border-[#f0f0f0] absolute w-full top-1/2"></div>
                                            <span className="inline-block px-2.5 bg-white relative font-[700] text-sm">OR</span>
                                        </div>
                                    )}
                                    <div className="border border-[#e8e8e8] rounded p-4 mb-4">
                                        {group.map((row, rowIndex) => (
                                            <div key={row.id || `row-${groupIndex}-${rowIndex}`} className="mb-4">
                                                {/* Two Column Layout for Attribute and Formula */}
                                                <div className="flex gap-3 mb-2">
                                                    <div className="flex-1">
                                                        <Select
                                                            className="w-full"
                                                            placeholder="Select Attribute"
                                                            value={
                                                                row.selectedAttribute ? JSON.stringify(row.selectedAttribute) : undefined
                                                            }
                                                            onChange={(value) => handleAttributeChange(value, groupIndex, rowIndex)}
                                                            options={attributes}
                                                            showSearch
                                                        />
                                                    </div>
                                                    <div className="flex-1">
                                                        <Select
                                                            className="w-full"
                                                            placeholder="Select Formula"
                                                            value={row.selectedFormula}
                                                            onChange={(value) => handleFormulaChange(value, groupIndex, rowIndex)}
                                                            disabled={!row.selectedAttribute}
                                                            options={
                                                                row.selectedAttribute && formulas[row.selectedAttribute.type]
                                                                    ? formulas[row.selectedAttribute.type].map((f) => ({
                                                                          label: f.label,
                                                                          value: f.value,
                                                                      }))
                                                                    : []
                                                            }
                                                        />
                                                    </div>
                                                </div>

                                                {/* Conditional Value Input based on attribute type and formula */}
                                                {row.selectedAttribute &&
                                                    row.selectedFormula &&
                                                    row.selectedFormula !== "is_defined" &&
                                                    row.selectedFormula !== "is_not_defined" && (
                                                        <div className="mt-2 mb-3">
                                                            {/* Multi-select attribute */}
                                                            {row.selectedAttribute.type === "multi_select" && (
                                                                <TreeSelect
                                                                    className="w-full"
                                                                    placeholder="Select values"
                                                                    value={row.selectedTreeValue}
                                                                    onChange={(value) =>
                                                                        handleTreeSelectChange(value, groupIndex, rowIndex)
                                                                    }
                                                                    treeData={row.treeData}
                                                                    treeCheckable={true}
                                                                    showCheckedStrategy={
                                                                        row.selectedAttribute.value === "categories"
                                                                            ? TreeSelect.SHOW_ALL
                                                                            : TreeSelect.SHOW_PARENT
                                                                    }
                                                                    allowClear
                                                                    multiple
                                                                    checkStrictly={row.selectedAttribute.value === "categories"}
                                                                    fieldNames={
                                                                        row.selectedAttribute.value === "categories"
                                                                            ? { label: "title", value: "value", children: "children" }
                                                                            : undefined
                                                                    }
                                                                />
                                                            )}

                                                            {/* Dropdown attribute */}
                                                            {row.selectedAttribute.type === "dropdown" && (
                                                                <Select
                                                                    className="w-full"
                                                                    placeholder="Select a value"
                                                                    value={row.selectedDropdownValue}
                                                                    onChange={(value) => handleDropdownChange(value, groupIndex, rowIndex)}
                                                                    options={row.treeData}
                                                                />
                                                            )}

                                                            {/* Short text and number attributes */}
                                                            {(row.selectedAttribute.type === "short_text" ||
                                                                row.selectedAttribute.type === "number") && (
                                                                <Input
                                                                    placeholder="Enter value"
                                                                    value={row.inputValue}
                                                                    onChange={(e) => handleInputChange(e, groupIndex, rowIndex)}
                                                                />
                                                            )}

                                                            {/* Long text attribute */}
                                                            {row.selectedAttribute.type === "long_text" && (
                                                                <TextArea
                                                                    placeholder="Enter value"
                                                                    value={row.inputValue}
                                                                    onChange={(e) => handleInputChange(e, groupIndex, rowIndex)}
                                                                    rows={3}
                                                                />
                                                            )}
                                                        </div>
                                                    )}

                                                {/* AND/OR Buttons and Delete Icon */}
                                                <div className="flex gap-[15px] mt-2.5 mb-4 border-b-0">
                                                    <button
                                                        onClick={() => addFilterRow(groupIndex)}
                                                        className="cursor-pointer border-none text-[#1890FF] text-[14px] font-bold h-7 px-1"
                                                    >
                                                        AND
                                                    </button>
                                                    <button
                                                        onClick={addFilterGroup}
                                                        className="cursor-pointer border-none text-[#1890FF] text-[14px] font-bold h-7 px-1"
                                                    >
                                                        OR
                                                    </button>
                                                    <div className="flex-1"></div>
                                                    <Button
                                                        type="text"
                                                        danger
                                                        size="small"
                                                        onClick={() => removeFilterRow(groupIndex, rowIndex)}
                                                        className="text-xs h-7 px-3 ml-auto"
                                                    >
                                                        <img src={DeleteIcon} alt="delete icon" />
                                                    </Button>
                                                </div>

                                                {/* AND Logic Options between rows */}
                                                {rowIndex < group.length - 1 && (
                                                    <div className="text-center my-3 relative">
                                                        <div className="border-b border-[#f0f0f0] absolute w-full top-1/2"></div>
                                                        <span className="inline-block px-2.5 bg-white relative text-sm text-[#292929] font-[700]">
                                                            AND
                                                        </span>
                                                    </div>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ))}
                    </>
                )}
            </div>

            {/* Footer Actions */}
            <div className="p-4 border-t border-[#f0f0f0] flex justify-end items-center">
                <Button type="text" onClick={clearAllFilters} className="text-[#FE1F23]">
                    Clear all
                </Button>
                <Button
                    type="primary"
                    onClick={handleSubmit}
                    disabled={!hasValidFilters()}
                    className={`${
                        hasValidFilters() ? "bg-[#740898] border-[#740898]" : "bg-[#d8c1e0] border-[#d8c1e0]"
                    } text-white font-[400] text-[14px]`}
                >
                    Apply
                </Button>
            </div>
        </div>
    );
};

export default ProductFilters;
