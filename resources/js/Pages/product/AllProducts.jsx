import React, { useState, useEffect } from "react";
import { router, usePage } from "@inertiajs/react";
import { Row, Col, Button, Modal, Form, Input, message, Space } from "antd";
import { LeftOutlined, UnorderedListOutlined, AppstoreOutlined } from "@ant-design/icons";
import { useSelector, useDispatch } from "react-redux";
import { fetchProducts, selectProducts, selectPagination, selectActiveFilters, selectLoading } from "../../store/slices/productSlice";
import ProductTable from "./ProductTable";
import ProductGrid from "./ProductGrid";
import SingleProduct from "./SingleProduct";
import { get, post } from "../../axios";
// Keep importing the other icons used elsewhere in the component
import TickIcon from "../../../../public/v2/icons/tick-icon.svg";
import DeleteIcon from "../../../../public/v2/icons/delete-icon.svg";
import EditIcon from "../../../../public/v2/icons/edit-icon.svg";
import BulkassignIcon from "../../../../public/v2/icons/bulkassign-icon.svg";
import BulkuploadIcon from "../../../../public/v2/icons/Bulkupload-icon.svg";

const AllProducts = () => {
    const { props } = usePage();
    const dispatch = useDispatch();
    const productsData = useSelector(selectProducts);
    const pagination = useSelector(selectPagination);
    const activeFilters = useSelector(selectActiveFilters);
    const loading = useSelector(selectLoading);

    const [selectedProduct, setSelectedProduct] = useState(props.initialProduct || null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [viewMode, setViewMode] = useState("list"); // 'list' or 'grid'
    const [form] = Form.useForm();

    useEffect(() => {
        // If we have initialProduct from props, use it to set selectedProduct
        if (props.initialProduct) {
            setSelectedProduct(props.initialProduct);
        }

        // Only fetch products if we're showing the table view
        if (!props.initialProduct) {
            dispatch(
                fetchProducts({
                    page: pagination.current,
                    pageSize: pagination.pageSize,
                    filters: activeFilters,
                })
            );
        }
    }, [props.initialProduct, pagination.current, pagination.pageSize, activeFilters, dispatch]);

    const handlePaginationChange = (page, pageSize) => {
        dispatch(
            fetchProducts({
                page,
                pageSize,
                filters: activeFilters,
            })
        );
    };

    const handleProductSelect = async (product) => {
        try {
            // Update the URL without full page reload
            router.visit(`/v2/products/single/${product.id}`, {
                preserveState: true,
                only: ["initialProduct"],
            });

            // Fetch full product details if needed
           const response = await get(`products/${product.id}`);
            setSelectedProduct(response.product || product);
        } catch (error) {
            console.error("Error fetching product details:", error);
            setSelectedProduct(product);
        }
    };

    const handleBackToProducts = () => {
        // Update the URL to the products page
        router.visit("/v2/products/all-products", {
            preserveState: true,
        });
        setSelectedProduct(null);
    };

    const showModal = () => {
        console.log("Opening create product modal");
        setIsModalOpen(true);
    };

    const handleCancel = () => {
        form.resetFields();
        setIsModalOpen(false);
    };

    const handleSubmit = async (values) => {
        try {
            setIsSubmitting(true);
            // Make API request to create a new product with the provided name as SKU
            const response = await post("products", {
                sku: values.productName,
                name: values.productName,
                organization_id: null, // This will be set by the backend using the authenticated user
            });

            message.success("Product created successfully!");
            form.resetFields();
            setIsModalOpen(false);

            // Refresh products list to include the new product
            dispatch(
                fetchProducts({
                    page: pagination.current,
                    pageSize: pagination.pageSize,
                    filters: activeFilters,
                })
            );
        } catch (error) {
            console.error("Error creating product:", error);
            message.error(error.response?.data?.message || "Failed to create product");
        } finally {
            setIsSubmitting(false);
        }
    };

    // Toggle view mode between list and grid
    const toggleViewMode = (mode) => {
        setViewMode(mode);
    };

    // Determine which view to show
    const renderContent = () => {
        if (selectedProduct) {
            return <SingleProduct productId={selectedProduct.id} />;
        } else {
            return (
                <>
                    <Row gutter={[20, 20]} className="pb-[20px] ">
                        <Col span={12}>
                            <div>
                                <p className="text-[#252525] font-[600] text-[18px]">Manage Products</p>
                                <p className="text-[#626262] font-normal text-[14px]">Add, update and organize your product listing</p>
                            </div>
                        </Col>
                        <Col span={12}>
                            <div className="flex justify-end gap-[8px]">
                                <div className="flex justify-end">
                                    <Button
                                        className={`
                                            border shadow-none border-[#740898] rounded-r-none
                                            transition-colors duration-200
                                            ${
                                                viewMode === "list"
                                                    ? "bg-[#740898] text-white hover:bg-[#740898] hover:border-[#740898]"
                                                    : "bg-white text-black !hover:bg-[#740898] hover:border-[#740898]"
                                            }
                                        `}
                                        style={{ borderRight: "none" }}
                                        onClick={() => toggleViewMode("list")}
                                        icon={<UnorderedListOutlined style={{ fontSize: "18px" }} />}
                                    />
                                    <Button
                                        className={`
                                            rounded-l-none shadow-none border border-[#740898]
                                            transition-colors duration-200
                                            ${
                                                viewMode === "grid"
                                                    ? "bg-[#740898] text-white hover:bg-[#5c0779] hover:border-[#5c0779]"
                                                    : "bg-white text-black hover:bg-[#F9F5FB] hover:border-[#740898]"
                                            }
                                        `}
                                        style={{ borderLeft: "none" }}
                                        onClick={() => toggleViewMode("grid")}
                                        icon={<AppstoreOutlined style={{ fontSize: "18px" }} />}
                                    />
                                </div>
                                <div className="flex justify-end gap-[8px]">
                                    <Button className="h-[32px] border border-[#D9D9D9] rounded-[4px]">Export</Button>
                                    <Button className="h-[32px] border border-[#D9D9D9] rounded-[4px]">Import CSV</Button>
                                    <Button
                                        className="bg-[#740898] text-white border border-[#740898] rounded-[4px] create-product-button"
                                        style={{ position: "relative", zIndex: 0 }}
                                        onClick={(e) => {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            console.log("Create Product button clicked in ProductsContainer");
                                            showModal();
                                        }}
                                    >
                                        Create Product
                                    </Button>

                                    {/* Fallback HTML button that will definitely work */}
                                    <button
                                        className="bg-[#740898] text-white border border-[#740898] rounded-[4px] h-[32px] px-4"
                                        style={{ position: "relative", zIndex: 50, display: "none" }}
                                        onClick={(e) => {
                                            e.preventDefault();
                                            console.log("HTML button clicked");
                                            setIsModalOpen(true);
                                        }}
                                    >
                                        Create (HTML)
                                    </button>
                                </div>
                            </div>
                        </Col>
                        <Col span={24}>
                            {viewMode === "list" ? (
                                <ProductTable
                                    products={{ products: productsData }}
                                    onProductSelect={handleProductSelect}
                                    pagination={pagination}
                                    onPaginationChange={handlePaginationChange}
                                />
                            ) : (
                                <ProductGrid products={{ products: productsData }} onProductSelect={handleProductSelect} />
                            )}
                        </Col>
                    </Row>
                </>
            );
        }
    };

    return (
        <div className="h-full p-0 m-0">
            {renderContent()}

            {/* Create Product Modal */}
            <Modal
                title={<div className="text-2xl font-bold text-[#252525]">Create a New Product</div>}
                open={isModalOpen}
                onCancel={handleCancel}
                footer={null}
                closeIcon={null}
                className="rounded-xl"
                width={640}
                centered
                maskClosable={true}
                bodyStyle={{ paddingTop: "12px" }}
                zIndex={1050}
            >
                <div className="mb-4">
                    <p className="text-[#6B7280] text-sm">
                        Enter a product name to create a new product. This will be used as the product's SKU.
                    </p>
                </div>
                <Form form={form} layout="vertical" onFinish={handleSubmit} preserve={false} requiredMark={false}>
                    <Form.Item
                        name="productName"
                        label={<span className="font-semibold text-[#252525] block mb-0">Product Name*</span>}
                        rules={[
                            {
                                required: true,
                                message: "Please enter a product name",
                            },
                            {
                                min: 3,
                                message: "Product name must be at least 3 characters long",
                            },
                        ]}
                    >
                        <Input placeholder="Enter Name" className="rounded-sm h-8" autoFocus />
                    </Form.Item>

                    <Form.Item className="m-0 text-right">
                        <Space size="middle">
                            <Button onClick={handleCancel} className="rounded min-w-20">
                                Cancel
                            </Button>
                            <Button
                                type="primary"
                                htmlType="submit"
                                loading={isSubmitting}
                                className="bg-[#740898] border-[#740898] rounded min-w-20"
                            >
                                Save
                            </Button>
                        </Space>
                    </Form.Item>
                </Form>
            </Modal>
        </div>
    );
};

export default AllProducts;
