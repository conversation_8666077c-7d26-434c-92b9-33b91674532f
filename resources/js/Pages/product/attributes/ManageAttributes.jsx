import React, { useState, useRef } from "react";
import { <PERSON><PERSON>, <PERSON>, Col } from "antd";
import Attributes from "./Attributes";
import VariantAttributes from "./VariantAttributes";
import ProductOrganizationAttributes from "./ProductOrganizationAttributes";

// Add CSS for overriding the blue outline/border on pagination buttons
const paginationOverrideStyles = `
    .ant-pagination li,
    .ant-pagination button,
    .ant-pagination .ant-pagination-item-link,
    .ant-pagination-item a {
        outline: none !important;
    }

    .ant-pagination li:focus,
    .ant-pagination li:focus-visible,
    .ant-pagination button:focus,
    .ant-pagination button:focus-visible,
    .ant-pagination-item-link:focus,
    .ant-pagination-item-link:focus-visible {
        outline: none !important;
        box-shadow: none !important;
        border-color: inherit;
    }
`;

const ManageAttributes = () => {
    const [activeTab, setActiveTab] = useState("Attributes"); // State to manage active tab

    // Refs to access child components' methods
    const attributesRef = useRef();
    const variantAttributesRef = useRef();
    const productOrgAttributesRef = useRef();

    // Handle tab change
    const handleTabChange = (tab) => {
        setActiveTab(tab);
    };

    // Handle create attribute based on active tab
    const handleCreateAttribute = () => {
        switch (activeTab) {
            case "Variant Attributes":
                if (variantAttributesRef.current && variantAttributesRef.current.handleCreateAttribute) {
                    variantAttributesRef.current.handleCreateAttribute();
                }
                break;
            case "Product Organization Attributes":
                if (productOrgAttributesRef.current && productOrgAttributesRef.current.handleCreateAttribute) {
                    productOrgAttributesRef.current.handleCreateAttribute();
                }
                break;
            case "Attributes":
            default:
                if (attributesRef.current && attributesRef.current.handleCreateAttribute) {
                    attributesRef.current.handleCreateAttribute();
                }
                break;
        }
    };

    // Render the appropriate component based on active tab
    const renderTabContent = () => {
        switch (activeTab) {
            case "Variant Attributes":
                return <VariantAttributes ref={variantAttributesRef} />;
            case "Product Organization Attributes":
                return <ProductOrganizationAttributes ref={productOrgAttributesRef} />;
            case "Attributes":
            default:
                return <Attributes ref={attributesRef} />;
        }
    };

    return (
        <div className="p-0 rounded-lg">
            <style>{paginationOverrideStyles}</style>

            {/* Header Section */}
            <div className="mb-6">
                <Row justify="space-between" align="middle">
                    <Col>
                        <h1 className="m-0 text-xl font-semibold text-[#252525]">Manage Attribute Sets</h1>
                        <p className="mt-1 mb-0 text-sm text-[#6B7280]">
                            Organize your product attributes into sets for easier management.
                        </p>
                    </Col>
                    <Col>
                        <Button
                            type="primary"
                            className="bg-[#740898] border-[#740898] rounded font-medium"
                            onClick={handleCreateAttribute}
                        >
                            Create Attributes
                        </Button>
                    </Col>
                </Row>
            </div>

            {/* Tabs */}
            <div className="bg-white rounded-[4px] p-[4px] mb-4">
                <div className="flex gap-[4px]">
                    {["Attributes", "Variant Attributes", "Product Organization Attributes"].map((tab) => (
                        <div
                            key={tab}
                            onClick={() => handleTabChange(tab)}
                            className={`text-center py-2 p-[12px] cursor-pointer transition-colors duration-200 rounded-[4px] ${
                                activeTab === tab ? "bg-[#740898] text-white" : "bg-white text-[#626262]"
                            }`}
                        >
                            {tab}
                        </div>
                    ))}
                </div>
            </div>

            {/* Tab Content */}
            {renderTabContent()}
        </div>
    );
};

export default ManageAttributes;
