import React, { useRef } from "react";
import { Head, usePage } from "@inertiajs/react";
import { Button } from "antd";

// Use relative imports for assets
const Logo = "/v2/images/logo.png";
const GoogleIcon = "/v2/icons/google-icon.svg";
const LinkIcon = "/media/icons8-link-100.png";

export default function ManualLinking({ existingUser, formData }) {
    const { csrf_token } = usePage().props;
    const formRef = useRef(null);

    const handleLinkGoogle = () => {
        // Submit the hidden form
        if (formRef.current) {
            formRef.current.submit();
        }
    };

    const handleCancel = () => {
        window.location.href = route("home");
    };

    return (
        <div className="flex h-screen">
            <Head title="Link Account" />

            <div className="w-full bg-white flex flex-col justify-center items-center">
                <div className="max-w-md px-6 py-8 text-center">
                    <h1 className="text-2xl font-bold mb-10">Link Account</h1>

                    <div className="flex justify-center items-center mb-6 space-x-4">
                        <img src={GoogleIcon} alt="Google" className="h-10" />
                        <img src={LinkIcon} alt="Link" className="h-10" />
                        <img src={Logo} alt="Apimio" className="h-10" />
                    </div>

                    <p className="mb-8 text-gray-700">
                        Your account is already linked with Email. Please click continue to Re-authenticate and Connect with your Google
                        Account as well.
                    </p>

                    {/* Hidden form that will be submitted */}
                    <form ref={formRef} method="POST" action={route("link.user")} style={{ display: "none" }}>
                        <input type="hidden" name="_token" value={csrf_token} />
                        <input type="hidden" name="id" value={existingUser?.id || 0} />
                        <input type="hidden" name="password" value={formData?.password || ""} />
                    </form>

                    <Button
                        type="primary"
                        block
                        size="large"
                        onClick={handleLinkGoogle}
                        className="mb-4"
                        style={{ backgroundColor: "#740898", borderColor: "#740898" }}
                    >
                        Continue
                    </Button>

                    <div className="text-center">
                        <a href="#" onClick={handleCancel} className="text-blue-600 hover:underline">
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    );
}
