import React from 'react';

const ErrorFallback = ({ error, pageName }) => {
    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
                <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full">
                    <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                </div>
                <div className="mt-4 text-center">
                    <h3 className="text-lg font-medium text-gray-900">Page Load Error</h3>
                    <p className="mt-2 text-sm text-gray-500">
                        Sorry, we couldn't load the page "{pageName}". This might be a temporary issue.
                    </p>
                    {error && (
                        <details className="mt-4 text-left">
                            <summary className="text-sm text-gray-600 cursor-pointer">Technical Details</summary>
                            <pre className="mt-2 text-xs text-gray-500 bg-gray-100 p-2 rounded overflow-auto">
                                {error.toString()}
                            </pre>
                        </details>
                    )}
                    <div className="mt-6 flex space-x-3">
                        <button
                            onClick={() => window.location.reload()}
                            className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            Reload Page
                        </button>
                        <button
                            onClick={() => window.history.back()}
                            className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                        >
                            Go Back
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ErrorFallback;
