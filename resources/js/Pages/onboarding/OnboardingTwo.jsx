import React, { useState, useContext, useEffect } from "react";
import { Layout, Button, Checkbox, Row, Col } from "antd";
import { OnboardingContext } from "./OnboardingContext";

const { Content } = Layout;

const OnboardingTwo = () => {
    const { handleNext, handleBack, updateFormData, formData } = useContext(OnboardingContext);
    const [checkedItems, setCheckedItems] = useState({});

    const checkboxOptions = [
        { label: "Streamline product information management" },
        { label: "Increase sales by enhancing product listings" },
        { label: "Improve operational efficiency and reduce time on manual tasks" },
        { label: "Ensure consistent product information across all channels" },
        { label: "Expand product catalog and scale data management effectively" },
        { label: "Enhance team collaboration on product data" },
        { label: "Other" },
    ];

    // Pre-fill checkedItems from context if available
    useEffect(() => {
        if (formData.goals) {
            const initialChecked = {};
            formData.goals.split(";").forEach((goal) => {
                initialChecked[goal] = true;
            });
            setCheckedItems(initialChecked);
        }
    }, [formData.goals]);

    const handleCheckboxChange = (e, label) => {
        setCheckedItems((prevState) => ({
            ...prevState,
            [label]: e.target.checked,
        }));
    };

    const handleNextClick = () => {
        // Extract selected goals
        const selectedGoals = Object.keys(checkedItems)
            .filter((label) => checkedItems[label])
            .join(";");

        // Update context with selected goals
        updateFormData("goals", selectedGoals);

        // Proceed to next step
        handleNext();
    };

    const handleSkip = () => {
        // Update context with empty goals
        updateFormData("goals", "");

        // Proceed to next step
        handleNext();
    };

    const toggleCheckbox = (label) => {
        setCheckedItems((prevState) => ({
            ...prevState,
            [label]: !prevState[label],
        }));
    };

    return (
        <div className="flex-1 px-[70px] py-[40px] pb-[120px] bg-[#F8F9FA]">
            <h2 className="xl:text-[40px] sm:text-xl text-center font-bold pb-[10px]">What Are the Goals of Your Company using a PIM?</h2>
            <p className="text-gray-500 text-center mb-6">Select all that apply</p>
            <div className="p-5">
                <Row gutter={[20, 20]}>
                    {checkboxOptions.map((option, index) => (
                        <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={12} key={index}>
                            <div
                                className="bg-white p-4 rounded-[12px] flex items-start cursor-pointer"
                                onClick={() => toggleCheckbox(option.label)}
                            >
                                <Checkbox
                                    className="text-[#252525] text-[16px]"
                                    checked={checkedItems[option.label] || false}
                                    onChange={(e) => handleCheckboxChange(e, option.label)}
                                    // Prevent the click event from bubbling up to the parent div
                                    onClick={(e) => e.stopPropagation()}
                                ></Checkbox>
                                <span
                                    className={`pl-[14px] pt-[2px] text-[#252525] text-[16px] ${
                                        checkedItems[option.label] ? "text-[#740898] font-semibold" : ""
                                    }`}
                                >
                                    {option.label}
                                </span>
                            </div>
                        </Col>
                    ))}
                </Row>
            </div>

            <div className="flex justify-end mt-6 gap-[20px]">
                <Button onClick={handleBack}>Back</Button>
                <Button
                    type="primary"
                    onClick={handleNextClick}
                    // Optionally disable "Next" if no selection is made
                    disabled={Object.keys(checkedItems).filter((key) => checkedItems[key]).length === 0}
                >
                    Next
                </Button>
                <button className="text-[#740898] text-sm cursor-pointer" onClick={handleSkip}>
                    Skip for now
                </button>
            </div>
        </div>
    );
};

export default OnboardingTwo;
