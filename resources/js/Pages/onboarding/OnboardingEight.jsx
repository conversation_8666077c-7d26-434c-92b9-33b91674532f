import React, { useState, useContext } from "react";
import { Layout, Card, Timeline, Button, Checkbox, Row, Col, Form, Input, Select, Modal, Image } from "antd";
import { OnboardingContext } from "./OnboardingContext";
import RightArrow from "../../../../public/v2/icons/right-arrow.svg";
import OrganizationIconOne from "../../../../public/v2/icons/organization-icon-1.svg";
import OrganizationIconTwo from "../../../../public/v2/icons/organization-icon-2.svg";

const { Content } = Layout;

const OnboardingEight = () => {
    const { handleNext } = useContext(OnboardingContext);
    const [emailFields, setEmailFields] = useState([{ email: "" }, { email: "" }, { email: "" }, { email: "" }, { email: "" }]);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [organizationName, setOrganizationName] = useState("");

    const handleAddEmail = () => {
        setEmailFields([...emailFields, { email: "" }]);
    };

    const handleRemoveEmail = (index) => {
        const newFields = emailFields.filter((_, i) => i !== index);
        setEmailFields(newFields);
    };

    const handleEmailChange = (e, index) => {
        const newFields = [...emailFields];
        newFields[index].email = e.target.value;
        setEmailFields(newFields);
    };

    const handleCreateOrganization = () => {
        console.log("Creating organization:", organizationName);
        // You can add logic to handle creating the organization here
        setIsModalVisible(false); // Close the modal after submission
    };

    const organizations = [
        { id: 1, name: "Organization example 1", img: OrganizationIconOne },
        { id: 2, name: "Organization example 2", img: OrganizationIconTwo },
    ];

    return (
        <Layout>
            <Content className="flex flex-col md:flex-row">
                <div className="flex-1 px-[70px] py-[40px] pb-[120px] bg-[#F8F9FA] h-screen">
                    <h2 className="text-[40px] text-center font-bold pb-[10px]">Welcome John Doe!</h2>
                    <p className="text-gray-500 text-center mb-6">Select an organization to continue</p>
                    <div className="max-w-4xl mb-[20px] mx-auto p-[18px] justify-center flex bg-[white] rounded-[12px] border border-dashed border-[#D9D9D9]">
                        <Button
                            variant="link"
                            color="default"
                            className="m-0 p-0"
                            onClick={() => setIsModalVisible(true)} // Open modal on click
                        >
                            + Create New Organization
                        </Button>
                    </div>
                    {organizations.map((org) => (
                        <div
                            onClick={handleNext}
                            key={org.id}
                            className="max-w-4xl cursor-pointer  mx-auto mb-[20px] p-[18px] bg-[white] rounded-[12px] border border-[#D9D9D9]"
                        >
                            <div className="flex items-center">
                                <div className="w-1/2 flex items-center gap-[17px]">
                                    <Image src={org.img} alt="organization Icon" />
                                    <p className="font-[700] text-[16px]">{org.name}</p>
                                </div>
                                <div className="w-1/2 flex justify-end">
                                    <Image src={RightArrow} alt="right arrow" />
                                </div>
                            </div>
                        </div>
                    ))}

                    <div className="flex justify-center mt-6 gap-[30px]">
                        <Button
                            className="bg-[#740898] rounded-[4px] mt-[40px] font-[400] h-[32px] w-[90px] border border-[#740898] text-[#FFFFFF]"
                            onClick={() => (window.location.href = "/v2/register")}
                        >
                            Logout
                        </Button>
                    </div>
                </div>
            </Content>

            {/* Modal for creating organization */}
            <Modal title="Create Organization" visible={isModalVisible} onCancel={() => setIsModalVisible(false)} footer={null}>
                <div className="space-y-4">
                    <Form layout="vertical">
                        <Form.Item label="Organization Name" required>
                            <Input
                                value={organizationName}
                                onChange={(e) => setOrganizationName(e.target.value)}
                                placeholder="Enter Organization Name"
                            />
                        </Form.Item>
                        <div className="flex justify-end gap-4">
                            <Button onClick={() => setIsModalVisible(false)}>Cancel</Button>
                            <Button
                                type="primary"
                                onClick={handleCreateOrganization}
                                disabled={!organizationName} // Disable if name is empty
                            >
                                Create
                            </Button>
                        </div>
                    </Form>
                </div>
            </Modal>
        </Layout>
    );
};
export default OnboardingEight;
