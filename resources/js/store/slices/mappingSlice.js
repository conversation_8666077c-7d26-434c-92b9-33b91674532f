// src/slices/mappingSlice.js
import { createSlice } from '@reduxjs/toolkit';

const mappingSlice = createSlice({
  name: 'mapping',
  initialState : {
  mappingData: {
    nodes: []
    // any other top-level fields
  }
},
  reducers: {
    // This action will be used to set/update the mapping data.
    setMappingData: (state, action) => {
      state.data = action.payload;
    },
  },
});

export const { setMappingData } = mappingSlice.actions;
export default mappingSlice.reducer;
