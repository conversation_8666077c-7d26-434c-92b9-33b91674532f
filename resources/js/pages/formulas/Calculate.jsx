import React, { useState, useEffect } from "react";
import { Input, Form } from "antd";
import SelectAttribute from "../components/SelectAttribute";

const Calculate = ({ node, convertedOutputArray, onFieldChange }) => {
  const [With, setWith] = useState(node.with || "");
  const [attributeOne, setAttributeOne] = useState(node.to?.[0] || null);

  const updateParent = (newWith, newAttr1) => {
    // Only proceed if all new values exist:
    if (newWith && newAttr1) {
      // Make a shallow copy of the old from array if it exists, or start fresh:

      // Call onFieldChange with the full newValue object:
      onFieldChange(node.id, {
        with: newWith, // "with" field
        // or "merge" if that’s the default you always want
        to: [newAttr1],
      });
    }
  };

  const handleWithChange = (e) => {
    const newWith = e.target.value;
    setWith(newWith);
  };

  const handleWithBlur = () => {
    updateParent(With, attributeOne);
  };

  const handleAttributeOneChange = (val) => {
    setAttributeOne(val);
    updateParent(With, val);
  };

  return (
    <div className="flex gap-4">
      <div className="flex flex-col gap-1">
        <Form.Item label="With (e.g)" style={{ marginBottom: 0 }}>
          <Input
            style={{ width: 100 }}
            value={With} // ensures we see the local state
            onChange={handleWithChange}
            onBlur={handleWithBlur}
          />
        </Form.Item>
      </div>
      <div className="flex flex-col gap-1">
        <Form.Item label="Apimio Attribute" style={{ marginBottom: 0 }}>
          <SelectAttribute
            value={attributeOne} // ensure we pass the local attributeOne as the current value
            options={convertedOutputArray}
            onChange={handleAttributeOneChange}
          />
        </Form.Item>
      </div>
    </div>
  );
};

export default Calculate;
