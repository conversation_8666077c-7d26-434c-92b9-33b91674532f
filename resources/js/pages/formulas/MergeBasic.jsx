import React, { useState, useEffect } from "react";
import { Input, Select, Form } from "antd";
// import { useSelector } from "react-redux";
import SelectAttribute from "../components/SelectAttribute";

const MergeBasic = ({
  node,
  convertedOutputArray,
  convertedInputArray,
  onFieldChange,
}) => {
  const [Glue, setGlue] = useState(node.with || "");
  const [attributeOne, setAttributeOne] = useState(node?.from[1] || null);
  const [attributeTwo, setAttributeTwo] = useState(node.to?.[0] || null);
  // console.log("MergeBasic", node);
  // console.log("MergeBasic", convertedOutputArray);
  // const mappingData = useSelector((state) => state.mapping.data);
  // console.log("MergeBasic", mappingData?.input_array?.nodes);

  const updateParent = (newGlue, newAttr1, newAttr2) => {
    // Only proceed if all new values exist:
    if (newGlue && newAttr1 && newAttr2) {
      // Make a shallow copy of the old from array if it exists, or start fresh:
      const updatedFrom = node.from ? [...node.from] : [];

      // Ensure there is at least a 0th element in case node.from was empty:
      if (updatedFrom[0] === undefined) {
        updatedFrom[0] = "";
      }

      // Now place newAttr1 at index 1:
      updatedFrom[1] = newAttr1;

      // Call onFieldChange with the full newValue object:
      onFieldChange(node.id, {
        from: updatedFrom,
        with: newGlue, // "with" field
        with_formula: "merge", // or "merge" if that’s the default you always want
        to: [newAttr2],
      });
    }
  };

  const handleGlueChange = (e) => {
    const newGlue = e.target.value;
    setGlue(newGlue);
  };

  // Update parent when glue input loses focus
  const handleGlueBlur = () => {
    updateParent(Glue, attributeOne, attributeTwo);
  };

  const handleAttributeOneChange = (val) => {
    setAttributeOne(val);
    updateParent(Glue, val, attributeTwo);
  };

  const handleAttributeTwoChange = (val) => {
    setAttributeTwo(val);
    updateParent(Glue, attributeOne, val);
  };

  useEffect(() => {
    console.log("Local state in <Split>:", {
      Glue,
      attributeOne,
      attributeTwo,
    });
  }, [Glue, attributeOne, attributeTwo]);

  useEffect(() => {
    console.log("Split received node prop:", node);
  }, [node]);
  return (
    <div className="flex gap-4">
      <div className="flex flex-col gap-1">
        <Form.Item label="Glue" style={{ marginBottom: 0 }}>
          <Input
            style={{ width: 100 }}
            value={Glue} // ensures we see the local state
            onChange={handleGlueChange}
            onBlur={handleGlueBlur}
          />
        </Form.Item>
      </div>
      <div className="flex flex-col gap-1">
        <Form.Item label="CSV Attribute (1)" style={{ marginBottom: 0 }}>
          <SelectAttribute
            value={attributeOne} // ensure we pass the local attributeOne as the current value
            options={convertedInputArray}
            onChange={handleAttributeOneChange}
          />
        </Form.Item>
      </div>
      <div className="flex flex-col gap-1">
        <Form.Item label="Apimio Attribute (2)" style={{ marginBottom: 0 }}>
          <SelectAttribute
            value={attributeTwo} // ensure we pass the local attributeTwo as the current value
            options={convertedOutputArray}
            onChange={handleAttributeTwoChange}
          />
        </Form.Item>
      </div>
    </div>
  );
};

export default MergeBasic;
