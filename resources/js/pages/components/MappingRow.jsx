import React, { memo } from "react";
import { Form, Select, Button, message } from "antd";
import { ExclamationCircleOutlined, DeleteOutlined } from "@ant-design/icons";
import { FormulaCallingComponent } from "./FormulaCallingComponent";
import SelectAttribute from "./SelectAttribute";

const Formulas = [
  { value: "assign", label: "Assign" },
  { value: "split", label: "Split" },
  { value: "merge", label: "Merge Basic" },
  { value: "shortcode", label: "Merge Advance" },
  { value: "replace", label: "Replace" },
  { value: "slug", label: "Slug" },
  { value: "vlookUp", label: "VlookUp" },
  { value: "calculate", label: "Calculate" },
  { value: "expand", label: "Expand" },
];

const MappingRow = ({
  index,
  node,
  convertedInputArray = [],
  convertedOutputArray = [],
  onDelete,
  onFormulaChange,
  onFromFieldChange,
  onFieldChange,
  onCreateAttribute,
}) => {
  //  const [selectedFormula, setSelectedFormula] = useState(node?.with_formula || "assign");
  const selectedFormula = node.with_formula || "assign";
  const checkIfMapped = (node) => {
  switch (node.with_formula) {
    case "split":
      // The row is mapped if:
      // 1. `to` has 2 items.
      // 2. Both `to[0]` and `to[1]` have valid selections.
      // 3. Separator (with) has a value.
      return (
        node?.from?.length > 0 &&
        node?.to?.length === 2 &&
        node?.to?.[0] &&
        node?.to?.[1] &&
        node?.with && node?.with.trim() !== "" // Check if separator is filled
      );
    case "merge":
      // The row is mapped if:
      // 1. `from` has 2 items.
      // 2. `to` has 1 item.
      // 3. Glue (with) should be filled.
      return (
        node?.from?.length > 1 &&
        node?.to?.length > 0 &&
        node?.with && node?.with.trim() !== "" // Check if glue is filled
      );
    case "shortcode":
      // The row is mapped if:
      // 1. `from` has 1 item with content.
      // 2. `to` has 1 item.
      return (
        node?.from?.length > 0 &&
        node?.from?.[0] && node?.from?.[0].trim() !== "" &&
        node?.to?.length > 0
      );
    case "replace":
      // The row is mapped if:
      // 1. `from` has 1 item.
      // 2. `to` has 1 item.
      // 3. `replace` and `with` should be filled.
      return (
        node?.from?.length > 0 &&
        node?.to?.length > 0 &&
        node?.replace && node?.replace.trim() !== "" &&
        node?.with && node?.with.trim() !== ""
      );
    case "expand":
      // The row is mapped if:
      // 1. `from` has 1 item.
      // 2. `to` has 1 item.
      // 3. `with` should be filled.
      return (
        node?.from?.length > 0 &&
        node?.to?.length > 0 &&
        node?.with && node?.with.trim() !== "" // Ensure `with` is filled
      );
    case "calculate":
      // Similar to expand, calculate also requires `with` and `to`
      return (
        node?.from?.length > 0 &&
        node?.to?.length > 0 &&
        node?.with && node?.with.trim() !== ""
      );
    case "vlookUp":
      // The row is mapped if:
      // 1. `from` has 1 item (source value to look up)
      // 2. `to` has 1 item (destination field)
      // 3. `with` has a value (the lookup table ID)
      return (
        node?.from?.length > 0 &&
        node?.to?.length > 0 &&
        node?.with && node?.with.trim() !== ""
      );
    case "slug":
      // The row is mapped if:
      // 1. `from` has 1 item.
      // 2. `to` has 1 item.
      return node?.from?.length > 0 && node?.to?.length > 0;
    case "assign":
      // The row is mapped if:
      // 1. `from` has 1 item.
      // 2. `to` has 1 item.
      return node?.from?.length > 0 && node?.to?.length > 0;
    default:
      return false;
  }
};


  const isMapped = checkIfMapped(node);

  const handleFromChange = (value) => {
    onFromFieldChange(node.id, value);
  };
  const handleFormulaChange = (value) => {
    onFormulaChange(node.id, value);
  };

  const HandleFieldChange = (rowId, newValue) => {
    onFieldChange(rowId, newValue);
  };
  const handleDelete = () => {
    onDelete(node.id);
     message.success("Row deleted successfully");
  };

  const FormulaComponent = FormulaCallingComponent[selectedFormula] || null;

  return (
    <div
      id={node.id}
      className={`mapping-item relative px-4 pt-2 pb-2 justify-content-between border-l-8 border-t border-r border-b
        ${
          isMapped ? "border-green-500" : "border-yellow-500"
        }
        rounded-md shadow-md
      `}
    >
      <Button
        type="text"
        icon={<DeleteOutlined style={{ color: "#ff4d4f", fontSize: "16px" }} />}
        className="absolute top-1 right-4"
        onClick={handleDelete} // attach your delete handler here
      />
      <div className="flex justify-start gap-8">
        {selectedFormula !== "shortcode" && (
          <div className="flex flex-col gap-1 mb-0">
            {/*<label className="font-semibold">CSV Attribute</label>*/}

            <Form.Item
              key={index}
              label={`CSV Attribute`}
              //name={`nodes[data][${index}][from][]`} // Dynamic name handling
              style={{ marginBottom: "0" }}
            >
              <SelectAttribute
                options={convertedInputArray}
                value={node?.from[0]} // or node?.from if multi
                onChange={handleFromChange}
              />
            </Form.Item>
          </div>
        )}
        <div className="flex flex-col gap-1">
          {/*<label className="font-semibold">Formulas</label>*/}
          <Form.Item
            key={index}
            label={`Formulas`}
            // name={`nodes[data][${index}][with_formula]`} // Dynamic name handling
            style={{ marginBottom: "0" }}
          >
            <Select
              style={{
                width: 180,
              }}
              value={selectedFormula} // controlled
              onChange={handleFormulaChange}
              options={Formulas}
            />
          </Form.Item>
        </div>
        {FormulaComponent && (
          <FormulaComponent
            rowIndex={index}
            node={node}
            onFieldChange={HandleFieldChange}
            convertedOutputArray={convertedOutputArray}
            convertedInputArray={convertedInputArray}
          />
        )}
      </div>
      {!isMapped && (
        <div className="flex justify-between items-center ">
          <p className="flex items-center text-xs text-yellow-500 mt-1">
            <ExclamationCircleOutlined
              style={{ color: "#F59E0B", fontSize: "15px", marginRight: "4px" }}
            />
            Please map all required fields for this row.
          </p>
          <Button
            type="text"
            style={{ color: "#740898", backgroundColor: "transparent" }}
            onClick={() => onCreateAttribute(node.id)}
          >
            Create Apimio Attribute
          </Button>
        </div>
      )}
    </div>
  );
};

export default MappingRow;
