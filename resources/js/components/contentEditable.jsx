import React, { useEffect, useRef } from "react";
import { useDispatch } from "react-redux";
import { updateNode } from "../reduxStore/nodeSlice"; // Redux action to update the node state
import { <PERSON><PERSON> } from "antd";

// // Utility function to convert content to base64
// const toBase64 = (string) => {
//   return btoa(unescape(encodeURIComponent(string)));
// };

// // Utility to decode base64 if needed (optional)
// const fromBase64 = (encoded) => {
//   return decodeURIComponent(escape(atob(encoded)));
// };

const ContentEditableField = ({
  data,
  initialValue,
  index,
  oncloseField,
  oncloseFieldClose,
}) => {
  const defaultNodeName = Object.keys(data.input_array.nodes);
  let originalValue = initialValue;
  const handleClearContent = () => {
    const shortCodeDiv = document.querySelector(`#editable-content-${index}`);
    if (shortCodeDiv) {
      shortCodeDiv.innerHTML = ""; // Clear the content of the contentEditable div
      initialValue = ""; // Optionally update the initial value if needed
    }
  };
  const handleContentEditableChange = (e) => {
    let updatedContent = e.target.innerHTML.trim();

    if (
      updatedContent === "<div><br></div>" ||
      updatedContent === "<br>" ||
      updatedContent === "<span><br></span>"
    ) {
      updatedContent = ""; // Clear out the unwanted elements
      console.log(updatedContent, "updatedContent");
    }
    const contentEditableDiv = document.querySelector(
      `#editable-content-${index}`
    );
    const spans = contentEditableDiv.querySelectorAll("span");
    spans.forEach((span) => {
      span.removeAttribute("style");
    });
  };
  const restoreFocus = () => {
    // Get the contenteditable element
    const contentEditableElement = document.getElementById(
      `editable-content-${index}`
    );

    if (contentEditableElement) {
      contentEditableElement.focus(); // Focus on the element

      // Create a range object and set the start and end points to the end of the content
      const range = document.createRange();
      const selection = window.getSelection();

      // Clear any existing selections
      selection.removeAllRanges();

      // Set the range to the end of the content
      range.selectNodeContents(contentEditableElement);
      range.collapse(false); // Collapse to the end
      // Apply the new range
      selection.addRange(range);
    }
  };

  const handleInsertElement = (elementValue) => {
    const { mergeAdvValue } = elementValue;
    let cleanValue = "";

    // Check if mergeAdvValue exists and is a string
    if (mergeAdvValue && typeof mergeAdvValue === "string") {
      // Use a regex to clean up unwanted characters, if needed
      cleanValue = mergeAdvValue.replace(/^[{]{2}|[}]{2}$/g, "");
    }

    // Ensure cleanValue is a valid string
    if (typeof cleanValue === "string") {
      // Insert the cleaned value into the content-editable area
      const shortCodeDiv = document.querySelector(`#editable-content-${index}`);
      if (shortCodeDiv) {
        const spanElement = document.createElement("span");
        spanElement.setAttribute("contenteditable", "false");
        spanElement.className = "element_tags";
        spanElement.innerHTML = "{{" + cleanValue + "}}";

        if (
          shortCodeDiv.innerHTML === "<div><br></div>" ||
          shortCodeDiv.innerHTML === "<br>"
        ) {
          shortCodeDiv.innerHTML = ""; // Clear out the unwanted elements
        }

        // Insert the span at the caret position
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          const selectedText = range.toString();
          if (selectedText.trim() === "") {
            // If no text is selected, just insert the span
            range.insertNode(spanElement);
          } else {
            // If there is selected text, replace it
            range.deleteContents(); // Delete the current selection (if any)
            range.insertNode(spanElement); // Insert the new span element at the caret position
          }
        } else {
          // Fallback: Append to the end if no selection
          shortCodeDiv.appendChild(spanElement);
        }

        // Update the hidden field and local state
        const updatedHtml = shortCodeDiv.innerHTML;
        const encodedHtml = btoa(updatedHtml);

        initialValue = updatedHtml;
        setTimeout(() => {
          restoreFocus();
        }, 10);
      }
    }
  };

  const handleContentEditableKeyDown = (e) => {
    // Get the current selection
    const selection = window.getSelection();
    if (!selection.rangeCount) return;

    const range = selection.getRangeAt(0);
    const caretPosition = range.startOffset;

    if (e.key === "Backspace") {
      if (selection.isCollapsed) {
        const caretPosition = range.startOffset;

        const lastChild = range.startContainer.previousSibling;

        // Check if the previous sibling is a span with the class 'element_tags'
        if (
          lastChild &&
          lastChild.nodeType === Node.ELEMENT_NODE &&
          lastChild.tagName === "SPAN" &&
          lastChild.classList.contains("element_tags") &&
          caretPosition === 0
        ) {
          e.preventDefault();

          lastChild.remove();

          // Maintain focus
          const newRange = document.createRange();
          const newSelection = window.getSelection();
          newRange.setStart(range.startContainer, 0);
          newRange.collapse(true);
          newSelection.removeAllRanges();
          newSelection.addRange(newRange);
        }
      }
    } else if (e.key === "Enter") {
      const shortCodeDiv = document.querySelector(`#editable-content-${index}`);
      e.preventDefault(); // Prevent the default Enter key behavior

      // Create a new <div> with a <br> to allow a line break
      const newDiv = document.createElement("div");
      newDiv.innerHTML = "<br>"; // Ensure the new div is empty but contains a line break

      // Get the current selection and range
      const selection = window.getSelection();
      const range = selection.getRangeAt(0);

      // Make sure we are inserting only inside the contenteditable div, not nested within its children
      let parentDiv = range.startContainer;
      while (parentDiv && parentDiv !== shortCodeDiv) {
        parentDiv = parentDiv.parentNode;
      }

      // If we are inside the main contenteditable div and not a child div
      if (parentDiv === shortCodeDiv) {
        // Insert the new div at the current position
        range.insertNode(newDiv);

        // Move the cursor to the new div
        const newRange = document.createRange();
        newRange.setStart(newDiv, 0);
        newRange.collapse(true);
        selection.removeAllRanges();
        selection.addRange(newRange);
      }
    }
  };

  const handleSaveValue = () => {
    const shortCodeDiv = document.querySelector(`#editable-content-${index}`);
    if (
      shortCodeDiv.innerHTML === "<div><br></div>" ||
      shortCodeDiv.innerHTML === "<br>"
    ) {
      shortCodeDiv.innerHTML = ""; // Clear out the unwanted elements
    }
    let innerHTML = shortCodeDiv.innerHTML.trim();

    // Regex pattern to match <div><br></div> at the end or <br> at the end
    const divBrPattern = /<div><br><\/div>$/;
    const brPattern = /<br>$/;

    // Check if the last element is <br> or <div><br></div>
    if (divBrPattern.test(innerHTML)) {
      // Remove <div><br></div> at the end
      shortCodeDiv.innerHTML = innerHTML.replace(divBrPattern, "");
    } else if (brPattern.test(innerHTML)) {
      // Remove <br> at the end
      shortCodeDiv.innerHTML = innerHTML.replace(brPattern, "");
    }
    const updatedHtml = shortCodeDiv.innerHTML;
    console.log(updatedHtml, "");
    oncloseField(updatedHtml);
  };
  const handleCloseValue = () => {
    const updatedHtml = originalValue;
    oncloseFieldClose(updatedHtml);
  };
  return (
    <div className="absolute w-[40%] left-[0%] top-[-60px] z-[9999]  ">
      <div className="flex flex-wrap  mx-auto bg-[#ccc] p-3 rounded-md border-2 border-[#1677ff]">
        <button
          type="button"
          className="absolute right-[-5px] top-[-15px]"
          data-bs-dismiss="modal"
          aria-label="Close"
          onClick={handleCloseValue}
        >
          <div className="flex justify-center items-center w-[25px] h-[25px] btn-sm bg-[#1677ff] text-white rounded-full">
            <span aria-hidden="true">×</span>
          </div>
        </button>
        <div className="w-full">
          <div className="form-group">
            <div
              id={`editable-content-${index}`}
              tabIndex="0"
              contentEditable="true"
              className="form-control short_code_div bg-white-smoke max-h-[100px]"
              data-placeholder="Enter your content with multiple elements"
              onInput={handleContentEditableChange}
              onKeyDown={handleContentEditableKeyDown}
              dangerouslySetInnerHTML={{ __html: initialValue }}
            ></div>

            <div className="d-flex justify-content-between mt-2">
              <div>
                <button
                  data-bs-toggle="tooltip"
                  data-bs-placement="top"
                  title="Clear Content"
                  type="button"
                  className="bg-transparent border-0 d-block clear_content mr-2 mt-2"
                  onClick={handleClearContent}
                >
                  <i className="fa fa-refresh" aria-hidden="true"></i>
                </button>
              </div>
              <div>
                <div className="dropdown">
                  <button
                    className="btn dropdown-toggle insert_element_title"
                    type="button"
                    id="dropdownMenuButton"
                    data-bs-toggle="dropdown"
                    aria-haspopup="true"
                    aria-expanded="false"
                  >
                    {"{ }"} Insert Element
                  </button>

                  <div
                    className="dropdown-menu insert_element_main  bg-white-smoke max-h-[200px] overflow-scroll"
                    aria-labelledby="dropdownMenuButton"
                    id={`editable-content-${index + 1}`}
                  >
                    {data.data_required.template_method_type === "import" ? (
                      <div>
                        <h6 className="dropdown-header insert_element_head">
                          {defaultNodeName || "Others"}
                        </h6>
                        <div className="insert_element_content"></div>
                        {Object.keys(data.input_array.nodes.Default).map(
                          (inputKey, optionIndex) => {
                            const fullValue =
                              defaultNodeName +
                              "," +
                              data.input_array.nodes.Default[inputKey];
                            const mergeAdvValue = "{{" + fullValue + "}}";
                            return (
                              <button
                                key={optionIndex}
                                className="dropdown-item insert_element"
                                value={"{{" + fullValue + "}}"}
                                onClick={() =>
                                  handleInsertElement({ mergeAdvValue })
                                }
                              >
                                {data.input_array.nodes.Default[inputKey]}
                              </button>
                            );
                          }
                        )}
                      </div>
                    ) : (
                      <div>
                        <div className="insert_element_content"></div>
                        {data.input_array.nodes.map(
                          (familyNode, familyIndex) => (
                            <>
                              <h6
                                className="dropdown-header insert_element_head"
                                key={familyIndex}
                              >
                                {familyNode.attributes &&
                                Object.keys(familyNode.attributes).length > 0
                                  ? familyNode.name || "Others"
                                  : familyNode.name}
                              </h6>
                              {familyNode.attributes &&
                                Object.entries(familyNode.attributes).map(
                                  (
                                    [attributeName, attributeValue],
                                    inputIndex
                                  ) => {
                                    let familyInputValue;
                                    familyInputValue = `${familyNode.name},${attributeName}`;
                                    const mergeAdvValue =
                                      "{{" + familyInputValue + "}}";
                                    return (
                                      <button
                                        value={"{{" + familyInputValue + "}}"}
                                        className="dropdown-item insert_element"
                                        key={inputIndex}
                                        onClick={() =>
                                          handleInsertElement({
                                            mergeAdvValue,
                                          })
                                        }
                                      >
                                        {attributeValue}
                                      </button>
                                    );
                                  }
                                )}
                            </>
                          )
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex flex-wrap justify-end w-full action-buttons">
                <Button
                  className="btn-outline-danger mr-2"
                  onClick={handleCloseValue}
                >
                  Cancel
                </Button>
                <Button type="primary" className="" onClick={handleSaveValue}>
                  Apply
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContentEditableField;
