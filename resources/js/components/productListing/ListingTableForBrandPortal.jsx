import React, { useEffect, useState } from "react";
import ReactD<PERSON> from "react-dom/client";
import axios from "axios";
import Filters from "./Filters";
import { getProgressColor } from "./utils/getProgressBarColor";
import { SearchOutlined, CloseOutlined } from "@ant-design/icons";
import { Table, Button, message, Progress, Modal, Radio, Input } from "antd";
import BrandsPortalFilters from "./BrandsPortalFilters";

export default function ListingTableForBrandPortal({ filteredData, filteredPagination, csvKey, templates, token, organizationId }) {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 30,
        total: 0,
    });
    const [filtersApplied, setFiltersApplied] = useState(false);
    const [finalizedFilters, setFinalizedFilters] = useState(null);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [allSelectedProductIds, setAllSelectedProductIds] = useState([]);
    const [deleteState, setDeleteState] = useState(false);
    const [searchValue, setSearchValue] = useState("");
    const [isDownloadVisible, setIsDownloadVisible] = useState(true);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isConfirmationVisible, setIsConfirmationVisible] = useState(false);
    const [selectedTemplate, setSelectedTemplate] = useState("");
    const [email, setEmail] = useState("");
    const [responseFilters, setResponseFilters] = useState([]);
    useEffect(() => {
        fetchData();
    }, [filteredData, filteredPagination, deleteState]);
    // useEffect(() => {
    //     // Update isDownloadVisible whenever filters or selectedRowKeys change
    //     setIsDownloadVisible(selectedRowKeys.length > 0);
    // }, [selectedRowKeys, filtersApplied]);

    const fetchData = (page = 1, pageSize = 30, filters = null) => {
        const urlPath = window.location.pathname;
        const key = urlPath.split("/show/")[1];
        setLoading(true);
        let url = `/api/brands-portal-products?&paginate=${pageSize}&page=${page}`;

        axios
            .post(
                url,
                { filters: [] },
                {
                    headers: {
                        "Content-Type": "application/json",
                    },
                    params: { id: key, tableSearch: searchValue ?? null },
                }
            )
            .then((response) => {
                setData(response.data.data.products);
                setPagination({
                    current: response.data.pagination.current_page,
                    pageSize: response.data.pagination.per_page,
                    total: response.data.pagination.total,
                });
            })
            .catch(() => {
                message.error("Server Busy, Try again Later");
            })
            .finally(() => {
                setLoading(false);
            });
    };

    // const fetchAllProductIds = () => {
    //     const urlPath = window.location.pathname;
    //     const key = urlPath.split("/show/")[1];
    //     let url = `/api/brands-portal-products?&paginate=${pagination.total}&page=1`;

    //     axios
    //         .get(url, {
    //             params: { id: key, ...finalizedFilters, tableSearch: searchValue ?? null },
    //         })
    //         .then((response) => {
    //             const allIds = response.data.data.map((item) => item.id);
    //             setSelectedRowKeys(allIds);
    //         })
    //         .catch(() => {
    //             message.error("Failed to select all products. Please try again later.");
    //         });
    // };

    const handleTableChange = (pagination) => {
        const { current, pageSize } = pagination;
        fetchData(current, pageSize, finalizedFilters);
    };

    const clearFilters = () => {
        setFinalizedFilters(null);
        setResponseFilters(null);
        setFiltersApplied(false);
        setAllSelectedProductIds([]);
        fetchData();
        message.success("Filter Cleared!!");
    };

    const onSelectChange = (newSelectedRowKeys) => {
        const updatedSelection = [...new Set([...allSelectedProductIds, ...newSelectedRowKeys])];
        setSelectedRowKeys(newSelectedRowKeys);
        setAllSelectedProductIds(updatedSelection);
        // setIsDownloadVisible(updatedSelection.length > 0);
    };

    const showDownloadModal = () => {
        setIsModalVisible(true);
    };

    const handleModalCancel = () => {
        setIsModalVisible(false);
        setSelectedTemplate("");
        setEmail("");
    };

    const handleDownload = () => {
        if (!selectedTemplate) {
            message.warning("Please select a template.");
            return;
        }
        if (!email) {
            message.warning("Please enter an email address.");
            return;
        }

        const selectedTemplateId = templates.find((template) => template.name === selectedTemplate)?.id;
        if (!selectedTemplateId) {
            message.error("Invalid template selected.");
            return;
        }

        const exportData =
            selectedRowKeys.length === pagination.total || selectedRowKeys.length === 0
                ? { filters: responseFilters }
                : { product_ids: allSelectedProductIds };

        axios
            .post(
                `/api/brands-portal-products/export`,
                {
                    email,
                    template_id: [selectedTemplateId],
                    ...exportData,
                },
                {
                    params: { brand_portal_id: csvKey },
                }
            )
            .then(() => {
                message.success("Export initiated! The file will be emailed to you shortly.");
            })
            .catch(() => {
                message.error("Failed to initiate export. Please try again later.");
            })
            .finally(() => {
                setIsModalVisible(false);
                setIsConfirmationVisible(true);

                // Clear all relevant states after download initiation
                setSelectedTemplate("");
                setEmail("");
                setSelectedRowKeys([]);
                setAllSelectedProductIds([]);
                setFinalizedFilters(null);
            });
    };

    const closeConfirmationModal = () => {
        setIsConfirmationVisible(false);
    };

    const handleInputChange = (e) => {
        setSearchValue(e.target.value);
    };

    const handleSearchClick = () => {
        fetchData(1, 30, finalizedFilters);
    };

    const columns = [
        {
            title: "Image",
            dataIndex: "file.link",
            key: "file",
            render: (text, record) => {
                const defaultImage = "/img/apimio_default.jpg";
                const imageSrc = record.file?.link || defaultImage;
                return <img src={imageSrc} alt={record.product_name} style={{ width: "40px", height: "40px", objectFit: "cover" }} />;
            },
        },
        {
            title: "Name of Product",
            dataIndex: "product_name",
            key: "product_name",
        },
        {
            title: "SKU",
            dataIndex: "sku",
            key: "sku",
            render: (text) => text || "-",
        },
        {
            title: "Scoring",
            dataIndex: "version_score",
            key: "version_score",
            render: (text, record) => (
                <Progress
                    style={{
                        width: "200px",
                    }}
                    percent={record.version_score}
                    size="small"
                    strokeColor={getProgressColor(record.version_score)}
                />
            ),
        },
        {
            title: "Brand",
            dataIndex: "brand",
            key: "brand",
        },
        {
            title: "Category",
            dataIndex: "category",
            key: "category",
        },
    ];
    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const scoreFilter = urlParams.get("score");
        // If you need to perform an action after `searchValue` changes
        if (searchValue === "") {
            fetchData(1, 30, finalizedFilters, null, scoreFilter); // Fetch with empty search
        }
    }, [searchValue]);
    const options = templates?.map((item) => ({ label: item.name, value: item.name }));
    const handleClearSearch = () => {
        // Clear the search value
        setSearchValue("");
        fetchData();
        clearFilters();
    };
    const selectAll = () => {
        // This will select all rows based on the data on the current page
        const allKeys = data.map((item) => item.id); // Use `id` as row key in this example
        setSelectedRowKeys(allKeys); // This will select all rows
    };
    const rowSelection = {
        selectedRowKeys,
        onChange: (newSelectedRowKeys) => {
            setSelectedRowKeys(newSelectedRowKeys);
        },
        // This is where you can customize selection options like "Select All"
        onSelectAll: (selected, selectedRows, changeRows) => {
            if (selected) {
                // Select all rows when "Select All" is clicked
                const allSelected = data.map((item) => item.id); // Get all ids for selection
                setSelectedRowKeys(allSelected);
            } else {
                // Deselect all rows when "Select All" is unchecked
                setSelectedRowKeys([]);
            }
        },
    };
    return (
        <div className="mb-5">
            <div className="d-flex justify-content-between">
                <div className="d-flex align-items-center w-full">
                    <div className="input-group mb-3 position-relative">
                        <input
                            type="text"
                            className="form-control "
                            placeholder="Search"
                            onChange={(e) => setSearchValue(e.target.value)}
                            value={searchValue}
                            aria-describedby="basic-addon2"
                            onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                    handleSearchClick();
                                }
                            }}
                        />
                        {searchValue.length > 0 && (
                            <CloseOutlined
                                style={{ position: "absolute", right: "45px", top: "10px", cursor: "pointer" }}
                                onClick={handleClearSearch}
                            />
                        )}
                        <span className="input-group-text bg-dark text-white" style={{ cursor: "pointer" }} onClick={handleSearchClick}>
                            <SearchOutlined />
                        </span>
                    </div>
                </div>
                <div className="d-flex">
                    {filtersApplied && (
                        <Button onClick={clearFilters} type="default" className="me-3">
                            Clear Filters
                        </Button>
                    )}
                    <BrandsPortalFilters
                        onFilteredData={(filteredData, filteredPagination, filters) => {
                            setData(filteredData.products);
                            setPagination(filteredPagination);
                            setFinalizedFilters(filters);
                            setFiltersApplied(true);
                            setResponseFilters(filters.length > 0 ? filters : []);
                        }}
                        endpoint="/api/brands-portal-products"
                        portalId={csvKey}
                        token={token}
                        organizationId={organizationId}
                    />
                </div>
            </div>
            <div className="mt-0">
                <Table
                    size="small"
                    columns={columns}
                    dataSource={data}
                    loading={loading}
                    rowKey={(record) => record.id}
                    rowSelection={{
                        selectedRowKeys,
                        onChange: onSelectChange,
                    }}
                    pagination={{
                        current: pagination.current,
                        pageSize: pagination.pageSize,
                        total: pagination.total,
                    }}
                    onChange={handleTableChange}
                />
            </div>

            <div
                className="bulk-footer"
                style={{
                    position: "fixed",
                    bottom: 0,
                    width: "-webkit-fill-available",
                    backgroundColor: "#f0f2f5",
                    padding: "10px",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                }}
            >
                <div>
                    <span>{`Selected ${selectedRowKeys.length} products`}</span>
                    <Button type="link" onClick={selectAll}>
                        Select All ({pagination.total})
                    </Button>
                </div>
                <Button color="default" variant="solid" style={{ backgroundColor: "black", color: "white" }} onClick={showDownloadModal}>
                    Download
                </Button>
            </div>

            <Modal
                title="Download CSV"
                visible={isModalVisible}
                onCancel={handleModalCancel}
                onOk={handleDownload}
                footer={[
                    <Button key="back" onClick={handleModalCancel}>
                        Cancel
                    </Button>,
                    <Button key="submit" type="primary" onClick={handleDownload}>
                        Download CSV
                    </Button>,
                ]}
            >
                <p>Select template</p>
                <Radio.Group options={options} value={selectedTemplate} onChange={(e) => setSelectedTemplate(e.target.value)} />
                <p className="mt-3">Email Address</p>
                <Input type="email" placeholder="Enter your email" value={email} onChange={(e) => setEmail(e.target.value)} />
            </Modal>

            <Modal
                visible={isConfirmationVisible}
                onCancel={closeConfirmationModal}
                footer={[
                    <Button key="ok" type="primary" onClick={closeConfirmationModal}>
                        OK, Thanks
                    </Button>,
                ]}
            >
                <div style={{ textAlign: "center" }}>
                    <div style={{ fontSize: "30px", color: "#52c41a", marginBottom: "20px" }}>✔️</div>
                    <h3>Email sent</h3>
                    <p>Your file is being processed and will be emailed to you shortly.</p>
                </div>
            </Modal>
        </div>
    );
}

const rootElement = document.getElementById("listingTable");

if (rootElement) {
    ReactDOM.createRoot(rootElement).render(<ListingTableForBrandPortal />);
}
