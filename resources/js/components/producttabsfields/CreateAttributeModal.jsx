import React, { useState, useEffect } from "react";
import axios from "axios";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
function CreateAttributeModal({
  index,
  allFamilies,
  allAttributes,
  onattrCloseModal,
  attributeCloseModal,
}) {
  console.log(index, "attributeIndex");
  const allAttributesinside = allAttributes || [];
  const allFamiliesinside = allFamilies || [];
  const [inputValue, setInputValue] = useState(""); // Track the input value
  var _token = $('meta[name="csrf-token"]').attr("content");
  const file_path = data.file_path;
  const template_method_type = data.data_required.template_method_type;
  const organization_id = data.data_required.organization_id;
  const [filteredFamilies, setFilteredFamilies] = useState([]);
  const [isFamilyListVisible, setFamilyListVisible] = useState(false); // Track the visibility of family list
  const [inputHiddenValue, setInputHiddenValue] = useState("");
  const [attrNameVal, setAttrNameVal] = useState("");
  const [attrTypeVal, setAttrTypeVal] = useState("");
  const [errors, setErrors] = useState({
    attributeFamilyName: "",
    attributeTitle: "",
    attributeType: "",
  });
  useEffect(() => {
    setFilteredFamilies(allFamilies);
  }, [allFamilies]);
  const handleInputChange = (e) => {
    const value = e.target.value;
    setInputValue(value); // Update the input value
    setInputHiddenValue(value);
    const inputValueLower = value.toLowerCase();
    const filtered = allFamiliesinside.filter((family) =>
      family.name.toLowerCase().includes(inputValueLower)
    );
    setFilteredFamilies(filtered);

    if (value) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        attributeFamilyName: "",
      }));
    }
  };
  const handleAttrName = (e) => {
    const value = e.target.value;
    setAttrNameVal(value);
    if (value) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        attributeTitle: "",
      }));
    }
  };
  const handleAttrType = (e) => {
    const value = e.target.value;
    setAttrTypeVal(value);
    if (value) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        attributeType: "",
      }));
    }
  };
  const handleInputFocus = () => {
    setFamilyListVisible(true); // Show family list when input is focused
  };

  const handleFamilyItemClick = (family) => {
    const familyName = family.name;
    const familyId = family.id;
    setInputValue(familyName); // Set the selected value in the visible input field
    setInputHiddenValue(familyName); // Set the selected value in the hidden input field
    setFamilyListVisible(false);
  };
  $(document).ready(function () {
    // Add a click event handler to the document
    $(document).on("click", function (event) {
      var $elements = $(".attribute-family");
      if (!($elements.is(event.target) || $elements.has(event.target).length)) {
        setFamilyListVisible(false);
      }
    });
  });
  const handleCloseModal = () => {
    attributeCloseModal();
  };

  const handleSubmit = async (e) => {
    e.preventDefault(); // Prevent default form submission

    // Initialize an object to hold error messages
    const newErrors = {
      attributeFamilyName: "",
      attributeTitle: "",
      attributeType: "",
    };
    if (!inputHiddenValue) {
      newErrors.attributeFamilyName = "Attribute family name is required.";
    }
    if (!attrNameVal) {
      newErrors.attributeTitle = "Attribute title is required.";
    }
    if (!attrTypeVal) {
      newErrors.attributeType = "Attribute type is required.";
    }

    // If there are errors, update the state and stop form submission
    if (
      newErrors.attributeFamilyName ||
      newErrors.attributeTitle ||
      newErrors.attributeType
    ) {
      setErrors(newErrors);
      return;
    }
    const formData = {
      organization_id,
      method_type: template_method_type,
      _token,
      attribute_family_name: inputHiddenValue,
      name: attrNameVal, // Change this to the actual input value
      attribute_type_id: attrTypeVal, // Change this to the actual value
    };
    axios.defaults.headers.common["X-CSRF-TOKEN"] = document
      .querySelector('meta[name="csrf-token"]')
      .getAttribute("content");

    axios
      .post("/products/attributes", formData)
      .then((response) => {
        toast.success(
          "Attribute created successfully. Kidnly Select it from the list."
        );
        // Assuming response.data.newArray contains the updated array data
        const updatedData = response.data.data;
        console.log(updatedData, "attribute create updated data");
        $("#mapping_create_new_attribute_success").removeClass("hidden");
        setTimeout(function () {
          $("#mapping_create_new_attribute_success").addClass("hidden");
        }, 3500);
        setAttrNameVal("");
        setAttrTypeVal("");
        setInputValue("");
        onattrCloseModal(updatedData, formData, index);
      })
      .catch((error) => {
        if (error.response && error.response.status === 404) {
          toast.error("Attribute already exists.");
        } else {
          toast.error("An unexpected error occurred.");
          console.log(error);
        }
        //attributeCloseModal();
      });
  };
  return (
    <div
      className="fixed w-full h-screen top-0 left-0 z-20 bg-[#303030ab]"
      id="create_attribute"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabIndex="-1"
      aria-labelledby="exampleModalLabel"
      aria-modal="true"
      role="dialog"
    >
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header">
            <h3 className="modal-title">Create Apimio Attribute</h3>
            <button
              type="button"
              className="btn-sm border-0 bg-white fs-24 px-0 pt-0 close-attribute-modal"
              data-bs-dismiss="modal"
              aria-label="Close"
              onClick={handleCloseModal}
            >
              <span aria-hidden="true">×</span>
            </button>
          </div>

          <div className="modal-body relative">
            <div id="mapping_create_new_attribute_main" className="formStyle">
              <div className="form-group relative">
                <label>
                  Attribute Family&nbsp;<span className="text-danger">*</span>
                </label>
                <input
                  type="text"
                  className="form-control hidden"
                  name="attribute_family_name"
                  placeholder=""
                  required=""
                  value={inputHiddenValue}
                  onChange={handleInputChange}
                />
                <input
                  type="text"
                  className="form-control attribute-family"
                  placeholder=""
                  required=""
                  onChange={handleInputChange}
                  onFocus={handleInputFocus}
                  value={inputValue}
                />
                {errors.attributeFamilyName && (
                  <span className="text-red-500">
                    {errors.attributeFamilyName}
                  </span>
                )}
                <div
                  className={`familylist absolute top-full left-0  bg-white w-full border border-t-0 border-gray-400 rounded-lg max-h-28 overflow-auto ${
                    isFamilyListVisible ? "" : "hidden"
                  }`}
                >
                  <ul id="family_list">
                    {filteredFamilies.map((family) => (
                      <li
                        key={family.id}
                        id={family.id}
                        onClick={() => handleFamilyItemClick(family)}
                        className="p-2 cursor-pointer hover:bg-gray-200"
                      >
                        {family.name}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              <div className="form-group mt-3">
                <label>
                  Attribute Title&nbsp;<span className="text-danger">*</span>
                </label>
                <input
                  type="text"
                  className="form-control attribute-name"
                  name="name"
                  placeholder=""
                  required=""
                  onChange={handleAttrName}
                  value={attrNameVal}
                />
                {errors.attributeTitle && (
                  <span className="text-red-500">{errors.attributeTitle}</span>
                )}
              </div>
              <div className="form-group mt-3">
                <label>
                  Attribute Type&nbsp;<span className="text-danger">*</span>
                </label>
                <select
                  className="form-control bg-white-smoke"
                  name="attribute_type_id"
                  required=""
                  onChange={handleAttrType}
                  value={attrTypeVal}
                >
                  <option value="">Choose</option>
                  {allAttributesinside.map((attribute) =>
                    attribute.id == 1 || attribute.id == 3 ? (
                      <option key={attribute.id} value={attribute.id}>
                        {attribute.name}
                      </option>
                    ) : (
                      ""
                    )
                  )}
                </select>
                {errors.attributeType && (
                  <span className="text-red-500">{errors.attributeType}</span>
                )}
              </div>
              <div className="form-group mt-4">
                <button
                  className="btn btn-primary btn-block"
                  id="mapping_create_new_attribute_btn"
                  onClick={handleSubmit}
                >
                  Create
                </button>
              </div>
            </div>
          </div>
          <div
            className="text-center hidden absolute top-0 left-0 z-20 h-full w-full"
            id="mapping_create_new_attribute_success"
          >
            <img
              src="https://cdn.dribbble.com/users/2185205/screenshots/7886140/media/90211520c82920dcaf6aea7604aeb029.gif"
              alt=""
              className="w-full"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default CreateAttributeModal;
