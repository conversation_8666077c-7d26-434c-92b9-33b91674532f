import React from "react";
import { useSelector, useDispatch } from "react-redux";
import { increment, decrement, reset, selectCount } from "../store/slices/counterSlice";

export default function Counter() {
    const count = useSelector(selectCount);
    const dispatch = useDispatch();

    return (
        <div className="flex flex-col items-center p-4 bg-white rounded-lg shadow-md">
            <h2 className="text-2xl font-bold mb-4">Redux Counter Example</h2>
            <div className="text-4xl font-bold mb-4">{count}</div>
            <div className="flex space-x-2">
                <button
                    onClick={() => dispatch(decrement())}
                    className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition"
                >
                    Decrease
                </button>
                <button onClick={() => dispatch(reset())} className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition">
                    Reset
                </button>
                <button
                    onClick={() => dispatch(increment())}
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition"
                >
                    Increase
                </button>
            </div>
        </div>
    );
}
