import React, { useState, useEffect } from "react";
import <PERSON>actD<PERSON> from "react-dom/client";
import { Form, Input, Button, Select, Upload, message, ColorPicker, Modal, Table, Tooltip } from "antd";
import { UploadOutlined, CopyOutlined, PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import axios from "axios";

const { Option } = Select;

const BrandPortal = () => {
    const [form] = Form.useForm();
    const [file, setFile] = useState(null);
    const [filePreview, setFilePreview] = useState(null);
    const [brandName, setBrandName] = useState("");
    const [url, setUrl] = useState("");
    const [template, setTemplate] = useState([]);
    const [store, setStore] = useState([]);
    const [storesFromBlade, setStoresFromBlade] = useState([]);
    const [templatesFromBlade, setTemplatesFromBlade] = useState(["template1", "template2", "template3"]);
    const [primaryColor, setPrimaryColor] = useState("#1890ff");
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isFormModalVisible, setIsFormModalVisible] = useState(false);
    const [brandPortals, setBrandPortals] = useState([]);
    const [pagination, setPagination] = useState({ total: 0, pageSize: 10, currentPage: 1 });
    const [editingId, setEditingId] = useState(null);
    const [existingFileId, setExistingFileId] = useState(null);
    const [loading, setLoading] = useState(false); // New loading state
      // const [organizationId, setOrganizationId] = useState(null);
    const brandPortalDiv = document.getElementById("brand-portal");

    const organizationId = JSON.parse(brandPortalDiv.dataset.organization);

    useEffect(() => {
        const brandPortalDiv = document.getElementById("brand-portal");
        const stores = JSON.parse(brandPortalDiv.dataset.stores);
        const templates = JSON.parse(brandPortalDiv.dataset.templates);

        setStoresFromBlade(stores);
        setTemplatesFromBlade(templates);
        getBrandPortals();
    }, [pagination.currentPage, pagination.pageSize]);

    useEffect(() => {
        if (isFormModalVisible) {
            form.setFieldsValue({
                brandName,
                template: templatesFromBlade.filter((template) => !template.id).map((template) => template.name), // Set default templates without id in form
                primaryColor,
            });
        }
    }, [editingId, isFormModalVisible, brandName, store, primaryColor]);

    const getBrandPortals = async () => {
        try {
            const response = await axios.get(
                `/api/2024-12/brands-portals?paginate=${pagination.pageSize}&page=${pagination.currentPage}&organization_id=${organizationId}`
            );
            setBrandPortals(response.data.data);
            setPagination((prev) => ({
                ...prev,
                total: response.data.pagination.total,
                currentPage: response.data.pagination.current_page,
            }));
        } catch (error) {
            message.error("Error fetching brand portals.");
        }
    };

    const fetchBrandPortal = async (id) => {
        try {
            const response = await axios.get(`/api/2024-12/brands-portal/${id}`);
            const data = response.data.data;
            setBrandName(data.name);
            setUrl(data.url);
            setTemplate(data.templateIds || []);
            setStore(data.channelIds || []);
            setPrimaryColor(data.primary_color || "#1890ff");
            setFilePreview(data.logo_url || null);
            setExistingFileId(data.file_id);

            form.setFieldsValue({
                brandName: data.name,
                template: data.templateIds,
                store: data.channelIds,
                primaryColor: data.primary_color,
            });
        } catch (error) {
            message.error("Failed to load brand portal data.");
        }
    };

    const handleFileChange = (info) => {
        const fileObject = info.file.originFileObj || info.file;
        if (fileObject) {
            setFile(fileObject);
            if (filePreview) URL.revokeObjectURL(filePreview);
            setFilePreview(URL.createObjectURL(fileObject));
        } else {
            setFile(null);
        }
    };
    const handleCopy = (url) => {
        navigator.clipboard.writeText(url);
        message.success("URL copied to clipboard!");
    };
    const handleSubmit = async () => {
        setLoading(true); // Disable the button when the query starts
        const formData = new FormData();
        formData.append("name", brandName);
        formData.append("template[]", JSON.stringify(template));
        formData.append("store[]", JSON.stringify(store));
        formData.append("primary_color", primaryColor);

        if (file) {
            formData.append("file", file);
        } else if (existingFileId) {
            formData.append("file_id", existingFileId);
        }

        try {
            const endpoint = editingId ? `/api/2024-12/brands-portal/${editingId}` : "/api/2024-12/brands-portals";
            const response = await axios.post(endpoint, formData, {
                headers: { "Content-Type": "multipart/form-data" },
            });
            message.success(editingId ? "Brand portal updated successfully!" : "Brand portal added successfully!");
            setIsModalVisible(true);
            setIsFormModalVisible(false);
            getBrandPortals();
            resetForm();
            setUrl(response.data.data.url);
        } catch (error) {
            message.error("Error saving brand portal. Please try again.");
        } finally {
            setLoading(false); // Re-enable the button when the query completes
        }
    };

    const handleColorPickerChange = (color) => {
        setPrimaryColor(color.toHexString());
    };

    const handleAddPortal = () => {
        resetForm();
        setEditingId(null);
        setIsFormModalVisible(true);
    };

    const handleEdit = async (record) => {
        try {
            await fetchBrandPortal(record.id);
            setEditingId(record.id);
            setIsFormModalVisible(true);
        } catch (error) {
            message.error("Error fetching brand portal details for editing.");
        }
    };

    const handleDelete = async (id) => {
        try {
            await axios.delete(`/api/2024-12/brands-portals/${id}`);
            message.success("Brand portal deleted successfully.");
            getBrandPortals();
        } catch (error) {
            message.error("Error deleting brand portal. Please try again.");
        }
    };

    const resetForm = () => {
        setBrandName("");
        setTemplate([]);
        setStore([]);
        setFile(null);
        setFilePreview(null);
        setPrimaryColor("#1890ff");
        setExistingFileId(null);
        form.resetFields();
    };

    const handleCloseFormModal = () => {
        setIsFormModalVisible(false);
        resetForm();
    };

    const handleTableChange = (pagination) => {
        setPagination({
            ...pagination,
            currentPage: pagination.current,
            pageSize: pagination.pageSize,
        });
    };

    const columns = [
        {
            title: "Logo",
            dataIndex: "logo_url",
            key: "logo",
            render: (text) =>
                text ? <img src={text} alt="Logo" style={{ width: "100px", height: "50px", objectFit: "contain" }} /> : "N/A",
        },
        {
            title: "Brand Name",
            dataIndex: "name",
            key: "name",
        },
        {
            title: "URL",
            dataIndex: "url",
            key: "url",
            render: (text) => (
                <Tooltip title={text}>
                    <a href={text} target="_blank" rel="noopener noreferrer">
                        {text ? `${text.slice(0, 30)}...` : "N/A"}
                    </a>
                </Tooltip>
            ),
        },
        {
            title: "Copy URL",
            dataIndex: "url",
            key: "url",
            render: (text, record) => (
                <Button icon={<CopyOutlined />} onClick={() => handleCopy(record.url)} style={{ marginLeft: "8px" }} />
            ),
        },

        {
            title: "Actions",
            key: "actions",
            render: (text, record) => (
                <div style={{ display: "flex", gap: "8px" }}>
                    <Button icon={<EditOutlined />} onClick={() => handleEdit(record)} />
                    <Button
                        icon={<DeleteOutlined />}
                        danger
                        onClick={() => {
                            Modal.confirm({
                                title: "Confirm Delete",
                                content: "Are you sure you want to delete this brand portal?",
                                okText: "Yes",
                                okType: "danger",
                                cancelText: "No",
                                onOk: () => handleDelete(record.id),
                            });
                        }}
                    />
                </div>
            ),
        },
    ];

    const handleModalClose = () => {
        setIsModalVisible(false);
        resetForm();
    };
    
    return (
        <div style={{ padding: "24px", width: "100%" }}>
            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "16px" }}>
                <h2>Brand Portals</h2>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAddPortal}>
                    Add Brand Portal
                </Button>
            </div>
            <Table
                dataSource={brandPortals}
                columns={columns}
                rowKey="id"
                pagination={{
                    current: pagination.currentPage,
                    pageSize: pagination.pageSize,
                    total: pagination.total,
                    onChange: (page, pageSize) => setPagination({ currentPage: page, pageSize }),
                }}
                onChange={handleTableChange}
            />

            <Modal
                title={editingId ? "Edit Brand Portal" : "Add Brand Portal"}
                visible={isFormModalVisible}
                onCancel={handleCloseFormModal}
                footer={null}
            >
                <Form layout="vertical" form={form}>
                    <Form.Item name="brandName" label="Brand Name">
                        <Input value={brandName} onChange={(e) => setBrandName(e.target.value)} />
                    </Form.Item>
                    <Form.Item name="template" label="Select Template">
                        <Select
                            mode="multiple"
                            defaultValue={templatesFromBlade.map((template) => template.id)} // Match 'id' with 'value'
                            value={template} // Controlled component state
                            onChange={(value) => {
                                // Filter out any templates that do not have an id
                                const validTemplates = value.filter((templateId) =>
                                    templatesFromBlade.some((template) => template.id === templateId)
                                );
                                setTemplate(validTemplates); // Update state with valid templates only
                            }}
                            style={{ width: "100%" }}
                        >
                            {templatesFromBlade.map((template, index) => (
                                <Option key={index} value={template.id} disabled={!template.id}>
                                    {template.name}
                                </Option>
                            ))}
                        </Select>
                    </Form.Item>
                    <Form.Item name="store" label="Store">
                        <Select value={store} onChange={(value) => setStore(value)} style={{ width: "100%" }}>
                            {storesFromBlade.map((store, index) => (
                                <Option key={index} value={store.id}>
                                    {store.name}
                                </Option>
                            ))}
                        </Select>
                    </Form.Item>
                    <Form.Item name="primaryColor" label="Primary Color">
                        <Input
                            value={primaryColor}
                            onChange={(e) => setPrimaryColor(e.target.value)}
                            addonAfter={<ColorPicker value={primaryColor} onChange={(color) => setPrimaryColor(color.toHexString())} />}
                        />
                    </Form.Item>
                    <Form.Item label="Logo">
                        <Upload.Dragger name="file" beforeUpload={() => false} onChange={handleFileChange} fileList={file ? [file] : []}>
                            <p className="ant-upload-drag-icon">
                                <UploadOutlined />
                            </p>
                            <p>Drag and Drop your file here or Click to Browse</p>
                        </Upload.Dragger>
                        {filePreview && <img src={filePreview} alt="Logo Preview" style={{ width: "200px", marginTop: "16px" }} />}
                    </Form.Item>
                    <div style={{ display: "flex", justifyContent: "flex-end" }}>
                        <Button onClick={handleCloseFormModal} style={{ marginRight: "8px" }}>
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleSubmit} loading={loading} disabled={loading}>
                            Save
                        </Button>
                    </div>
                </Form>
            </Modal>

            <Modal
                title="Success"
                visible={isModalVisible}
                onCancel={handleModalClose}
                footer={[
                    <Button key="copy" icon={<CopyOutlined />} onClick={() => handleCopy(url)}>
                        Copy URL
                    </Button>,
                    <Button key="ok" type="primary" onClick={handleModalClose}>
                        OK
                    </Button>,
                ]}
            >
                <p>URL: {url}</p>
            </Modal>
        </div>
    );
};

export default BrandPortal;

const rootElement = document.getElementById("brand-portal");
ReactDOM.createRoot(rootElement).render(<BrandPortal />);
