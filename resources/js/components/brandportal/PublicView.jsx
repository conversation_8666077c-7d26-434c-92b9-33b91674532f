import React, { useState, useEffect } from "react";
import { Layout, Typography } from "antd";
import styled from "styled-components";
import ReactDOM from "react-dom/client";
import axios from "axios";
import "bootstrap/dist/css/bootstrap.min.css";
import ListingTableForBrandPortal from "../productListing/ListingTableForBrandPortal";

const PublicView = () => {
    const [portals, setPortals] = useState(null);
    const [templates, setTemplates] = useState(null);
    const [token, setToken] = useState(null);
    const [organizationId, setOrganizationId] = useState(null);
    const [csvKey, setCsvKey] = useState(null);

    useEffect(() => {
        const urlPath = window.location.pathname;
        const portalId = urlPath.split("/show/")[1];
        setCsvKey(portalId);

        const brandPortalDiv = document.getElementById("brand-public");
        const portals = JSON.parse(brandPortalDiv.dataset.portals);
        const templates = JSON.parse(brandPortalDiv.dataset.templates);
        const token = JSON.parse(brandPortalDiv.dataset.token);

        setToken(token);
        setTemplates(templates);
        setPortals(portals);
        setOrganizationId(portals?.organization.id);
    }, []);

    // console.log("templates in public:", portals?.organization.id);

    const { Header } = Layout;
    const { Title, Text } = Typography;

    // Function to create a slightly lighter shade of the header color
    const lightenColor = (color, percent) => {
        const num = parseInt(color.replace("#", ""), 16),
            amt = Math.round(2.55 * percent),
            R = (num >> 16) + amt,
            G = ((num >> 8) & 0x00ff) + amt,
            B = (num & 0x0000ff) + amt;
        return (
            "#" +
            (
                0x1000000 +
                (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
                (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
                (B < 255 ? (B < 1 ? 0 : B) : 255)
            )
                .toString(16)
                .slice(1)
        );
    };

    // Function to determine if color is light or dark
    const isColorDark = (color) => {
        const c = color.substring(1); // Remove #
        const rgb = parseInt(c, 16); // Convert hex to integer
        const r = (rgb >> 16) & 0xff;
        const g = (rgb >> 8) & 0xff;
        const b = (rgb >> 0) & 0xff;
        const brightness = (r * 299 + g * 587 + b * 114) / 1000;
        return brightness < 128;
    };

    const headerBackgroundColor = portals?.primary_color || "#ffe0a6";
    const accentBackgroundColor = lightenColor(headerBackgroundColor, 15);

    // Set text color based on background brightness
    const textColor = isColorDark(headerBackgroundColor) ? "white" : "black";

    const StyledHeader = styled(Header)`
        background-color: ${headerBackgroundColor};
        padding: 20px;
        height: 200px;
        align-items: center;
        display: flex;
        width: 100%;
    `;

    const InnerContainer = styled.div`
        background-color: ${accentBackgroundColor};
        border-radius: 15px;
        padding: 20px;
        display: flex;
        align-items: center;
        width: 100%;
        height: 120px;
    `;

    const LogoContainer = styled.div`
        width: 260px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
    `;

    const Logo = styled.img`
        width: 120px;
        height: auto;
    `;

    const StoreDetails = styled.div`
        display: flex;
        flex-direction: column;
    `;

    const StoreName = styled(Title)`
        color: ${textColor};
        text-transform: capitalize;
        margin: 0;
        font-size: 20px;
    `;

    const SubText = styled(Text)`
        color: ${textColor};
        font-size: 14px;
    `;

    return (
        <div style={{ marginLeft: "0rem" }}>
            <StyledHeader>
                <LogoContainer>
                    <Logo src={portals?.logo_url} alt="Logo" />
                </LogoContainer>
                <InnerContainer>
                    <StoreDetails>
                        <StoreName level={3}>{portals?.name}</StoreName>
                        {/* <SubText>All store</SubText> */}
                    </StoreDetails>
                </InnerContainer>
            </StyledHeader>
            <br />
            <div className="p-3">
                <ListingTableForBrandPortal csvKey={csvKey} templates={templates} token={token} organizationId={organizationId} />
            </div>
        </div>
    );
};

export default PublicView;

const rootElement = document.getElementById("brand-public");
ReactDOM.createRoot(rootElement).render(<PublicView />);
