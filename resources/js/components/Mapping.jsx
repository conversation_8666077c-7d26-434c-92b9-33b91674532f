import { useState, useEffect } from "react";
import ReactDOM from "react-dom/client";
import ImportProducts from "./ImportProduct";
import ExportProducts from "./ExportProduct";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
function Mapping({ data }) {
  const [resetClicked, setResetClicked] = useState(false);
  const [ToggleState, setToggleState] = useState(1);
  const [importExport, setImportExport] = useState(
    (data &&
      data.data_required &&
      data.data_required.template_method_type === "import") ||
      false
  );
  const getActiveClass = (index, className) =>
    ToggleState === index ? className : "";
  const toggleTab = (index) => {
    setToggleState(index);
  };

  useEffect(() => {
    if (data && data.data_required) {
      if (data.data_required.template_method_type == "import") {
        setImportExport(true);
      } else {
        setImportExport(false);
        $("#handle_warning").addClass("hidden");
        $("#pro_imp_btn").removeAttr("disabled");
      }
    }
  }, []);
  return (
    <div className="import_tabs">
      <ToastContainer
        position="top-right"
        autoClose={2000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />

      <div className="content-container">
        <div className={`content ${getActiveClass(1, "active-content")}`}>
          {importExport ? (
            <ImportProducts
              data={data}
              resetClicked={resetClicked}
              onResetComplete={() => setResetClicked(false)}
            />
          ) : (
            <ExportProducts
              data={data}
              resetClicked={resetClicked}
              onResetComplete={() => setResetClicked(false)}
            />
          )}
        </div>
        {/* <div className={`content ${getActiveClass(2, "active-content")}`}>
          <VariantsTab data={data} updateVariantsData={updateVariantsData} />
        </div> */}
      </div>
    </div>
  );
}

export default Mapping;

const rootElement = document.getElementById("importmapping");

ReactDOM.createRoot(rootElement).render(<Mapping data={data} />);
