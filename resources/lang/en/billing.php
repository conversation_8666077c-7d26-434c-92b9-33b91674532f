<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Billing Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used during billing for various
    | messages that we need to display to the user. You are free to modify
    | these language lines according to your application's requirements.
    |
    */

    'page_title' => 'Subscriptions',
    'page_description' => 'Manage your billings and subscriptions.',

    /*when user have signed up manually and have subscribed to a plan*/
    'current_plan' => 'Current Plan',
    'free_plan' => 'Free',
    'lifetime_subscription' => ':time Subscription',
    'upgrade_plan_btn' => 'Upgrade Plan',
    'subscription_details' => 'View Subscription',

    'payment_method' => 'Payment Method',
    'default_card' => 'Default Card: ',
    'empty' => 'N/A',

    'accepted_card_types' => 'We accept Visa, Mastercard, American Express payment methods.',

    'total_store' => 'Stores: :no_of_channels',
    'unlimited_languages' => 'Languages: Unlimited',

    //Summary
    'breakdown_summary' => 'Current Plan Breakdown Summary:',

    'total_SKU' => 'SKUS: :no_of_products',
    'vendors' => 'Vendors: :no_of_vendors',
    'charges_per_vendor' => 'Vendors: $10 will be charge extra for each vendor.',
    'retailers' => 'Retailers: :no_of_retailers',
    'charges_per_retailer' => 'Retailers: $10 will be charge extra for each retailer.',

    'integrations' => 'Integrations: ',
    'total_price' => 'Total ',

    //plans

    'plan_page_title' => 'Plans',
    'plan_page_description' => 'You can upgrade to a different pricing plans, and choose add ons.',

    'per_month' => '/mo',
    'billed_annually' => 'Billed annually',
    'or_month_to_month' => '$:price_per_month month-to-month',

    'upgrade_to_yearly_btn' => 'Upgrade to Yearly for $:price_per_year',
    'upgrade_to_monthly_btn' => 'Upgrade to Monthly for $:price_per_month',

    //shopify btns
    'current_plan_btn' => 'Current Plan',
    'shopify_upgrade_to_monthly_btn' => 'Upgrade to Monthly',

    //plan features
    'no_of_skus' => ':no_of_products SKUs',
    'no_of_retailers' => 'Invite upto :no_of_retailers Channel Partners (Retailers/Vendors)',
    'catalogs_included' => '1 store included',
    'additional_catalogs' => '$100 per additional stores per month',
    'brand_portal' => 'Brand Portal',
    'asset_management' => 'Digital Asset Management',
    'no_of_variants' => 'Unlimited Variants, Attributes, Attribute Sets',
    'data_quality' => 'Data Quality Insights',
    'storage' => ':storage GB Storage',
    'language' => '1 Language',
    'multi_language' => 'Multi-Languages Support',
    'currency' => '1 Currency',
    'multi_currency' => 'Multi-Currency Support',
    'single_user' => 'Single User',
    'three_team_members' => '3 Team Members',
    'ten_team_members' => '10 Team Members',

    //Channel connect btns
    'choose_yearly_btn' => 'Choose yearly for :price_per_year',
    'choose_monthly_btn' => 'Choose monthly for :price_per_month',


    //community plan
    'community_plan_no_of_skus' => 'Includes 1000 SKUs',
    'community_plan_catalogs_included' => 'Includes 1 Store',
    'community_plan_allowed_templates' => 'Includes 5 Templates',
    'community_plan_data_importing' => 'Bulk Product Data Importing',
    'community_plan_import_templates' => 'Re-useable CSV Import Templates',
    'community_plan_products_variants' => 'Configurable Products & Variants',
    'community_plan_family_attribute' => 'Customizable Product Families & Attributes',
    'community_plan_onboarding_crm' => 'Supplier Onboarding CRM',
    'community_plan_completeness_score' => 'Product Completeness Score',
    'community_plan_image_optimization' => 'Image Optimization & Insights',
    'community_plan_validation_rules' => 'Attribute Validation Rules',
    'community_plan_seo_validation' => 'SEO Field Validation',
    'community_plan_channel_automation' => 'Shopify Channel Automation',
    'community_plan_export_templates' => 'Re-useable CSV Export Templates',
    'community_plan_retailer_onboarding_crm' => 'Retailer Onboarding CRM',
    'community_plan_support' => 'Email & Live Chat Support',

    //standard plan
    'standard_plan_grow_plan_contents' => 'Includes everything in Grow plus',
    'standard_plan_no_of_skus' => 'Includes 10000 SKUs',
    'standard_plan_no_of_stores' => 'Includes 3 stores',
    'standard_plan_allowed_templates' => 'Includes Unlimited Templates',
    'standard_plan_bulk_editing' => 'Bulk Product Editing',
    'standard_plan_advance_functions' => 'Advanced Enrichment Functions',
    'standard_plan_DAM' => 'Digital Asset Management',
    'standard_plan_team_collaboration' => 'Team Collaboration',
    'standard_plan_product_update_notifications' => 'Supplier Product Updates & Notifications',
    'standard_plan_multi_language_support' => 'Multi Language Support',
    'standard_plan_multi_currency_support' => 'Multi Currency Support',
    'standard_plan_shopify_magento' => 'Shopify & Magento Connectors',
    'standard_plan_onboarding_guide' => 'Guided Onboarding',
    'standard_plan_support' => 'Email & Live Chat Support',

    // plus plan
    'plus_plan_expand_plan_contents' => 'Includes everything in Expand Plan plus',
    'plus_plan_no_of_skus' => 'Includes 1 Million SKUs',
    'plus_plan_no_of_stores' => 'Includes 10 stores',
    'plus_plan_allowed_templates' => 'Includes Unlimited Templates',
    'plus_plan_hierarchies_categories_brands' => 'Hierarchies on Categories & Brands',
    'plus_plan_advance_DAM' => 'Advanced Digital Asset Management',
    'plus_plan_api_access' => 'API Access',
    'plus_plan_account_manager' => 'Dedicated Account Manager',


];
