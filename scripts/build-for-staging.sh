#!/bin/bash

# Build script for staging environment
# This ensures proper environment variables are set during build

echo "=== Building for Staging Environment ==="

# Set environment variables for staging build
export APP_ENV=staging
export NODE_ENV=production
export VITE_APP_ENV=staging

echo "Environment variables set:"
echo "APP_ENV: $APP_ENV"
echo "NODE_ENV: $NODE_ENV"
echo "VITE_APP_ENV: $VITE_APP_ENV"

# Check if vendor files exist
echo ""
echo "=== Checking vendor files ==="
if [ -d "vendor/apimio/mapping-connector-package" ]; then
    echo "✅ Vendor mapping package found"
else
    echo "❌ Vendor mapping package not found"
    echo "Running composer install..."
    composer install --no-dev --optimize-autoloader
fi

# Check if node_modules exist
echo ""
echo "=== Checking node modules ==="
if [ -d "node_modules" ]; then
    echo "✅ Node modules found"
else
    echo "❌ Node modules not found"
    echo "Running npm install..."
    npm install
fi

# Run the build
echo ""
echo "=== Running build ==="
npm run build

echo ""
echo "=== Build completed ==="

# Check if build files were created
if [ -d "public/build" ]; then
    echo "✅ Build files created successfully"
    ls -la public/build/
else
    echo "❌ Build files not found"
    exit 1
fi
