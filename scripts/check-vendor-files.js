#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to check if vendor mapping files exist
 * This helps debug the staging environment issues
 */

const fs = require('fs');
const path = require('path');

const vendorPath = path.join(__dirname, '../vendor/apimio/mapping-connector-package/resources/js/pages');
const localPath = path.join(__dirname, '../packages/apimio/mapping-fields-package/resources/js/pages');

console.log('=== Checking Vendor and Local Mapping Files ===');
console.log('Environment:', process.env.APP_ENV || 'not set');
console.log('Node Environment:', process.env.NODE_ENV || 'not set');

console.log('\n--- Checking Vendor Path ---');
console.log('Vendor path:', vendorPath);
if (fs.existsSync(vendorPath)) {
    console.log('✅ Vendor path exists');
    try {
        const files = fs.readdirSync(vendorPath, { recursive: true });
        console.log('Vendor files:', files);
    } catch (error) {
        console.log('❌ Error reading vendor directory:', error.message);
    }
} else {
    console.log('❌ Vendor path does not exist');
}

console.log('\n--- Checking Local Path ---');
console.log('Local path:', localPath);
if (fs.existsSync(localPath)) {
    console.log('✅ Local path exists');
    try {
        const files = fs.readdirSync(localPath, { recursive: true });
        console.log('Local files:', files);
    } catch (error) {
        console.log('❌ Error reading local directory:', error.message);
    }
} else {
    console.log('❌ Local path does not exist');
}

console.log('\n--- Recommendations ---');
if (!fs.existsSync(vendorPath) && !fs.existsSync(localPath)) {
    console.log('❌ Neither vendor nor local mapping files exist!');
    console.log('   This will cause the staging error.');
    console.log('   Please ensure the mapping package is properly installed.');
} else if (!fs.existsSync(vendorPath)) {
    console.log('⚠️  Vendor files missing but local files exist.');
    console.log('   Make sure to run: composer install --no-dev in staging');
} else {
    console.log('✅ Files appear to be available.');
}
