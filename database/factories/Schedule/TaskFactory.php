<?php

namespace Database\Factories\Schedule;

use App\Models\Organization\Organization;
use App\Models\Schedule\Task;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Task>
 */
class TaskFactory extends Factory
{
    protected $model = Task::class;

    public function definition(): array
    {
        $scheduledAt = Carbon::now()->addMinutes($this->faker->numberBetween(-60, 60)); // Random time past or future

        return [
            'organization_id' => Organization::first()->id, // Generate an associated organization
            'json_payload' => json_encode([
                'price' => $this->faker->randomFloat(2, 10, 100),
                'compare_at_price' => $this->faker->randomFloat(2, 50, 200),
                'filters' => $this->faker->randomElements([1, 2, 3, 4, 5], $this->faker->numberBetween(1, 5)),
            ]),
            'type' => $this->faker->randomElement(['bulk_price_update', 'data_import']),
            'scheduled_at' => $scheduledAt,
            'timezone' => $this->faker->timezone,
            'status' => $this->faker->randomElement(['pending', 'processing', 'completed']),
        ];
    }
}

