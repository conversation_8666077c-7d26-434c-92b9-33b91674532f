<?php

namespace Database\Seeders;

use App\Models\Product\Template;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DefaultTemplateSeeder extends Seeder
{
    //php artisan db:seed --class=DefaultTemplateSeeder

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Template::create(
            [
                'organization_id' => 1 ,
                'version_id' => json_encode("1"),
                'channel_id' => json_encode(["1"]) ,
                'name' => "Default_import" ,
                'type' => "import" ,
                'product_status' => 1,

                //for shopify seeder
//               'payload' => '{"data":[{"from":["Default,Handle"],"with_formula":"assign","to":["Default,handle"]},{"from":["Default,Title"],"with_formula":"assign","to":["General,product_name"]},{"from":["Default,Body (HTML)"],"with_formula":"assign","to":["General,description"]},{"from":["Default,Vendor"],"with_formula":"assign","to":["Default,vendor"]},{"from":["Default,Type"],"with_formula":"assign","to":["Default,categories"]},{"from":["Default,Tags"],"with_formula":"assign","to":["SEO,seo_keyword"]},{"from":["Default,Option1 Name"],"with_formula":"assign","to":["Variant Option,option1_name"]},{"from":["Default,Option1 Value"],"with_formula":"assign","to":["Variant Option,option1_value"]},{"from":["Default,Option2 Name"],"with_formula":"assign","to":["Variant Option,option2_name"]},{"from":["Default,Option2 Value"],"with_formula":"assign","to":["Variant Option,option2_value"]},{"from":["Default,Option3 Name"],"with_formula":"assign","to":["Variant Option,option3_name"]},{"from":["Default,Option3 Value"],"with_formula":"assign","to":["Variant Option,option3_value"]},{"from":["Default,Variant SKU"],"with_formula":"assign","to":["Variant,sku"]},{"from":["Default,Variant Grams"],"with_formula":"assign","to":["Variant,weight"]},{"from":["Default,Variant Price"],"with_formula":"assign","to":["Variant,price"]},{"from":["Default,Variant Compare At Price"],"with_formula":"assign","to":["Variant,compare_at_price"]},{"from":["Default,Cost per item"],"with_formula":"assign","to":["Variant,cost_price"]},{"from":["Default,Variant Barcode"],"with_formula":"assign","to":["Variant,barcode"]},{"from":["Default,Image Src"],"with_formula":"assign","to":["Default,file"]},{"from":["Default,SEO Title"],"with_formula":"assign","to":["SEO,seo_title"]},{"from":["Default,SEO Description"],"with_formula":"assign","to":["SEO,seo_description"]},{"from":["Default,Variant Image"],"with_formula":"assign","to":["Variant,file"]},{"from":["Default,Variant Weight Unit"],"with_formula":"assign","to":["Variant,weight_unit"]}],"variant":[]}' ,
                // loloi seeder
                  'payload' => '{"data":[{"from":["Default,Design"],"with_formula":"assign","to":["Default,handle"]},{"from":["Default,SKU"],"with_formula":"assign","to":["Variant,sku"]},{"from":["Default,UPC"],"with_formula":"assign","to":["Variant,barcode"]},{"from":["Default,Product Name Current"],"with_formula":"assign","to":["Variant,name"]},{"from":["Default,Product Name New"],"with_formula":"assign","to":["SEO,seo_title"]},{"from":["Default,Brand"],"with_formula":"assign","to":["Default,brand"]},{"from":["Default,Main Category"],"with_formula":"assign","to":["Default,categories"]},{"from":["Default,Collection"],"with_formula":"assign","to":["Default,vendor"]},{"from":["Default,Cost"],"with_formula":"assign","to":["Variant,price"]},{"from":["Default,MAP"],"with_formula":"assign","to":["Variant,cost_price"]},{"from":["Default,MSRP"],"with_formula":"assign","to":["Variant,weight"]},{"from":["Default,Creation Date"],"with_formula":"assign","to":["General,description"]},{"from":["Default,Dimensions"],"with_formula":"assign","to":["Variant Option,option1_value"]},{"from":["Default,Shape"],"with_formula":"assign","to":["Variant Option,option2_value"]}],"variant":[]}'
                ]
        );
    }
}
