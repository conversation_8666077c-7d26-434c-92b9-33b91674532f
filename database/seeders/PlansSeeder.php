<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PlansSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        //Free plan for invited users
        DB::table('plans')->insert([
            'stripe_monthly_id' => 0,
            'stripe_yearly_id' => 0,
            'name' => "Free Plan",
            'handle' => "free_plan",
            'price_per_month' => 0,
            'price_per_year' => 0,
            'next_price' => 100,
            'no_of_products' => 500,
            'icon_link' => 'media/billing/community.png',
            'storage' => 2,
            'no_of_catalogue' => 1,
            'no_of_languages' => 1,
            'no_of_currencies' => 0,
            'no_of_retailers' => 10,
            'no_of_vendors' => 10,
            'no_of_team_members' => 1,
            'no_of_channels' => 1,
            'no_of_templates' => 1,
            'is_brand_portal' => true,
            'is_vaf' => true,
            'is_oks' => true,
            'is_trial_available' => true,
            'trial_period' => 14,
        ]);

        //Community Plan
        DB::table('plans')->insert([
            'stripe_monthly_id' => env('STARTUP_MONTHLY_PRICE_ID'),
            'stripe_yearly_id' => env('STARTUP_YEARLY_PRICE_ID'),
            'name' => "Grow Plan",
            'handle' => "community_plan",
            'price_per_month' => 49,
            'price_per_year' => 49,
            'next_price' => 100,
            'no_of_products' => 1000,
            'icon_link' => 'media/billing/community.png',
            'storage' => 2,
            'no_of_catalogue' => 1,
            'no_of_languages' => 1,
            'no_of_currencies' => 1,
            'no_of_retailers' => 10,
            'no_of_vendors' => 10,
            'no_of_team_members' => 0,
            'no_of_channels' => 1,
            'no_of_templates' => 5,
            'is_brand_portal' => true,
            'is_vaf' => true,
            'is_oks' => true,
            'is_trial_available' => false,
            'trial_period' => 0,
        ]);

        //Standard Plan
        DB::table('plans')->insert([
            'stripe_monthly_id' => env('PLUS_MONTHLY_PRICE_ID'),
            'stripe_yearly_id' => env('PLUS_YEARLY_PRICE_ID'),
            'name' => "Expand Plan",
            'handle' => "standard_plan",
            'price_per_month' => 399,
            'price_per_year' => 399,
            'next_price' => 100,
            'no_of_products' => 20000,
            'icon_link' => 'media/billing/starter.png',
            'storage' => 100,
            'no_of_catalogue' => 1,
            'no_of_languages' => 0,
            'no_of_currencies' => 0,
            'no_of_retailers' => 25,
            'no_of_vendors' => 25,
            'no_of_team_members' => 3,
            'no_of_channels' => 3,
            'no_of_templates' => 50,
            'is_brand_portal' => true,
            'is_vaf' => true,
            'is_oks' => true,
            'is_trial_available' => true,
            'trial_period' => 14,
        ]);

        //Plus Plan
        DB::table('plans')->insert([
            'stripe_monthly_id' => env('PRO_MONTHLY_PRICE_ID'),
            'stripe_yearly_id' => env('PRO_YEARLY_PRICE_ID'),
            'name' => "Scale Plan",
            'handle' => "plus_plan",
            'price_per_month' => 999,
            'price_per_year' => 999,
            'next_price' => 100,
            'no_of_products' => 100000,
            'icon_link' => 'media/billing/business.png',
            'storage' => 1000,
            'no_of_catalogue' => 1,
            'no_of_languages' => 0,
            'no_of_currencies' => 0,
            'no_of_retailers' => 100,
            'no_of_vendors' => 100,
            'no_of_team_members' => 10,
            'no_of_channels' => 10,
            'no_of_templates' => 0,
            'is_brand_portal' => true,
            'is_vaf' => true,
            'is_oks' => true,
            'is_trial_available' => true,
            'trial_period' => 14,
        ]);

    }
}
