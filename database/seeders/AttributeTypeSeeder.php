<?php

namespace Database\Seeders;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AttributeTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('attribute_types')->insert([
            'name' => 'single line text',
        ]);

        DB::table('attribute_types')->insert([
            'name' => 'number',
        ]);

        DB::table('attribute_types')->insert([
            'name' => 'multi line text',
        ]);

        DB::table('attribute_types')->insert([
            'name' => 'multiselect',
        ]);

        DB::table('attribute_types')->insert([
            'name' => 'date and time',
        ]);
        DB::table('attribute_types')->insert([
            'name' => 'file',
        ]);
        DB::table('attribute_types')->insert([
            'name' => 'measurement',
        ]);
        DB::table('attribute_types')->insert([
            'name' => 'rating',
        ]);
        DB::table('attribute_types')->insert([
            'name' => 'json',
        ]);
        DB::table('attribute_types')->insert([
            'name' => 'true or false',
        ]);
        DB::table('attribute_types')->insert([
            'name' => 'url',
        ]);
        DB::table('attribute_types')->insert([
            'name' => 'color',
        ]);
        DB::table('attribute_types')->insert([
            'name' => 'variants',
        ]);


    }
}
