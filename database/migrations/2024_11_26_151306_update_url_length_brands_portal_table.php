<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('brands_portals', function (Blueprint $table) {
            // Drop the old column
            $table->dropColumn('url');
        });

        Schema::table('brands_portals', function (Blueprint $table) {
            // Create the new column with the desired type
            $table->longText('url')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the longText column
        Schema::table('brands_portals', function (Blueprint $table) {
            $table->dropColumn('url');
        });

        // Recreate the original column
        Schema::table('brands_portals', function (Blueprint $table) {
            $table->string('url', 255)->unique()->nullable();
        });
    }
};
