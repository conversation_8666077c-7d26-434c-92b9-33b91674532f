<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateChannelsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('channels', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organization_id')->nullable()->constrained('organizations')->onDelete('cascade');
            $table->string("name");
            $table->string("product_update",100)->nullable();
            $table->integer("inventory")->nullable();
            $table->enum("export_status",['publish','draft','archive'])->nullable();
            $table->boolean("category_empty")->default(0)->comment('yes=>1, no=>0');
            $table->enum("type", ["shopify", "magento", "woocommerce"]);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('channels');
    }
}
