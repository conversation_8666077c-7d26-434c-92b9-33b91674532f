<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Product\AttributeType;
class EditAttributeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (env('APP_ENV') == 'production') {
            AttributeType::where('id',1)->update(['name'=> 'single line text']);
            AttributeType::where('id',2)->update(['name'=> 'number']);
            AttributeType::where('id',3)->update(['name'=> 'multi line text']);

            $attribute_array = [
                ['name' => 'date and time'],
                ['name' => 'file'],
                ['name' => 'measurement'],
                ['name' => 'rating'],
                ['name' => 'json'],
                ['name' => 'true or false'],
                ['name' => 'url']
            ];
            \App\Models\Product\AttributeType::insert($attribute_array);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
