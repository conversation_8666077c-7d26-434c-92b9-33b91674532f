<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('brands_portals', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('url', 255)->unique()->nullable();
            $table->string('primary_color')->nullable();
            $table->enum("status",['inactive','active'])->nullable();
            $table->string('logo_url')->nullable();
            $table->foreignId('organization_id')->constrained('organizations')->onDelete('cascade');
            $table->foreignId('file_id')->constrained('files')->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();

        });

        Schema::create('brands_portal_channel', function (Blueprint $table) {
            $table->id();
            $table->foreignId('brands_portal_id')->constrained('brands_portals')->onDelete('cascade');
            $table->foreignId('channel_id')->constrained('channels')->onDelete('cascade');
            $table->timestamps();
        });

        Schema::create('brands_portal_template', function (Blueprint $table) {
            $table->id();
            $table->foreignId('brands_portal_id')->constrained('brands_portals')->onDelete('cascade');
            $table->foreignId('template_id')->constrained('templates')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('brands_portals');
        Schema::dropIfExists('brands_portal_channel');
        Schema::dropIfExists('brands_portal_template');
    }
};
