{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "ext-json": "*", "ext-zip": "*", "apimio/completeness": "4.0.17", "apimio/gallery": "5.0.24", "apimio/mapping-connector-package": "8.1", "barryvdh/laravel-translation-manager": "^0.6.3", "bref/bref": "^2.3", "bref/laravel-bridge": "^2.4", "doctrine/dbal": "^3.0", "ghazniali95/react-with-laravel-blade": "^1.0", "ghazniali95/shopifyconnector": "dev-main", "guzzlehttp/guzzle": "^7.3", "inertiajs/inertia-laravel": "^1.3", "josiasmontag/laravel-recaptchav3": "^1.0", "laravel/cashier": "^15.3", "laravel/framework": "^10.0", "laravel/pulse": "^1.2", "laravel/sanctum": "^3.3", "laravel/slack-notification-channel": "^2.3", "laravel/socialite": "^5.2", "laravel/tinker": "^2.8", "laravel/ui": "^4.0", "league/flysystem-aws-s3-v3": "^3.10", "maatwebsite/excel": "^3.1", "openai-php/laravel": "^0.10.1", "phpoffice/phpspreadsheet": "^1.18", "predis/predis": "^1.1", "pusher/pusher-php-server": "^7.2", "rap2hpoutre/laravel-log-viewer": "^2.3", "stripe/stripe-php": "^13.0", "tightenco/ziggy": "^2.5"}, "repositories": [{"type": "git", "url": "https://<EMAIL>/apimio/mapping-fields-package.git"}, {"type": "git", "url": "https://<EMAIL>/apimio/completeness.git"}, {"type": "git", "url": "https://<EMAIL>/apimio/gallery.git"}], "require-dev": {"barryvdh/laravel-debugbar": "^3.8", "fakerphp/faker": "^1.9.1", "laravel/telescope": "^4.10", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^9.0", "spatie/laravel-ignition": "^2.0", "laravel/pint": "^1.0", "laravel/sail": "^1.18"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "php-http/discovery": false}}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Ghazniali95\\ShopifyConnector\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "Apimio\\Completeness\\": "packages/apimio/completeness/src", "Apimio\\MappingConnectorPackage\\": "packages/apimio/mapping-fields-package/src/", "Apimio\\Gallery\\": "packages/apimio/gallery/src/"}, "files": ["app/Helpers/helper.php"]}, "minimum-stability": "stable", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan vendor:publish --tag=public", "@php artisan ziggy:generate"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}