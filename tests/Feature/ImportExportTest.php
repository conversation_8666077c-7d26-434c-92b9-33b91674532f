<?php

namespace Tests\Feature;

use App\Models\Product\Attribute;
use App\Models\Product\Family;
use App\Models\Product\Product;
use Illuminate\Http\UploadedFile;
use Tests\TestCase;

/**
 * @test php vendor/bin/phpunit tests/Feature/ImportExportTest.php
 * */
class ImportExportTest extends TestCase
{

    private $test_data_1;
    private $test_data_2;
    private $test_data_3;
    private $test_data_4;
    private $test_data_5;

    public function setUp(): void
    {
        parent::setUp();
        $this->setup_account();

        $this->test_data_1 = array (
            'file_path' => public_path("test_data/test_sheet_1.csv"),
            'nodes' =>
                array (
                    'data' =>
                        array (
                            1 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Rug ID',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'sku',
                                        ),
                                ),
                            2 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => NULL,
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => NULL,
                                        ),
                                ),
                            3 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => NULL,
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => NULL,
                                        ),
                                ),
                            4 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => NULL,
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => NULL,
                                        ),
                                ),
                            0 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => NULL,
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'categories',
                                        ),
                                ),
                        ),
                    'variant' =>
                        array (
                            'id' => NULL,
                            'variant_options' =>
                                array (
                                    0 => NULL,
                                    1 => NULL,
                                    2 => NULL,
                                ),
                        ),
                ),
            'version' => '1',
            'status' => '1',
            'catalog' => '1',
            'temp_name' => NULL,
            'temp_id' => NULL,
        );
        $this->test_data_2 = array (
            'file_path' => public_path("test_data/test_sheet_1.csv"),
            'nodes' =>
                array (
                    'data' =>
                        array (
                            1 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Rug ID',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'sku',
                                        ),
                                ),
                            2 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Product Name Current',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'product_name',
                                        ),
                                ),
                            3 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Product Name New',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'product_name',
                                        ),
                                ),
                            4 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Brand',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'brand',
                                        ),
                                ),
                            5 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Cost',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'price',
                                        ),
                                ),
                            6 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'MSRP',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'compare_at_price',
                                        ),
                                ),
                            7 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Dimensions',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'weight',
                                        ),
                                ),
                            8 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Creation Date',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'description',
                                        ),
                                ),
                            9 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Images',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'file',
                                        ),
                                ),
                            10 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Image Name',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'vendor',
                                        ),
                                ),
                            11 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Dimensions',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'seo_description',
                                        ),
                                ),
                            12 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Fill Insert',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'weight',
                                        ),
                                ),
                            0 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Collection',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'categories',
                                        ),
                                ),
                        ),
                    'variant' =>
                        array (
                            'id' => NULL,
                            'variant_options' =>
                                array (
                                    0 => NULL,
                                    1 => NULL,
                                    2 => NULL,
                                ),
                        ),
                ),
            'version' => '1',
            'status' => '0',
            'catalog' => '1',
            'temp_name' => NULL,
            'temp_id' => NULL,
        );
        $this->test_data_3 = array (
            'file_path' => public_path("test_data/test_sheet_1.csv"),
            'nodes' =>
                array (
                    'data' =>
                        array (
                            1 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'SKU',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'sku',
                                        ),
                                ),
                            2 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Product Name Current',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'product_name',
                                        ),
                                ),
                            3 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'UPC',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'upc_barcode',
                                        ),
                                ),
                            4 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'MAP',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'compare_at_price',
                                        ),
                                ),
                            5 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'MSRP',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'cost_price',
                                        ),
                                ),
                            6 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Pile Height',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'weight',
                                        ),
                                ),
                            7 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Primary Material',
                                            1 => 'Secondary Material',
                                        ),
                                    'with_formula' => 'merge',
                                    'with' => NULL,
                                    'to' =>
                                        array (
                                            0 => 'description',
                                        ),
                                ),
                            8 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Product Name New',
                                        ),
                                    'with_formula' => 'slug',
                                    'to' =>
                                        array (
                                            0 => 'seo_title',
                                        ),
                                ),
                            9 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'RD Secondary Color',
                                        ),
                                    'with_formula' => 'split',
                                    'with' => 'x',
                                    'to' =>
                                        array (
                                            0 => NULL,
                                            1 => 'seo_url',
                                        ),
                                ),
                            10 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Images',
                                        ),
                                    'with_formula' => 'split',
                                    'with' => ',',
                                    'to' =>
                                        array (
                                            0 => 'file',
                                            1 => 'file',
                                        ),
                                ),
                            11 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Shape',
                                        ),
                                    'with_formula' => 'replace',
                                    'replace' => 'Sample',
                                    'with' => 'RECTANGLE',
                                    'to' =>
                                        array (
                                            0 => 'vendor',
                                        ),
                                ),
                            12 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => NULL,
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'brand',
                                        ),
                                ),
                            0 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Main Category',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'categories',
                                        ),
                                ),
                        ),
                    'variant' =>
                        array (
                            'id' => NULL,
                            'variant_options' =>
                                array (
                                    0 => NULL,
                                    1 => NULL,
                                    2 => NULL,
                                ),
                        ),
                ),
            'version' => '1',
            'status' => '1',
            'catalog' => '1',
            'temp_name' => NULL,
            'temp_id' => NULL,
        );

        $this->test_data_4 = array (
            'file_path' => public_path("test_data/test_sheet_1.csv"),
            'nodes' =>
                array (
                    'data' =>
                        array (
                            1 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Rug ID',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'sku',
                                        ),
                                ),
                            2 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Product Name Current',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'product_name',
                                        ),
                                ),
                            3 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'UPC',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'upc_barcode',
                                        ),
                                ),
                            4 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'MAP',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'compare_at_price',
                                        ),
                                ),
                            5 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'MSRP',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'cost_price',
                                        ),
                                ),
                            6 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Pile Height',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'weight',
                                        ),
                                ),
                            7 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Primary Material',
                                            1 => 'Secondary Material',
                                        ),
                                    'with_formula' => 'merge',
                                    'with' => NULL,
                                    'to' =>
                                        array (
                                            0 => 'description',
                                        ),
                                ),
                            8 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Product Name New',
                                        ),
                                    'with_formula' => 'slug',
                                    'to' =>
                                        array (
                                            0 => 'seo_title',
                                        ),
                                ),
                            9 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'RD Secondary Color',
                                        ),
                                    'with_formula' => 'split',
                                    'with' => 'x',
                                    'to' =>
                                        array (
                                            0 => NULL,
                                            1 => 'seo_url',
                                        ),
                                ),
                            10 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Images',
                                        ),
                                    'with_formula' => 'split',
                                    'with' => ',',
                                    'to' =>
                                        array (
                                            0 => 'file',
                                            1 => 'file',
                                        ),
                                ),
                            11 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Shape',
                                        ),
                                    'with_formula' => 'replace',
                                    'replace' => 'Sample',
                                    'with' => 'RECTANGLE',
                                    'to' =>
                                        array (
                                            0 => 'vendor',
                                        ),
                                ),
                            12 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => NULL,
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'brand',
                                        ),
                                ),
                            0 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Main Category',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'categories',
                                        ),
                                ),
                        ),
                    'variant' =>
                        array (
                            'id' => NULL,
                            'variant_options' =>
                                array (
                                    0 => NULL,
                                    1 => NULL,
                                    2 => NULL,
                                ),
                        ),
                ),
            'version' => '1',
            'status' => '1',
            'catalog' => '1',
            'temp_name' => NULL,
            'temp_id' => NULL,
        );
        $this->test_data_5 = array (
            'file_path' => public_path("test_data/test_sheet_1.csv"),
            'nodes' =>
                array (
                    'data' =>
                        array (
                            1 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Rug ID',
                                        ),
                                    'with_formula' => 'assign',
                                    'to' =>
                                        array (
                                            0 => 'sku',
                                        ),
                                ),
                            2 =>
                                array (
                                    'from' =>
                                        array (
                                            0 => 'Rug ID',
                                            1 => 'UPC',
                                        ),
                                    'with_formula' => 'merge',
                                    'with' => NULL,
                                    'to' =>
                                        array (
                                            0 => 'description',
                                        ),
                                ),
                        ),
                    'variant' =>
                        array (
                            'id' => NULL,
                            'variant_options' =>
                                array (
                                    0 => NULL,
                                    1 => NULL,
                                    2 => NULL,
                                ),
                        ),
                ),
            'version' => '1',
            'status' => '1',
            'catalog' => '1',
            'temp_name' => NULL,
            'temp_id' => NULL,
        );
    }


    /**
     * Test step 1 for import fake file.
     *
     * @return void
     */
    public function test_case_1_import_empty_file()
    {
        $inputs = [
            'csv_file' =>
                UploadedFile::
                fake()->
                create(
                    'testCSV.csv'
                )
        ];

        $this->from("product/import/step1")->post("product/import/step2", [
            "file" => $inputs['csv_file']
        ])->assertRedirect("product/import/step1");
    }

//    public function test_case_2_import_file() {
//        $this->from("product/import/step3")->post("product/import", $this->test_data_1)->assertRedirect("products");
//        $this->assertEquals("99", Product::count());
//
//        $this->from("product/import/step3")->post("product/import", $this->test_data_1)->assertRedirect("products");
//        $this->assertEquals("0", Product::count());
//    }

    public function test_case_3_import_file() {
        $this->from("product/import/step3")->post("product/import", $this->test_data_2)->assertRedirect("products");
        $this->assertEquals("0", Product::count());
    }

//    public function test_case_4_import_file() {
//        $family  = factory(Family::class)->create();
//
//        for($i=0 ; $i<5; $i++) {
//            $attribute = factory(Attribute::class)->create();
//            $family->attributes()->attach($attribute->id);
//        }
//
//        $this->from("product/import/step3")->post("product/import", $this->test_data_3)->assertRedirect("products");
//
//        $this->assertEquals("0", Product::count());
//    }

    public function test_case_5_import_file() {
        $family  = factory(Family::class)->create();

        for($i=0 ; $i<5; $i++) {
            $attribute = factory(Attribute::class)->create();
            $family->attributes()->attach($attribute->id);
        }

        $this->from("product/import/step3")->post("product/import", $this->test_data_4)->assertRedirect("products");
        $this->assertEquals("0", Product::count());
    }

//    public function test_case_6_merge_formula() {
//        $this->from("product/import/step3")->post("product/import", $this->test_data_5)->assertRedirect("products");
//        $this->assertEquals("99", Product::count());
//    }

}
