<?php

namespace Tests\Feature;

use App\Models\Channel\ShopifyChannel;
use App\Models\Organization\Organization;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Tests\TestCase;

class LimitTraitTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function testCase1()
    {
        $org = factory(Organization::class)->create();
        $prod = factory(Product::class)->create();
        $variant = new Variant([
            "product_id" => $prod->id,
            "name" => "size",
            "option" => "green",
        ]);
        $variant->save();
        $this->assertIsInt($org->as_subscription()->limit_remaining_products());
    }

    public function testCase2() {
        $org = factory(Organization::class)->create();
        $this->assertIsInt($org->as_subscription()->limit_remaining_catalog());
    }

    public function testCase3() {
        $org = factory(Organization::class)->create();
        $user = factory(User::class)->create();
        $prod = factory(Product::class)->create();

        DB::table('subscriptions')->insert([
            'name' => 'Community Plan',
            'stripe_id' => '234f3f4r434',
            'stripe_status' => 'active',
            'stripe_price' => 'price_1KKK4WJKtHOhwx4o05w8Eq4u',
            'quantity' => 1,
            'organization_id' => $org->id,
        ]);

        $variant = new Variant([
            "product_id" => $prod->id,
            "name" => "size",
            "option" => "red"
        ]);
        $variant->save();
        $this->assertIsInt($org->as_subscription()->limit_remaining_products());
    }

    public function testCase4(){
        $user           = factory(User::class)->create();
        $org   = factory(Organization::class)->create();
        $channel        = factory(ShopifyChannel::class)->create();
        $prod = factory(Product::class)->create();
        $org->users()->attach($user->id);

        DB::table('shopify_subscriptions')->insert([
            'name' => 'Community Plan',
            'shopify_channel_id' => $channel->id,
            'recurring_application_charge_id' => Str::random(10),
            'price' => '900',
            'status' => "active",
            'organization_id' => $org->id,
            "test" => 1,
            "trial_days" => 0,
            "capped_amount" => 100,
            "balance_used" => 0,
            "balance_remaining" => 100,
            'plan_id' => 1
        ]);

        $this->assertIsInt($org->as_subscription()->limit_remaining_products());
    }
}
