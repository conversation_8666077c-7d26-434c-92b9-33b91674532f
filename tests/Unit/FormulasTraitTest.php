<?php

namespace Tests\Unit;

use App\Classes\ImportExport;
use PHPUnit\Framework\TestCase;

/**
 * Formulas trait used to apply formulas.
 *
 * @test php vendor/bin/phpunit tests/Unit/FormulasTraitTest.php
 * */
class FormulasTraitTest extends TestCase
{
    /**
     * test assign formula.
     *
     * @return void
     */
    public function testCase1()
    {
        $import = new ImportExport();
        $data = $import->from([
            [
                "key" => "sku-id",
                "value" => "123"
            ]
        ])
            ->withFormula("assign")
            ->to(["sku"]);

        $this->assertTrue(isset($data["sku"]));

        $data = $import->from([
            [
                "key" => "sku-id",
                "value" => "123"
            ],
            [
                "key" => "product title",
                "value" => "Fresh banana"
            ]
        ])
            ->withFormula("assign")
            ->to(["sku", "product_title"]);

        $this->assertTrue(isset($data["sku"]));
        $this->assertTrue(isset($data["product_title"]));

        $import->to(["sku", "product_title"]);
    }

    public function testCase2() {
        $import = new ImportExport();
        $data = $import->from([[
                "key" => "sku-id",
                "value" => "123"
            ]])
            ->withFormula("split")
            ->with("-")->to(["first", "second"]);

        $this->assertTrue(isset($data["first"]));
        $this->assertEquals("123", $data["first"]);
        $this->assertEmpty($data["second"]);

        $data = $import->from([[
                "key" => "sku-id",
                "value" => "123-321"
            ]])
            ->withFormula("split")
            ->with("-")
            ->to(["first", "second"]);

        $this->assertEquals("123", $data["first"]);
        $this->assertEquals("321", $data["second"]);
    }

    public function testCase3() {
        $import = new ImportExport();
        $data = $import->from([
            [
                "key" => "sku-id",
                "value" => "123-321"
            ],
            [
                "key" => "handle",
                "value" => "123-321"
            ]
        ])
            ->withFormula("merge")
            ->with("-")
            ->to(["first"]);

        $this->assertTrue(isset($data["first"]));
        $this->assertEquals("123-321-123-321", $data["first"]);
    }

    public function testCase4() {
        $import = new ImportExport();
        $data = $import->from([[
                "key" => "price",
                "value" => "$1"
            ]])
            ->withFormula("replace")
            ->remove("$")
            ->with("")
            ->to(["cost_price"]);

        $this->assertTrue(isset($data["cost_price"]));
        $this->assertEquals("1", $data["cost_price"]);
    }

    public function testCase5() {
        $import = new ImportExport();
        $data = $import->from([[
            "key" => "product title",
            "value" => "hello world"
        ]])
            ->withFormula("slug")
            ->to(["handle"]);

        $this->assertTrue(isset($data["handle"]));
        $this->assertEquals("hello-world", $data["handle"]);
    }
}
