<?php
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\StreamedResponse;

/* Import Export routes */
Route::prefix('products')->namespace('Product')->middleware(['check_billing'])->group(function () {
    //import route
    Route::get('/import/step1', 'ImportController@import_step1')->name('import.csv.step1');
    Route::post('/import/step2', 'ImportController@import_step2')->name('import.csv.step2');
    Route::get('/import/step2', 'ImportController@import_step2')->name('import.csv.step2');
    Route::get('/import/create-template', 'ImportController@create_template')->name('import.create.template');
    Route::get('/import/apply-template', 'ImportController@apply_template')->name('import.apply.template');
    Route::post('import/save-template','ImportController@save_template')->name('import.save.template');
    Route::delete('/import/delete-template', 'ImportController@delete_template')->name('import.delete.template');
    // Alternative route for delete template using POST with _method=DELETE
    Route::post('/import/delete-template', 'ImportController@delete_template')->name('import.delete.template.post');
    Route::post('/import/step3', 'ImportController@import_step3')->name('import.csv.step3');
    Route::post('/import', 'ImportController@import_csv')->name("products.import");



    //Export route
    Route::get('/export/step1', 'ExportController@export_step1')->name('export.exportOne');
    Route::post('/export/step2', 'ExportController@export_step2')->name('export.exportTwo');
    Route::post('/export/step3', 'ExportController@export_step3')->name('export.exportThree');
    Route::post('/export', 'ExportController@export_csv')->name("products.export")->middleware('prime.user');

});

Route::get('products/download/', function (Request $request) {
    $filename = $request->query('filename');
    $fileUrl = Storage::disk('s3')->url($filename);

//        return new StreamedResponse(function() use ($fileUrl) {
//            echo file_get_contents($fileUrl);
//        }, 200, [
//            'Content-Type' => 'application/octet-stream',
//            'Content-Disposition' => 'attachment; filename="' . basename($fileUrl) . '"',
//        ]);
    $download = [
        'file' => $fileUrl,
        'name' => basename($fileUrl),
        'details' => "Your file is ready for download. We've successfully generated your export, and you can now access it by clicking the button below."
    ];

    return view('download', compact('download'));
})->name('download');












////////////////import export all test routes below/////////////////////

Route::prefix('products')->namespace('Product')->middleware(['check_billing'])->group(function () {
    // testing routes for get method
    Route::get('/import', 'ProductController@import_csv')->name("products.import");


    // testing routes for get method
    Route::get('/export-csv', 'ProductController@export_csv')->name("export.csv");

});
